<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Test - Huku<PERSON>ge Özetleme</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body class="profile-body">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-balance-scale nav-icon"></i>
                <span>Hukuki Belge Özetleme</span>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">Ana Sayfa</a>
                <button class="nav-btn" id="user-menu-btn">
                    <i class="fas fa-user"></i>
                    <span id="user-name">Test Kullanıcı</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Profile Content -->
    <div class="profile-container">
        <div class="profile-card">
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h1>Kullanıcı Profili</h1>
                <p>Hesap bilgilerinizi görüntüleyin ve yönetin</p>
            </div>

            <div class="profile-content">
                <!-- Kişisel Bilgiler -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3>
                            <i class="fas fa-user"></i>
                            Kişisel Bilgiler
                        </h3>
                    </div>
                    <div class="profile-info">
                        <div class="info-row">
                            <div class="info-item">
                                <label>Ad:</label>
                                <span id="profile-first-name">Test</span>
                            </div>
                            <div class="info-item">
                                <label>Soyad:</label>
                                <span id="profile-last-name">Kullanıcı</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <label>E-posta:</label>
                                <span id="profile-email"><EMAIL></span>
                            </div>
                            <div class="info-item">
                                <label>Telefon:</label>
                                <span id="profile-phone">0555 123 4567</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <label>Meslek/Ünvan:</label>
                                <span id="profile-profession">Avukat</span>
                            </div>
                            <div class="info-item">
                                <label>Kayıt Tarihi:</label>
                                <span id="profile-created-at">01.01.2024</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hesap Durumu -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3>
                            <i class="fas fa-shield-check"></i>
                            Hesap Durumu
                        </h3>
                    </div>
                    <div class="profile-info">
                        <div class="info-row">
                            <div class="info-item">
                                <label>Durum:</label>
                                <span id="profile-status" class="status-badge active">Aktif</span>
                            </div>
                            <div class="info-item">
                                <label>Son Giriş:</label>
                                <span id="profile-last-login">Bugün</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Kullanım Amacı -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3>
                            <i class="fas fa-comment"></i>
                            Kullanım Amacı
                        </h3>
                    </div>
                    <div class="profile-info">
                        <div class="usage-reason">
                            <p id="profile-reason">Hukuki belgelerin hızlı özetlenmesi ve analizi için kullanmayı planlıyorum.</p>
                        </div>
                    </div>
                </div>

                <!-- Profil İşlemleri -->
                <div class="profile-section">
                    <div class="section-header">
                        <h3>
                            <i class="fas fa-cog"></i>
                            Hesap İşlemleri
                        </h3>
                    </div>
                    <div class="profile-actions">
                        <button class="profile-btn secondary" onclick="goHome()">
                            <i class="fas fa-home"></i>
                            Ana Sayfaya Dön
                        </button>
                        <button class="profile-btn danger" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Çıkış Yap
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Menu Dropdown -->
    <div class="user-menu-dropdown" id="user-menu-dropdown" style="display: none;">
        <div class="user-menu-item" onclick="goToProfile()">
            <i class="fas fa-user"></i>
            <span>Profil</span>
        </div>
        <div class="user-menu-divider"></div>
        <div class="user-menu-item" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            <span>Çıkış Yap</span>
        </div>
    </div>

    <script>
        // Basit test fonksiyonları
        function goHome() {
            window.location.href = '/';
        }
        
        function logout() {
            if (confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
                localStorage.removeItem('userToken');
                localStorage.removeItem('userData');
                window.location.href = '/';
            }
        }
        
        function goToProfile() {
            // Zaten profil sayfasındayız
            document.getElementById('user-menu-dropdown').style.display = 'none';
        }
        
        // User menu setup
        document.addEventListener('DOMContentLoaded', function() {
            const userMenuBtn = document.getElementById('user-menu-btn');
            const userMenuDropdown = document.getElementById('user-menu-dropdown');
            
            if (userMenuBtn && userMenuDropdown) {
                userMenuBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleUserMenu();
                });
                
                document.addEventListener('click', function(e) {
                    if (!userMenuBtn.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                        userMenuDropdown.style.display = 'none';
                    }
                });
            }
        });
        
        function toggleUserMenu() {
            const userMenuDropdown = document.getElementById('user-menu-dropdown');
            const userMenuBtn = document.getElementById('user-menu-btn');
            
            if (userMenuDropdown.style.display === 'none' || !userMenuDropdown.style.display) {
                const rect = userMenuBtn.getBoundingClientRect();
                userMenuDropdown.style.display = 'block';
                userMenuDropdown.style.top = (rect.bottom + 5) + 'px';
                userMenuDropdown.style.right = (window.innerWidth - rect.right) + 'px';
            } else {
                userMenuDropdown.style.display = 'none';
            }
        }
    </script>
</body>
</html>
