<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js CSP Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #result { margin-top: 20px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔒 PDF.js CSP Uyumluluk Testi</h1>
    
    <div class="test-section info">
        <h3>Test Durumu</h3>
        <p>Bu sayfa PDF.js'in Content Security Policy ile uyumlu çalışıp çalışmadığını test eder.</p>
        <p><strong>Beklenen:</strong> PDF.js worker olmadan çalışmalı ve CSP hatası vermemeli.</p>
    </div>

    <div class="test-section">
        <h3>1. PDF.js Yükleme Testi</h3>
        <button onclick="testPDFJSLoading()">PDF.js Yükleme Test Et</button>
        <div id="loading-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Worker Konfigürasyon Testi</h3>
        <button onclick="testWorkerConfig()">Worker Konfigürasyonu Test Et</button>
        <div id="worker-result"></div>
    </div>

    <div class="test-section">
        <h3>3. PDF Dosya Yükleme Testi</h3>
        <input type="file" id="pdf-file" accept=".pdf">
        <button onclick="testPDFProcessing()">PDF İşleme Test Et</button>
        <div id="processing-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Console Log Kontrolü</h3>
        <button onclick="checkConsoleErrors()">Console Hatalarını Kontrol Et</button>
        <div id="console-result"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // PDF.js worker'ı devre dışı bırak - CSP uyumlu çözüm
        pdfjsLib.GlobalWorkerOptions.workerSrc = false;
        
        let consoleErrors = [];
        
        // Console error'ları yakala
        const originalError = console.error;
        console.error = function(...args) {
            consoleErrors.push(args.join(' '));
            originalError.apply(console, arguments);
        };

        function testPDFJSLoading() {
            const resultDiv = document.getElementById('loading-result');
            
            try {
                if (typeof pdfjsLib !== 'undefined') {
                    resultDiv.innerHTML = '<div class="success">✅ PDF.js başarıyla yüklendi</div>';
                    resultDiv.innerHTML += `<pre>Version: ${pdfjsLib.version || 'Unknown'}</pre>`;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ PDF.js yüklenemedi</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Hata: ${error.message}</div>`;
            }
        }

        function testWorkerConfig() {
            const resultDiv = document.getElementById('worker-result');
            
            try {
                const workerSrc = pdfjsLib.GlobalWorkerOptions.workerSrc;
                
                if (workerSrc === false) {
                    resultDiv.innerHTML = '<div class="success">✅ Worker devre dışı - CSP uyumlu</div>';
                } else if (workerSrc) {
                    resultDiv.innerHTML = `<div class="info">ℹ️ Worker aktif: ${workerSrc}</div>`;
                } else {
                    resultDiv.innerHTML = '<div class="info">ℹ️ Worker otomatik mod</div>';
                }
                
                resultDiv.innerHTML += `<pre>Worker Source: ${JSON.stringify(workerSrc)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Hata: ${error.message}</div>`;
            }
        }

        async function testPDFProcessing() {
            const resultDiv = document.getElementById('processing-result');
            const fileInput = document.getElementById('pdf-file');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">❌ Lütfen bir PDF dosyası seçin</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">⏳ PDF işleniyor...</div>';
                
                const file = fileInput.files[0];
                const arrayBuffer = await file.arrayBuffer();
                const typedArray = new Uint8Array(arrayBuffer);
                
                // PDF.js konfigürasyonu - Worker'sız
                const loadingTask = pdfjsLib.getDocument({
                    data: typedArray,
                    disableWorker: true,
                    useWorkerFetch: false,
                    isEvalSupported: false
                });
                
                const pdf = await loadingTask.promise;
                
                // İlk sayfayı test et
                const page = await pdf.getPage(1);
                const textContent = await page.getTextContent();
                const text = textContent.items.map(item => item.str).join(' ');
                
                resultDiv.innerHTML = '<div class="success">✅ PDF başarıyla işlendi</div>';
                resultDiv.innerHTML += `<pre>Sayfa sayısı: ${pdf.numPages}</pre>`;
                resultDiv.innerHTML += `<pre>İlk sayfa metin (ilk 200 karakter): ${text.substring(0, 200)}...</pre>`;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ PDF işleme hatası: ${error.message}</div>`;
                console.error('PDF processing error:', error);
            }
        }

        function checkConsoleErrors() {
            const resultDiv = document.getElementById('console-result');
            
            const cspErrors = consoleErrors.filter(error => 
                error.includes('Content Security Policy') || 
                error.includes('worker') ||
                error.includes('blob:')
            );
            
            if (cspErrors.length === 0) {
                resultDiv.innerHTML = '<div class="success">✅ CSP ile ilgili hata bulunamadı</div>';
            } else {
                resultDiv.innerHTML = '<div class="error">❌ CSP hataları bulundu:</div>';
                cspErrors.forEach(error => {
                    resultDiv.innerHTML += `<pre>${error}</pre>`;
                });
            }
            
            if (consoleErrors.length > 0) {
                resultDiv.innerHTML += '<div class="info">Tüm console hataları:</div>';
                consoleErrors.forEach(error => {
                    resultDiv.innerHTML += `<pre>${error}</pre>`;
                });
            }
        }

        // Sayfa yüklendiğinde otomatik testler
        window.addEventListener('load', function() {
            setTimeout(() => {
                testPDFJSLoading();
                testWorkerConfig();
            }, 1000);
        });
    </script>
</body>
</html>
