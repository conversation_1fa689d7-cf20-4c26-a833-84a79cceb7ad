<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Token Kontrolü</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .debug-item {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .debug-item strong {
            color: #333;
        }
        .token-data {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Debug - Token ve Kullanıcı Bilgileri</h1>
        
        <div class="debug-item">
            <strong>Token Durumu:</strong>
            <span id="token-status">Kontrol ediliyor...</span>
        </div>
        
        <div class="debug-item">
            <strong>UserData Durumu:</strong>
            <span id="userdata-status">Kontrol ediliyor...</span>
        </div>
        
        <div class="debug-item">
            <strong>Token İçeriği:</strong>
            <div class="token-data" id="token-content">Yükleniyor...</div>
        </div>
        
        <div class="debug-item">
            <strong>UserData İçeriği:</strong>
            <div class="token-data" id="userdata-content">Yükleniyor...</div>
        </div>
        
        <div class="debug-item">
            <strong>İşlemler:</strong>
            <button class="btn" onclick="goToProfile()">Profile Git</button>
            <button class="btn" onclick="goToLogin()">Login'e Git</button>
            <button class="btn danger" onclick="clearStorage()">Storage Temizle</button>
            <button class="btn" onclick="createTestToken()">Test Token Oluştur</button>
            <button class="btn" onclick="createSimpleToken()">Basit Token Oluştur</button>
            <button class="btn" onclick="testSummary()">Özet API Test</button>
        </div>
    </div>

    <script>
        function checkStorage() {
            const token = localStorage.getItem('userToken');
            const userData = localStorage.getItem('userData');
            
            // Token durumu
            const tokenStatus = document.getElementById('token-status');
            if (token) {
                tokenStatus.textContent = 'Mevcut ✓';
                tokenStatus.style.color = 'green';
                
                try {
                    // JWT token decode
                    const tokenParts = token.split('.');
                    if (tokenParts.length === 3) {
                        let payload = tokenParts[1];
                        while (payload.length % 4) {
                            payload += '=';
                        }
                        const tokenData = JSON.parse(atob(payload));
                        document.getElementById('token-content').textContent = JSON.stringify(tokenData, null, 2);
                    } else {
                        document.getElementById('token-content').textContent = 'Simple token: ' + token.substring(0, 50) + '...';
                    }
                } catch (e) {
                    document.getElementById('token-content').textContent = 'Token decode hatası: ' + e.message;
                }
            } else {
                tokenStatus.textContent = 'Yok ✗';
                tokenStatus.style.color = 'red';
                document.getElementById('token-content').textContent = 'Token bulunamadı';
            }
            
            // UserData durumu
            const userDataStatus = document.getElementById('userdata-status');
            if (userData) {
                userDataStatus.textContent = 'Mevcut ✓';
                userDataStatus.style.color = 'green';
                
                try {
                    const userDataObj = JSON.parse(userData);
                    document.getElementById('userdata-content').textContent = JSON.stringify(userDataObj, null, 2);
                } catch (e) {
                    document.getElementById('userdata-content').textContent = 'UserData parse hatası: ' + e.message;
                }
            } else {
                userDataStatus.textContent = 'Yok ✗';
                userDataStatus.style.color = 'red';
                document.getElementById('userdata-content').textContent = 'UserData bulunamadı';
            }
        }
        
        function goToProfile() {
            window.location.href = '/profile';
        }

        function goToLogin() {
            window.location.href = '/login';
        }
        
        function clearStorage() {
            localStorage.removeItem('userToken');
            localStorage.removeItem('userData');
            alert('Storage temizlendi!');
            checkStorage();
        }
        
        // UTF-8 safe base64 encode function
        function utf8ToBase64(str) {
            return btoa(unescape(encodeURIComponent(str)));
        }

        function createTestToken() {
            try {
                // JWT benzeri token oluştur (gerçek JWT değil, test amaçlı)
                const header = utf8ToBase64(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
                const payload = utf8ToBase64(JSON.stringify({
                    userId: 'test-user-123',
                    email: '<EMAIL>',
                    firstName: 'Test',
                    lastName: 'User', // Türkçe karakter kullanmayalım
                    role: 'user',
                    iat: Math.floor(Date.now() / 1000),
                    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 saat sonra
                }));
                const signature = utf8ToBase64('test-signature');

                const testToken = `${header}.${payload}.${signature}`;

                const testUserData = {
                    id: 'test-user-123',
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User',
                    firstName: 'Test',
                    lastName: 'User'
                };

                localStorage.setItem('userToken', testToken);
                localStorage.setItem('userData', JSON.stringify(testUserData));

                alert('Test JWT token oluşturuldu!');
                checkStorage();
            } catch (error) {
                console.error('Token creation error:', error);
                alert('Token oluşturma hatası: ' + error.message);
            }
        }

        function createSimpleToken() {
            // Basit session token (JWT değil)
            const simpleToken = 'simple-session-token-' + Date.now();

            const testUserData = {
                id: 'test-user-123',
                email: '<EMAIL>',
                first_name: 'Test',
                last_name: 'User',
                firstName: 'Test',
                lastName: 'User'
            };

            localStorage.setItem('userToken', simpleToken);
            localStorage.setItem('userData', JSON.stringify(testUserData));

            alert('Basit token oluşturuldu!');
            checkStorage();
        }

        async function testSummary() {
            const testText = `
ANKARA ASLİYE HUKUK MAHKEMESİ HAKİMLİĞİNE
DAVACILAR : Saniye Özkaya varisleri,
1-Kadir Seven Tc:0000000000
DAVALI : Ankara belediye başkanlığı
DAVA : Kamulaştırmasız el atma sebebiyle 1.000.-TL tazminatın faiziyle el atma tarihinden ve 1.000.-TL ecrimisil istemli faizyle el atma tarihinden itibaren, belirsiz alacak davası.
DAVA DEĞERİ : 2.000.-TL
            `.trim();

            try {
                console.log('Özet API test başlatılıyor...');

                const token = localStorage.getItem('userToken');
                const headers = {
                    'Content-Type': 'application/json',
                };

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const startTime = Date.now();
                const response = await fetch('/.netlify/functions/generate-summary', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ text: testText })
                });

                const endTime = Date.now();
                console.log(`API çağrısı süresi: ${endTime - startTime}ms`);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const data = await response.json();
                console.log('API Response:', data);

                alert(`Özet API Test Başarılı!\nSüre: ${endTime - startTime}ms\nÖzet: ${data.summary.substring(0, 100)}...`);

            } catch (error) {
                console.error('Özet API test hatası:', error);
                alert(`Özet API Test Hatası: ${error.message}`);
            }
        }

        // Sayfa yüklendiğinde kontrol et
        document.addEventListener('DOMContentLoaded', checkStorage);
    </script>
</body>
</html>
