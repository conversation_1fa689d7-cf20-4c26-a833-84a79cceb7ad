// Brevo E-posta Test Fonksiyonu

const { sendEmail } = require('./utils/email-service');

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        console.log('🧪 Starting Brevo email test...');

        // Test e-postası gönder
        const testResult = await sendEmail({
            to: '<EMAIL>',
            subject: '🧪 Brevo Test E-postası - ' + new Date().toLocaleString('tr-TR'),
            html: `
                <h1>🧪 Brevo Test E-postası</h1>
                <p>Bu e-posta Brevo servisinin çalışıp çalışmadığını test etmek için gönderilmiştir.</p>
                <p><strong>Gönderim Zamanı:</strong> ${new Date().toLocaleString('tr-TR')}</p>
                <p><strong>Test Durumu:</strong> ✅ Başarılı</p>
                <hr>
                <p><small>Bu e-posta LegalAI test sistemi tarafından otomatik olarak gönderilmiştir.</small></p>
            `,
            text: 'Brevo Test E-postası - ' + new Date().toLocaleString('tr-TR')
        });

        console.log('📧 Test email result:', testResult);

        if (testResult.success) {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Test e-postası başarıyla gönderildi!',
                    details: {
                        emailId: testResult.emailId,
                        provider: testResult.provider,
                        timestamp: new Date().toISOString()
                    }
                })
            };
        } else {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Test e-postası gönderilemedi',
                    details: testResult.error
                })
            };
        }

    } catch (error) {
        console.error('🚨 Test email error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Test sırasında hata oluştu',
                details: error.message,
                stack: error.stack
            })
        };
    }
};
