const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 8888;

const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

const server = http.createServer((req, res) => {
    console.log(`${req.method} ${req.url}`);

    // Clean URLs - remove .html extension
    let filePath = req.url;
    
    // Handle clean URLs
    if (filePath === '/login') filePath = '/login.html';
    else if (filePath === '/profile') filePath = '/profile.html';
    else if (filePath === '/admin') filePath = '/admin.html';
    else if (filePath === '/apply') filePath = '/apply.html';
    else if (filePath === '/debug') filePath = '/debug.html';
    else if (filePath === '/') filePath = '/index.html';

    filePath = path.join(__dirname, filePath);

    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // File not found, serve index.html for SPA
                fs.readFile(path.join(__dirname, 'index.html'), (error, content) => {
                    if (error) {
                        res.writeHead(500);
                        res.end('Server Error');
                    } else {
                        res.writeHead(200, { 'Content-Type': 'text/html' });
                        res.end(content, 'utf-8');
                    }
                });
            } else {
                res.writeHead(500);
                res.end('Server Error: ' + error.code);
            }
        } else {
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(port, () => {
    console.log(`Server running at http://localhost:${port}/`);
    console.log('Available pages:');
    console.log('- http://localhost:8888/');
    console.log('- http://localhost:8888/login');
    console.log('- http://localhost:8888/profile');
    console.log('- http://localhost:8888/admin');
    console.log('- http://localhost:8888/apply');
    console.log('- http://localhost:8888/debug');
});
