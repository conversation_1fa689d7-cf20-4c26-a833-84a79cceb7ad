// Debug Admin Token

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ 
                    error: 'Geçersiz token.',
                    details: jwtError.message 
                })
            };
        }

        // Admin kontrolü
        const { data: admin, error: adminError } = await supabase
            .from('admins')
            .select('id, email, role, is_active')
            .eq('id', decoded.adminId)
            .single();

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                tokenDecoded: {
                    adminId: decoded.adminId,
                    email: decoded.email,
                    role: decoded.role,
                    exp: decoded.exp,
                    iat: decoded.iat
                },
                adminFromDB: admin,
                adminError: adminError?.message,
                timestamp: new Date().toISOString()
            })
        };

    } catch (error) {
        console.error('Debug admin token error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Debug failed',
                details: error.message 
            })
        };
    }
}
