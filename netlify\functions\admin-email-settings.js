// Admin Email A<PERSON>ları Yönetimi
const { createClient } = require('@supabase/supabase-js');
const { testEmailConfiguration, clearEmailSettingsCache } = require('./utils/email-service');

// Supabase client - lazy initialization
let supabase = null;

function getSupabase() {
    if (!supabase) {
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

        if (!supabaseUrl || !supabaseKey) {
            console.error('Missing Supabase environment variables:', {
                hasUrl: !!supabaseUrl,
                hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
                hasServiceKey: !!process.env.SUPABASE_SERVICE_KEY,
                hasAnonKey: !!process.env.SUPABASE_ANON_KEY
            });
            throw new Error('Supabase configuration missing');
        }

        supabase = createClient(supabaseUrl, supabaseKey);
    }
    return supabase;
}

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    try {
        // Admin authentication kontrolü
        const authResult = await authenticateAdmin(event);
        if (!authResult.success) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: authResult.error })
            };
        }

        const method = event.httpMethod;
        const adminData = authResult.admin;

        switch (method) {
            case 'GET':
                return await getEmailSettings(adminData, headers);
            case 'POST':
                return await updateEmailSettings(event.body, adminData, headers);
            case 'PUT':
                return await testEmailSettings(adminData, headers);
            default:
                return {
                    statusCode: 405,
                    headers,
                    body: JSON.stringify({ error: 'Method not allowed' })
                };
        }
    } catch (error) {
        console.error('Admin email settings error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

/**
 * Admin authentication - Simplified version like other admin functions
 */
async function authenticateAdmin(event) {
    try {
        const authHeader = event.headers.authorization || event.headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return { success: false, error: 'Authorization header missing' };
        }

        const token = authHeader.substring(7);
        const jwt = require('jsonwebtoken');

        let decoded;
        try {
            decoded = jwt.verify(token, process.env.JWT_SECRET);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError.message);
            return { success: false, error: 'Invalid token' };
        }

        // JWT'den admin bilgilerini al (session kontrolü yapmıyoruz, diğer admin fonksiyonları gibi)
        return {
            success: true,
            admin: {
                adminId: decoded.adminId,
                email: decoded.email,
                fullName: decoded.fullName || decoded.email,
                role: decoded.role || 'admin'
            }
        };
    } catch (error) {
        console.error('Admin auth error:', error);
        return { success: false, error: 'Authentication failed' };
    }
}

/**
 * Email ayarlarını getir
 */
async function getEmailSettings(adminData, headers) {
    try {
        const supabase = getSupabase();
        // Spesifik key'leri çekelim - LIKE sorunu var
        const { data: settings, error } = await supabase
            .from('system_settings')
            .select('setting_key, setting_value, display_name, description, setting_type, options')
            .in('setting_key', [
                'email_enabled',
                'email_service_provider',
                'email_test_mode',
                'email_test_address',
                'email_company_name',
                'email_company_address',
                'email_footer_text',
                'email_logo_url',
                'email_rate_limit_enabled',
                'email_rate_limit_per_minute',
                'email_rate_limit_per_hour',
                'brevo_api_key',
                'brevo_sender_email',
                'brevo_sender_name',

                'admin_notification_emails',
                'support_email'
            ])
            .eq('is_active', true)
            .order('setting_key');

        if (error) {
            console.error('Settings fetch error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Settings could not be fetched' })
            };
        }

        // Şifre alanlarını gizle - SADECE boş değilse
        const sanitizedSettings = settings.map(setting => {
            console.log(`Setting ${setting.setting_key}: type=${setting.setting_type}, value="${setting.setting_value}", length=${setting.setting_value?.length}`);

            if (setting.setting_type === 'password' && setting.setting_value && setting.setting_value.length > 0) {
                return {
                    ...setting,
                    setting_value: '••••••••',
                    _original_length: setting.setting_value.length
                };
            }
            return setting;
        });

        // Admin log
        await logAdminAction(adminData, 'view_email_settings', null, null);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                settings: sanitizedSettings
            })
        };
    } catch (error) {
        console.error('Get email settings error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to get email settings' })
        };
    }
}

/**
 * Email ayarlarını güncelle
 */
async function updateEmailSettings(requestBody, adminData, headers) {
    try {
        const { settings } = JSON.parse(requestBody);
        
        if (!settings || !Array.isArray(settings)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Invalid settings format' })
            };
        }

        const updatedSettings = [];
        const errors = [];

        for (const setting of settings) {
            const { setting_key, setting_value } = setting;
            
            if (!setting_key) {
                errors.push('Setting key is required');
                continue;
            }

            // Şifre alanları için özel kontrol
            console.log(`Processing setting: ${setting_key} = "${setting_value}"`);
            if (setting_value === '••••••••') {
                console.log(`Skipping ${setting_key} - password unchanged`);
                continue; // Şifre değiştirilmemiş, atla
            }

            try {
                const supabase = getSupabase();
                const { data, error } = await supabase
                    .from('system_settings')
                    .update({
                        setting_value: setting_value,
                        updated_at: new Date().toISOString()
                    })
                    .eq('setting_key', setting_key)
                    .select();

                if (error) {
                    errors.push(`${setting_key}: ${error.message}`);
                } else {
                    updatedSettings.push(setting_key);
                }
            } catch (updateError) {
                errors.push(`${setting_key}: ${updateError.message}`);
            }
        }

        // Cache'i temizle
        clearEmailSettingsCache();

        // Admin log
        await logAdminAction(adminData, 'update_email_settings', 'system_settings', null, {
            updated_settings: updatedSettings,
            errors: errors
        });

        if (errors.length > 0) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Some settings could not be updated',
                    updated: updatedSettings,
                    errors: errors
                })
            };
        }

        // Cache'i temizle
        try {
            const { clearEmailSettingsCache } = require('./utils/email-service');
            clearEmailSettingsCache();
        } catch (e) {
            console.log('Cache clear failed:', e.message);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Email settings updated successfully',
                updated: updatedSettings
            })
        };
    } catch (error) {
        console.error('Update email settings error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to update email settings' })
        };
    }
}

/**
 * Email ayarlarını test et
 */
async function testEmailSettings(adminData, headers) {
    try {
        // Cache'i temizle ki yeni ayarlar kullanılsın
        clearEmailSettingsCache();
        
        const testResult = await testEmailConfiguration();

        // Admin log
        await logAdminAction(adminData, 'test_email_settings', null, null, {
            test_result: testResult
        });

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                test_result: testResult
            })
        };
    } catch (error) {
        console.error('Test email settings error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to test email settings' })
        };
    }
}

/**
 * Admin aksiyonunu logla
 */
async function logAdminAction(adminData, action, targetType, targetId, details = null) {
    try {
        const supabase = getSupabase();
        await supabase.from('admin_logs').insert([{
            admin_id: adminData.adminId,
            admin_email: adminData.email,
            action: action,
            target_type: targetType,
            target_id: targetId,
            details: details,
            ip_address: 'admin_panel',
            user_agent: 'admin_panel'
        }]);
    } catch (logError) {
        console.error('Admin log error:', logError);
    }
}
