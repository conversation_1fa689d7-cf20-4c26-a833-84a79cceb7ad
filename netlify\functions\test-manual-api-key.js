const { createClient } = require('@supabase/supabase-js');
const { apiKeyManager } = require('./api-key-manager');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        console.log('=== Manual API Key Test ===');
        
        const { keyName } = JSON.parse(event.body || '{}');
        const testKey = keyName || 'GEMINI_API_KEY2';
        
        console.log(`Testing manual selection with: ${testKey}`);
        
        // 1. Mevcut durumu kontrol et
        console.log('1. Current status before test:');
        const statusBefore = apiKeyManager.getKeyStatus();
        console.log('Status before:', statusBefore);
        
        // 2. Manuel seçimi test et
        console.log(`2. Setting manual API key: ${testKey}`);
        const setResult = await apiKeyManager.setManualApiKey(testKey);
        console.log(`Set result: ${setResult}`);
        
        // 3. Durumu tekrar kontrol et
        console.log('3. Status after setting:');
        const statusAfter = apiKeyManager.getKeyStatus();
        console.log('Status after:', statusAfter);
        
        // 4. Veritabanından kontrol et
        console.log('4. Checking database:');
        const { data: dbSetting, error: dbError } = await supabase
            .from('system_settings')
            .select('*')
            .eq('setting_key', 'manual_api_key')
            .single();
        
        console.log('Database setting:', dbSetting);
        console.log('Database error:', dbError);
        
        // 5. getCurrentApiKey test et
        console.log('5. Testing getCurrentApiKey:');
        const currentKey = await apiKeyManager.getCurrentApiKey();
        console.log('Current key:', currentKey?.name);
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                test_key: testKey,
                set_result: setResult,
                status_before: statusBefore,
                status_after: statusAfter,
                database_setting: dbSetting,
                database_error: dbError,
                current_active_key: currentKey?.name || 'None'
            })
        };

    } catch (error) {
        console.error('Manual API key test error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Test hatası',
                details: error.message
            })
        };
    }
};
