// Telegram Bot Bildirim Fonksiyonu
const https = require('https');

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json; charset=utf-8'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const { message, userInfo, type, ticketInfo, replyInfo } = JSON.parse(event.body || '{}');

        // Environment variables kontrolü
        const botToken = process.env.TELEGRAM_BOT_TOKEN;
        const chatId = process.env.TELEGRAM_CHAT_ID;

        if (!botToken || !chatId) {
            console.error('Telegram bot credentials missing');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Telegram bot yapılandırması eksik' })
            };
        }

        // Mesaj formatını type'a göre oluştur
        let telegramMessage = '';

        switch (type) {
            case 'new_support_ticket':
                telegramMessage = formatNewTicketMessage(ticketInfo, userInfo);
                break;
            case 'support_ticket_reply':
                telegramMessage = formatTicketReplyMessage(ticketInfo, replyInfo, userInfo);
                break;
            case 'user_registration':
            default:
                telegramMessage = formatUserRegistrationMessage(userInfo, message);
                break;
        }

        // Telegram API'ye mesaj gönder
        const telegramApiUrl = `https://api.telegram.org/bot${botToken}/sendMessage`;
        
        const postData = JSON.stringify({
            chat_id: chatId,
            text: telegramMessage,
            parse_mode: 'Markdown'
        });

        const result = await sendTelegramMessage(telegramApiUrl, postData);

        if (result.success) {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Telegram bildirimi başarıyla gönderildi'
                })
            };
        } else {
            throw new Error(result.error);
        }

    } catch (error) {
        console.error('Telegram notification error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Telegram bildirimi gönderilirken hata oluştu',
                details: error.message
            })
        };
    }
};

// Telegram API'ye HTTPS isteği gönder
function sendTelegramMessage(url, postData) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: 443,
            path: urlObj.pathname,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (response.ok) {
                        resolve({ success: true, data: response });
                    } else {
                        resolve({ 
                            success: false, 
                            error: `Telegram API Error: ${response.description || 'Unknown error'}` 
                        });
                    }
                } catch (parseError) {
                    resolve({ 
                        success: false, 
                        error: `Response parse error: ${parseError.message}` 
                    });
                }
            });
        });

        req.on('error', (error) => {
            resolve({ 
                success: false, 
                error: `Request error: ${error.message}` 
            });
        });

        req.write(postData);
        req.end();
    });
}

// Kullanıcı kaydı mesaj formatı
function formatUserRegistrationMessage(userInfo, message) {
    let telegramMessage = `🔔 *Üyelik Bildirimi*\n\n`;

    if (userInfo) {
        telegramMessage += `👤 *Ad Soyad:* ${userInfo.firstName} ${userInfo.lastName}\n`;
        telegramMessage += `📧 *E-posta:* ${userInfo.email}\n`;
        telegramMessage += `📱 *Telefon:* ${userInfo.phone || 'Belirtilmemiş'}\n`;
        telegramMessage += `💼 *Meslek:* ${userInfo.profession || 'Belirtilmemiş'}\n`;
        telegramMessage += `📝 *Başvuru Nedeni:* ${userInfo.reason || 'Belirtilmemiş'}\n`;
        telegramMessage += `⏰ *Tarih:* ${new Date().toLocaleString('tr-TR')}\n`;
    }

    if (message) {
        telegramMessage += `\n📄 *Mesaj:* ${message}`;
    }

    return telegramMessage;
}

// Yeni destek talebi mesaj formatı
function formatNewTicketMessage(ticketInfo, userInfo) {
    let telegramMessage = `🎧 *Yeni Destek Talebi*\n\n`;

    if (ticketInfo) {
        telegramMessage += `🎫 *Ticket No:* #${ticketInfo.ticket_number}\n`;
        telegramMessage += `📋 *Konu:* ${ticketInfo.subject}\n`;
        telegramMessage += `📝 *Açıklama:* ${ticketInfo.description}\n`;
        telegramMessage += `⚡ *Öncelik:* ${getPriorityText(ticketInfo.priority)}\n`;
        telegramMessage += `📂 *Kategori:* ${ticketInfo.category_name || 'Belirtilmemiş'}\n`;
    }

    if (userInfo) {
        telegramMessage += `\n👤 *Kullanıcı:* ${userInfo.first_name} ${userInfo.last_name}\n`;
        telegramMessage += `📧 *E-posta:* ${userInfo.email}\n`;
    }

    telegramMessage += `⏰ *Tarih:* ${new Date().toLocaleString('tr-TR')}`;

    return telegramMessage;
}

// Destek talebi yanıt mesaj formatı
function formatTicketReplyMessage(ticketInfo, replyInfo, userInfo) {
    const isAdminReply = replyInfo.is_admin_reply;
    const replyType = isAdminReply ? 'Admin Yanıtı' : 'Kullanıcı Yanıtı';
    const icon = isAdminReply ? '💬' : '📝';

    let telegramMessage = `${icon} *${replyType}*\n\n`;

    if (ticketInfo) {
        telegramMessage += `🎫 *Ticket No:* #${ticketInfo.ticket_number}\n`;
        telegramMessage += `📋 *Konu:* ${ticketInfo.subject}\n`;
    }

    if (replyInfo) {
        telegramMessage += `💬 *Yanıt:* ${replyInfo.message}\n`;

        if (isAdminReply && userInfo) {
            telegramMessage += `👨‍💼 *Yanıtlayan:* ${userInfo.full_name || userInfo.fullName || 'Destek Ekibi'}\n`;
        } else if (!isAdminReply && userInfo) {
            telegramMessage += `👤 *Yanıtlayan:* ${userInfo.first_name} ${userInfo.last_name}\n`;
        }
    }

    telegramMessage += `⏰ *Tarih:* ${new Date().toLocaleString('tr-TR')}`;

    return telegramMessage;
}

// Öncelik metni
function getPriorityText(priority) {
    const priorities = {
        'low': '🟢 Düşük',
        'medium': '🟡 Orta',
        'high': '🟠 Yüksek',
        'urgent': '🔴 Acil'
    };
    return priorities[priority] || priority;
}
