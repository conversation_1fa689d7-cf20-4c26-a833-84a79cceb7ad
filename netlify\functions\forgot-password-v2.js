const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

exports.handler = async (event, context) => {
    console.log('Forgot password v2 function called');
    
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Environment variables check
        if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
            console.error('Missing Supabase environment variables');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Server configuration error' })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { email } = body;

        console.log('Processing email:', email);

        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'E-posta adresi gereklidir.' })
            };
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçerli bir e-posta adresi girin.' })
            };
        }

        // Initialize Supabase
        const supabase = createClient(
            process.env.SUPABASE_URL,
            process.env.SUPABASE_SERVICE_ROLE_KEY
        );

        console.log('Supabase client initialized');

        // Kullanıcıyı kontrol et
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, first_name, last_name, status')
            .eq('email', email)
            .single();

        console.log('User query result:', { user: !!user, error: userError });

        if (userError || !user) {
            // Güvenlik için her zaman başarılı mesajı döndür
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderilecektir.'
                })
            };
        }

        // Kullanıcı durumu kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderilecektir.'
                })
            };
        }

        // Token oluştur
        const resetToken = crypto.randomBytes(32).toString('hex');
        const tokenHash = crypto.createHash('sha256').update(resetToken).digest('hex');
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 saat

        console.log('Token generated, saving to database');

        // Mevcut aktif token'ları temizle
        await supabase
            .from('password_reset_tokens')
            .delete()
            .eq('user_id', user.id)
            .is('used_at', null);

        // Token'ı veritabanına kaydet
        const { error: tokenError } = await supabase
            .from('password_reset_tokens')
            .insert({
                user_id: user.id,
                email: user.email,
                token_hash: tokenHash,
                expires_at: expiresAt.toISOString(),
                ip_address: event.headers['x-forwarded-for']?.split(',')[0] || 'unknown',
                user_agent: event.headers['user-agent'] || null
            });

        if (tokenError) {
            console.error('Token save error:', tokenError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Şifre sıfırlama isteği işlenirken hata oluştu.' })
            };
        }

        console.log('Token saved successfully');

        // Reset URL oluştur
        const resetUrl = `${process.env.URL || 'https://hukukibelgeozetleme.netlify.app'}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;

        console.log('Reset URL generated:', resetUrl);

        // Şimdilik email gönderme simülasyonu
        console.log('Email would be sent to:', email);
        console.log('Reset link:', resetUrl);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Şifre sıfırlama linki e-posta adresinize gönderildi.',
                debug: {
                    resetUrl: resetUrl,
                    note: 'Bu test modunda - gerçek email gönderilmedi'
                }
            })
        };

    } catch (error) {
        console.error('Forgot password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Şifre sıfırlama isteği işlenirken hata oluştu.',
                details: error.message 
            })
        };
    }
};
