// Trial limit kontrolü function'ı
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // IP adresini al
        const clientIP = getClientIP(event);
        
        if (!clientIP) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'IP adresi alınamadı.' })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { userAgent, fingerprint } = body;

        // Supabase function ile trial limit kontrolü - Fallback mekanizması ile
        let data;
        try {
            const { data: rpcData, error: rpcError } = await supabase.rpc('check_trial_limit', {
                p_ip_address: clientIP,
                p_user_agent: userAgent || null,
                p_fingerprint: fingerprint || null
            });

            if (rpcError) {
                console.error('Trial check RPC error:', rpcError);
                // RPC fonksiyonu yoksa fallback kullan
                data = await checkTrialLimitFallback(clientIP);
            } else {
                data = rpcData;
            }
        } catch (generalError) {
            console.error('Trial check general error:', generalError);
            // Genel hata durumunda fallback kullan
            data = await checkTrialLimitFallback(clientIP);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                trial: data,
                ip: clientIP // Debug için
            })
        };

    } catch (error) {
        console.error('Check trial error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Trial kontrolü sırasında hata oluştu.' })
        };
    }
};

// IP adresini güvenilir şekilde al
function getClientIP(event) {
    // Netlify'da client IP'yi alma yöntemleri
    const headers = event.headers;
    
    // Netlify'nin sağladığı header'lar
    let ip = headers['x-nf-client-connection-ip'] ||
             headers['x-forwarded-for'] ||
             headers['x-real-ip'] ||
             headers['cf-connecting-ip'] || // Cloudflare
             headers['x-client-ip'] ||
             event.requestContext?.identity?.sourceIp ||
             '127.0.0.1';

    // X-Forwarded-For birden fazla IP içerebilir, ilkini al
    if (ip.includes(',')) {
        ip = ip.split(',')[0].trim();
    }

    // IPv6'yı IPv4'e çevir (gerekirse)
    if (ip === '::1') {
        ip = '127.0.0.1';
    }

    return ip;
}

// 🛡️ GÜVENLİK: Trial limit fallback kontrolü (RPC fonksiyonu çalışmadığında)
async function checkTrialLimitFallback(clientIP) {
    try {
        // Basit IP tabanlı kontrol - son 24 saatte 3'ten fazla kullanım var mı?
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);

        // Mevcut trial_usage tablosundan kontrol et
        const { data: usageRecord, error } = await supabase
            .from('trial_usage')
            .select('*')
            .eq('ip_address', clientIP)
            .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
            console.error('Fallback trial check error:', error);
            // Veritabanı hatası durumunda izin ver (güvenlik vs kullanılabilirlik)
            return {
                allowed: true,
                message: 'Deneme hakkınız bulunmaktadır.',
                usage_count: 0,
                max_usage: 1,
                remaining: 1
            };
        }

        const maxUsage = 1; // Sadece 1 deneme hakkı (24 saat sıfırlama yok)
        let currentUsage = 0;

        if (usageRecord) {
            // 24 saat sıfırlama kaldırıldı - bir kez kullandıysa artık hak yok
            currentUsage = usageRecord.usage_count || 0;
        }

        return {
            allowed: currentUsage < maxUsage,
            message: currentUsage >= maxUsage ?
                'Deneme hakkınız dolmuştur. Lütfen üye olun.' :
                'Deneme hakkınız bulunmaktadır.',
            usage_count: currentUsage,
            max_usage: maxUsage,
            remaining: Math.max(0, maxUsage - currentUsage)
        };

    } catch (error) {
        console.error('Fallback trial check general error:', error);
        // Genel hata durumunda izin ver
        return {
            allowed: true,
            message: 'Deneme hakkınız bulunmaktadır.',
            usage_count: 0,
            max_usage: 1,
            remaining: 1
        };
    }
}
