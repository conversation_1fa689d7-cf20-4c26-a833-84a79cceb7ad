// Reset Password JavaScript
document.addEventListener('DOMContentLoaded', function() {
    setupResetForm();
    checkUrlParams();
});

let resetToken = null;
let resetEmail = null;

function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    resetToken = urlParams.get('token');
    resetEmail = urlParams.get('email');

    if (!resetToken || !resetEmail) {
        showError('Geçersiz şifre sıfırlama linki. Lütfen yeni bir şifre sıfırlama isteği yapın.');
        document.getElementById('reset-form').style.display = 'none';
        return;
    }

    console.log('Reset parameters found:', { email: resetEmail, hasToken: !!resetToken });
}

function setupResetForm() {
    const form = document.getElementById('reset-form');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm-password');

    form.addEventListener('submit', handleResetPassword);
    
    // Real-time password validation
    passwordInput.addEventListener('input', validatePassword);
    confirmPasswordInput.addEventListener('input', validatePasswordMatch);
}

function validatePassword() {
    const password = document.getElementById('password').value;
    const strengthDiv = document.querySelector('.password-strength');
    
    if (password.length === 0) {
        strengthDiv.textContent = 'Şifreniz en az 6 karakter uzunluğunda olmalıdır.';
        strengthDiv.style.color = 'var(--gray-500)';
        return false;
    } else if (password.length < 6) {
        strengthDiv.textContent = 'Şifre çok kısa. En az 6 karakter gerekli.';
        strengthDiv.style.color = '#dc2626';
        return false;
    } else if (password.length >= 6 && password.length < 8) {
        strengthDiv.textContent = 'Şifre gücü: Zayıf';
        strengthDiv.style.color = '#f59e0b';
        return true;
    } else if (password.length >= 8) {
        strengthDiv.textContent = 'Şifre gücü: İyi';
        strengthDiv.style.color = '#10b981';
        return true;
    }
}

function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const confirmInput = document.getElementById('confirm-password');
    
    if (confirmPassword.length === 0) {
        confirmInput.style.borderColor = 'var(--gray-200)';
        return false;
    }
    
    if (password === confirmPassword) {
        confirmInput.style.borderColor = '#10b981';
        return true;
    } else {
        confirmInput.style.borderColor = '#dc2626';
        return false;
    }
}

async function handleResetPassword(e) {
    e.preventDefault();
    
    if (!resetToken || !resetEmail) {
        showError('Geçersiz şifre sıfırlama linki.');
        return;
    }
    
    const form = e.target;
    const formData = new FormData(form);
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    
    // Validation
    if (!validatePassword()) {
        showError('Şifre en az 6 karakter olmalıdır.');
        return;
    }
    
    if (!validatePasswordMatch()) {
        showError('Şifreler eşleşmiyor.');
        return;
    }
    
    const submitBtn = document.getElementById('reset-submit');
    const btnText = document.getElementById('reset-btn-text');
    const spinner = document.getElementById('reset-loading-spinner');

    // Show loading
    btnText.style.display = 'none';
    spinner.style.display = 'inline-flex';
    submitBtn.disabled = true;
    
    try {
        const resetData = {
            token: resetToken,
            email: resetEmail,
            password: password,
            confirmPassword: confirmPassword
        };

        console.log('Reset password attempt:', { email: resetEmail, hasToken: !!resetToken });

        const response = await fetch('/.netlify/functions/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(resetData)
        });

        const data = await response.json();
        console.log('Reset password response:', { status: response.status, success: data.success });
        
        if (data.success) {
            showSuccess(data.message);
            form.style.display = 'none';
            
            // Redirect to login after 3 seconds
            setTimeout(() => {
                window.location.href = '/login';
            }, 3000);
        } else {
            showError(data.error || 'Şifre sıfırlanırken hata oluştu.');
        }
        
    } catch (error) {
        console.error('Reset password error:', error);
        showError('Bağlantı hatası. Lütfen tekrar deneyin.');
    } finally {
        // Hide loading
        btnText.style.display = 'inline-flex';
        spinner.style.display = 'none';
        submitBtn.disabled = false;
    }
}

function showSuccess(message) {
    const successDiv = document.getElementById('success-message');
    const errorDiv = document.getElementById('error-message');
    
    errorDiv.style.display = 'none';
    successDiv.textContent = message;
    successDiv.style.display = 'block';
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function showError(message) {
    const errorDiv = document.getElementById('error-message');
    const successDiv = document.getElementById('success-message');
    
    successDiv.style.display = 'none';
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    // Hide after 10 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 10000);
}
