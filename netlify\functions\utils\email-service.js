// Email Servis Yönetimi - Dinamik Email Sağlayıcı Seçimi
// NOT: Supabase dependency kaldırıldı - email ayarları parametre olarak geçilecek

// Email ayarlarını cache'le (performans için)
let emailSettingsCache = null;
let cacheTimestamp = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 dakika

// Cache temizleme fonksiyonu
function clearEmailSettingsCache() {
    emailSettingsCache = null;
    cacheTimestamp = null;
    console.log('📧 Email settings cache cleared');
}

/**
 * Email ayarlarını parametre olarak al (Supabase dependency kaldırıldı)
 */
function getEmailSettings(settingsData) {
    try {
        console.log('📧 Processing email settings from parameter:', settingsData);

        // Settings'i object'e çevir + fallback değerler
        const settingsObj = {
            // Kritik fallback değerler
            email_enabled: 'true',
            email_service_provider: 'brevo', // Brevo'yu var<PERSON><PERSON>lan yap
            email_test_mode: 'false',
            brevo_api_key: '',
            brevo_sender_email: '<EMAIL>',
            brevo_sender_name: 'LegalAI',

        };

        if (settingsData && Array.isArray(settingsData)) {
            settingsData.forEach(setting => {
                let value = setting.setting_value;

                // Tip dönüşümleri
                switch (setting.setting_type) {
                    case 'boolean':
                        value = value === 'true';
                        break;
                    case 'number':
                        value = parseInt(value);
                        break;
                    case 'json':
                        try {
                            value = JSON.parse(value);
                        } catch (e) {
                            console.error('JSON parse error for', setting.setting_key, ':', e);
                        }
                        break;
                }

                settingsObj[setting.setting_key] = value;
            });
        }

        console.log('📧 Final settings object:', settingsObj);
        return settingsObj;
    } catch (error) {
        console.error('Get email settings error:', error);
        return null;
    }
}

/**
 * Email servis sağlayıcısını başlat
 */
async function initializeEmailService(settingsData) {
    try {
        const settings = getEmailSettings(settingsData);
        if (!settings) {
            throw new Error('Email settings could not be loaded');
        }

        const provider = settings.email_service_provider || 'brevo';

        // Sadece Brevo destekleniyor
        if (provider !== 'brevo') {
            console.warn(`Unsupported email provider: ${provider}, using Brevo as default`);
        }
        return await initializeBrevo(settings);
    } catch (error) {
        console.error('Email service initialization error:', error);
        throw error;
    }
}



/**
 * Brevo servisini başlat (Modern Fetch API ile)
 */
async function initializeBrevo(settings) {
    const apiKey = settings.brevo_api_key;
    if (!apiKey || apiKey.trim() === '') {
        console.error('❌ Brevo API key not configured or empty');
        throw new Error('Brevo API key not configured');
    }

    console.log('🔧 Initializing Brevo with modern fetch API');
    console.log('🔧 API Key length:', apiKey.length);
    console.log('🔧 Sender email:', settings.brevo_sender_email);

    return {
        provider: 'brevo',
        client: null, // Fetch API kullanacağız
        settings: {
            apiKey: apiKey,
            senderEmail: settings.brevo_sender_email || '<EMAIL>',
            senderName: settings.brevo_sender_name || 'LegalAI'
        }
    };
}



/**
 * Email gönder - Universal interface
 */
async function sendEmail(emailData, settingsData) {
    try {
        const settings = getEmailSettings(settingsData);
        if (!settings) {
            throw new Error('Email settings not available');
        }

        // Email gönderimi devre dışı mı? - Güçlü kontrol
        const emailEnabledRaw = settings.email_enabled;
        const emailEnabled = emailEnabledRaw === 'true' || emailEnabledRaw === true || emailEnabledRaw === 1 || emailEnabledRaw === '1';

        console.log('🔍 EMAIL ENABLED CHECK:', {
            email_enabled_raw: emailEnabledRaw,
            email_enabled_type: typeof emailEnabledRaw,
            emailEnabled: emailEnabled,
            provider: settings.email_service_provider,
            brevo_api_key_exists: !!settings.brevo_api_key,
            brevo_api_key_length: settings.brevo_api_key?.length,
            all_email_settings: Object.keys(settings).filter(k => k.includes('email') || k.includes('brevo'))
        });

        if (!emailEnabled) {
            console.error('❌ Email sending is disabled. email_enabled value:', emailEnabledRaw);
            return { success: false, error: `Email sending is disabled (value: ${emailEnabledRaw})` };
        }

        console.log('✅ Email sending is enabled');

        // Test modu kontrolü
        const testMode = settings.email_test_mode === 'true' || settings.email_test_mode === true;
        const originalTo = emailData.to;
        
        if (testMode && settings.email_test_address) {
            emailData.to = Array.isArray(emailData.to) 
                ? [settings.email_test_address] 
                : settings.email_test_address;
            
            // Subject'e test mode ekleme
            emailData.subject = `${emailData.subject} (Test Mode - Original: ${Array.isArray(originalTo) ? originalTo.join(', ') : originalTo})`;
        }

        const emailService = await initializeEmailService(settingsData);
        
        // Sadece Brevo destekleniyor
        if (emailService.provider !== 'brevo') {
            throw new Error(`Unsupported provider: ${emailService.provider}`);
        }
        return await sendWithBrevo(emailService, emailData);
    } catch (error) {
        console.error('Send email error:', error);
        return { success: false, error: error.message };
    }
}

/**
 * Resend ile email gönder
 */


/**
 * Brevo ile email gönder (Modern Fetch API)
 */
async function sendWithBrevo(emailService, emailData) {
    const { settings } = emailService;

    const sendSmtpEmail = {
        sender: {
            name: settings.senderName,
            email: settings.senderEmail
        },
        to: Array.isArray(emailData.to)
            ? emailData.to.map(email => ({ email }))
            : [{ email: emailData.to }],
        subject: emailData.subject,
        htmlContent: emailData.html,
        textContent: emailData.text
    };

    try {
        // DETAYLI LOGLAMA - SORUN TESPİTİ İÇİN
        console.log('=== BREVO EMAIL SENDING DEBUG ===');
        console.log('📧 Original emailData received:', JSON.stringify(emailData, null, 2));
        console.log('📧 Processed sendSmtpEmail:', JSON.stringify(sendSmtpEmail, null, 2));
        console.log('📧 Email service settings:', JSON.stringify(settings, null, 2));
        console.log('📧 Timestamp:', new Date().toISOString());
        console.log('=== END BREVO DEBUG ===');

        console.log('📧 Sending email via Brevo API:', {
            to: sendSmtpEmail.to,
            subject: sendSmtpEmail.subject,
            sender: sendSmtpEmail.sender
        });

        console.log('🌐 Making request to Brevo API...');
        const response = await fetch('https://api.brevo.com/v3/smtp/email', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'api-key': settings.apiKey
            },
            body: JSON.stringify(sendSmtpEmail)
        });

        console.log('📡 Brevo API response status:', response.status);
        console.log('📡 Brevo API response ok:', response.ok);
        console.log('📡 Brevo API response headers:', JSON.stringify([...response.headers.entries()], null, 2));

        const result = await response.json();
        console.log('📡 Brevo API response body:', JSON.stringify(result, null, 2));

        if (response.ok) {
            console.log('✅ Brevo email sent successfully:', result);
            return { success: true, emailId: result.messageId, provider: 'brevo' };
        } else {
            console.error('❌ Brevo API error. Status:', response.status);
            console.error('❌ Brevo API error body:', result);
            return { success: false, error: result.message || `Brevo API error (${response.status})` };
        }
    } catch (error) {
        console.error('❌ Brevo fetch error:', error);
        return { success: false, error: error.message };
    }
}



/**
 * Email ayarlarını test et
 */
async function testEmailConfiguration(settingsData) {
    try {
        const settings = getEmailSettings(settingsData);
        if (!settings) {
            return { success: false, error: 'Email settings not available' };
        }

        const provider = settings.email_service_provider;
        const testEmail = settings.email_test_address || '<EMAIL>';

        const testResult = await sendEmail({
            to: testEmail,
            subject: 'Email Configuration Test',
            html: '<h1>Test Email</h1><p>This is a test email to verify email configuration.</p>',
            text: 'Test Email - This is a test email to verify email configuration.'
        }, settingsData);

        return {
            success: testResult.success,
            provider: provider,
            error: testResult.error,
            emailId: testResult.emailId
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * Cache'i temizle
 */
function clearEmailSettingsCache() {
    emailSettingsCache = null;
    cacheTimestamp = null;
}

module.exports = {
    getEmailSettings,
    initializeEmailService,
    sendEmail,
    testEmailConfiguration,
    clearEmailSettingsCache
};
