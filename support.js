// Destek Talebi Oluşturma Sayfası JavaScript

let userToken = null;
let userData = null;

// <PERSON>fa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    checkAuthentication();
    loadCategories();
    setupFormHandlers();
});

// Kullanıcı kimlik doğrulaması kontrolü
function checkAuthentication() {
    userToken = localStorage.getItem('userToken');
    const userDataStr = localStorage.getItem('userData');
    
    if (!userToken || !userDataStr) {
        // Kullanıcı giriş yapmamış, login sayfasına yönlendir
        window.location.href = '/login';
        return;
    }
    
    try {
        userData = JSON.parse(userDataStr);
        console.log('User authenticated:', userData);
    } catch (e) {
        console.error('Invalid user data:', e);
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        window.location.href = '/login';
    }
}

// <PERSON>gorileri yükle
async function loadCategories() {
    try {
        const response = await fetch('/.netlify/functions/support-categories', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            populateCategories(data.categories);
        } else {
            console.error('Failed to load categories');
            showError('Kategoriler yüklenemedi. Lütfen sayfayı yenileyin.');
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        showError('Kategoriler yüklenirken bir hata oluştu.');
    }
}

// Kategori seçeneklerini doldur
function populateCategories(categories) {
    const categorySelect = document.getElementById('category');
    
    // Mevcut seçenekleri temizle (placeholder hariç)
    while (categorySelect.children.length > 1) {
        categorySelect.removeChild(categorySelect.lastChild);
    }
    
    // Kategorileri ekle
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        option.setAttribute('data-color', category.color);
        option.setAttribute('data-icon', category.icon);
        categorySelect.appendChild(option);
    });
}

// Form event handler'larını ayarla
function setupFormHandlers() {
    const form = document.getElementById('support-form');
    const descriptionTextarea = document.getElementById('description');
    const charCountSpan = document.getElementById('char-count');
    
    // Karakter sayacı
    descriptionTextarea.addEventListener('input', function() {
        const currentLength = this.value.length;
        charCountSpan.textContent = currentLength;
        
        // Limit aşıldığında uyarı rengi
        if (currentLength > 1900) {
            charCountSpan.style.color = '#e53e3e';
        } else {
            charCountSpan.style.color = '#718096';
        }
    });
    
    // Form submit
    form.addEventListener('submit', handleFormSubmit);
}

// Form gönderimi
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const submitBtn = document.getElementById('submit-btn');
    const formData = new FormData(event.target);
    
    // Buton durumunu güncelle
    setButtonLoading(submitBtn, true);
    
    try {
        const ticketData = {
            category_id: formData.get('category'),
            subject: formData.get('subject').trim(),
            description: formData.get('description').trim(),
            priority: formData.get('priority')
        };
        
        // Validasyon
        if (!validateForm(ticketData)) {
            setButtonLoading(submitBtn, false);
            return;
        }
        
        // API'ye gönder
        const response = await fetch('/.netlify/functions/support-create-ticket', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ticketData)
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            showSuccess(`Destek talebiniz başarıyla oluşturuldu! Ticket numaranız: ${data.ticket.ticket_number}`);
            
            // Formu temizle
            event.target.reset();
            document.getElementById('char-count').textContent = '0';
            
            // 2 saniye sonra destek listesine yönlendir
            setTimeout(() => {
                window.location.href = '/support-tickets';
            }, 2000);
            
        } else {
            showError(data.error || 'Destek talebi oluşturulurken bir hata oluştu.');
        }
        
    } catch (error) {
        console.error('Error creating support ticket:', error);
        showError('Destek talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

// Form validasyonu
function validateForm(data) {
    // Kategori kontrolü
    if (!data.category_id) {
        showError('Lütfen bir kategori seçin.');
        return false;
    }
    
    // Konu kontrolü
    if (!data.subject || data.subject.length < 5) {
        showError('Konu en az 5 karakter olmalıdır.');
        return false;
    }
    
    if (data.subject.length > 255) {
        showError('Konu 255 karakterden uzun olamaz.');
        return false;
    }
    
    // Açıklama kontrolü
    if (!data.description || data.description.length < 20) {
        showError('Açıklama en az 20 karakter olmalıdır.');
        return false;
    }
    
    if (data.description.length > 2000) {
        showError('Açıklama 2000 karakterden uzun olamaz.');
        return false;
    }
    
    // Öncelik kontrolü
    if (!data.priority) {
        showError('Lütfen bir öncelik seviyesi seçin.');
        return false;
    }
    
    return true;
}

// Buton loading durumu
function setButtonLoading(button, isLoading) {
    if (isLoading) {
        button.disabled = true;
        button.classList.add('loading');
        button.innerHTML = `
            <span class="loading-spinner"></span>
            Gönderiliyor...
        `;
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        button.innerHTML = `
            <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>
            Destek Talebi Gönder
        `;
    }
}

// Hata mesajı göster
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    const successDiv = document.getElementById('success-message');
    
    successDiv.style.display = 'none';
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    
    // Sayfanın üstüne scroll
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // 5 saniye sonra gizle
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

// Başarı mesajı göster
function showSuccess(message) {
    const errorDiv = document.getElementById('error-message');
    const successDiv = document.getElementById('success-message');
    
    errorDiv.style.display = 'none';
    successDiv.textContent = message;
    successDiv.style.display = 'block';
    
    // Sayfanın üstüne scroll
    successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Sayfa çıkış uyarısı kaldırıldı - kullanıcı deneyimi için
