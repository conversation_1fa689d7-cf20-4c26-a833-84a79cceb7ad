// Destek Kategorilerini Getiren API Endpoint

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece GET metodunu kabul et
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Kullanıcının varlığını kontrol et
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, status')
            .eq('id', decoded.userId)
            .single();

        if (userError || !user) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
            };
        }

        // Kullanıcı onaylanmış mı kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({ 
                    error: 'Destek talebi oluşturmak için hesabınızın onaylanmış olması gerekir.' 
                })
            };
        }

        // Aktif kategorileri getir
        const { data: categories, error: categoriesError } = await supabase
            .from('support_categories')
            .select('id, name, description, color, icon')
            .eq('is_active', true)
            .order('sort_order');

        if (categoriesError) {
            console.error('Categories fetch error:', categoriesError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Kategoriler getirilemedi.' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                categories: categories || []
            })
        };

    } catch (error) {
        console.error('Support categories error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}
