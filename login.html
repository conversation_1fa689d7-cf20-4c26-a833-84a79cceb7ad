<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giriş - LegalAI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="mobile-menu.css">
    <style>
        /* Modern Corporate Design System */
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-900: #1e3a8a;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
        }

        /* Navbar styles are in styles.css and mobile-menu.css */

        /* Modern Container */
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 2rem;
        }

        .login-wrapper {
            width: 100%;
            max-width: 1000px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
        }

        /* Left Panel - Modern Hero */
        .hero-panel {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .hero-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2.5rem;
            line-height: 1.6;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            backdrop-filter: blur(10px);
        }

        .feature-icon {
            font-size: 1.25rem;
            margin-right: 1rem;
            opacity: 0.9;
        }

        .feature-text {
            font-weight: 500;
            font-size: 0.95rem;
        }

        /* Login Panel */
        .login-panel {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: var(--gray-50);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: var(--gray-600);
            font-size: 1rem;
        }

        /* Modern Form Styles */
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-input {
            padding: 0.875rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: 0.75rem;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            background: white;
            color: var(--gray-900);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input::placeholder {
            color: var(--gray-400);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .checkbox-input {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid var(--gray-300);
            border-radius: 0.375rem;
        }

        .checkbox-input:checked {
            background-color: var(--primary-500);
            border-color: var(--primary-500);
        }

        .checkbox-label {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .login-button {
            width: 100%;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            border: none;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .forgot-link {
            text-align: center;
            margin-top: 1rem;
        }

        .forgot-link a {
            color: var(--primary-600);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .forgot-link a:hover {
            text-decoration: underline;
        }

        .signup-link {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--gray-200);
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .signup-link a {
            color: var(--primary-600);
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: 0.75rem;
            margin-top: 1rem;
            font-size: 0.875rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                margin: 1rem;
                border-radius: 1rem;
            }

            .hero-panel {
                padding: 2rem;
                text-align: center;
            }

            .hero-title {
                font-size: 2rem;
            }

            .login-panel {
                padding: 2rem;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                padding: 5rem 1rem 1rem;
            }

            .hero-panel,
            .login-panel {
                padding: 1.5rem;
            }

            .hero-title {
                font-size: 1.75rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-balance-scale nav-icon"></i>
                <span class="nav-title">LegalAI</span>
            </div>

            <!-- Desktop Menu -->
            <div class="nav-menu">
                <a href="/" class="nav-link">Ana Sayfa</a>
                <button class="nav-btn" id="user-menu-btn" style="display: none;">
                    <i class="fas fa-user"></i>
                    <span id="user-name"></span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <a href="/apply" class="nav-link" id="register-link">Üye Ol</a>
                <a href="/login" class="nav-link active" id="login-link">Giriş Yap</a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle" type="button" aria-label="Menüyü aç/kapat">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobile-menu">
            <div class="mobile-menu-header">
                <div class="mobile-brand">
                    <i class="fas fa-balance-scale"></i>
                    <span>LegalAI</span>
                </div>
                <button class="mobile-menu-close" id="mobile-menu-close" type="button" aria-label="Menüyü kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mobile-menu-content">
                <div class="mobile-nav-section">
                    <a href="/" class="mobile-nav-link">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </div>

                <!-- Mobile User Menu (when logged in) -->
                <div class="mobile-nav-section" id="mobile-user-section" style="display: none;">
                    <div class="mobile-user-info">
                        <div class="mobile-user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="mobile-user-details">
                            <span class="mobile-user-name" id="mobile-user-name">Kullanıcı</span>
                            <span class="mobile-user-status">Aktif Üye</span>
                        </div>
                    </div>

                    <a href="/profile" class="mobile-nav-link">
                        <i class="fas fa-user-circle"></i>
                        <span>Profil</span>
                    </a>

                    <button class="mobile-nav-link logout-btn" id="mobile-logout-btn" type="button">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Çıkış Yap</span>
                    </button>
                </div>

                <!-- Mobile Auth Buttons (when not logged in) -->
                <div class="mobile-nav-section" id="mobile-auth-section">
                    <div class="mobile-auth-buttons">
                        <a href="/apply" class="mobile-auth-btn primary">
                            <i class="fas fa-user-plus"></i>
                            <span>Üye Ol</span>
                        </a>
                        <a href="/login" class="mobile-auth-btn secondary">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Giriş Yap</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Container -->
    <div class="main-container">
        <div class="login-wrapper">
            <!-- Hero Panel -->
            <div class="hero-panel">
                <div class="hero-content">
                    <div class="hero-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h1 class="hero-title">Tekrar Hoş Geldiniz</h1>
                    <p class="hero-subtitle">LegalAI hesabınıza giriş yapın ve hukuki belgelerinizi özetlemeye devam edin</p>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="fas fa-shield-check feature-icon"></i>
                            <span class="feature-text">Güvenli Giriş</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-bolt feature-icon"></i>
                            <span class="feature-text">Hızlı Erişim</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-user-shield feature-icon"></i>
                            <span class="feature-text">Kişisel Hesap</span>
                        </div>
                    </div>
                    
                    <p style="opacity: 0.8; font-size: 0.9rem;">
                        Hesabınız yok mu? 
                        <a href="/apply" style="color: white; text-decoration: underline; font-weight: 600;">Başvuru yapın</a>
                    </p>
                </div>
            </div>
            
            <!-- Login Panel -->
            <div class="login-panel">
                <div class="login-header">
                    <h2 class="login-title">Giriş Yap</h2>
                    <p class="login-subtitle">Hesabınıza erişim sağlayın</p>
                </div>
                
                <form class="login-form" id="login-form">
                    <div class="form-group">
                        <label for="email" class="form-label">E-posta</label>
                        <input type="email" id="email" name="email" required 
                               class="form-input" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">Şifre</label>
                        <input type="password" id="password" name="password" required 
                               class="form-input" placeholder="Şifrenizi girin">
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="remember" name="remember" class="checkbox-input">
                        <label for="remember" class="checkbox-label">Beni hatırla</label>
                    </div>
                    
                    <button type="submit" id="login-submit" class="login-button">
                        <span id="btn-text">
                            <i class="fas fa-sign-in-alt" style="margin-right: 0.5rem;"></i>
                            Giriş Yap
                        </span>
                        <span id="loading-spinner" style="display: none; margin-left: 0.5rem;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>
                    
                    <div class="forgot-link">
                        <a href="#" id="forgot-password-link">Şifrenizi mi unuttunuz?</a>
                    </div>
                </form>

                <!-- Error/Success Messages - Form'un hemen altında -->
                <div id="login-error" class="error-message" style="display: none; margin-top: 1rem; margin-bottom: 1rem;"></div>

                <div class="signup-link">
                    Hesabınız yok mu? <a href="/apply">Başvuru yapın</a>
                </div>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
    <script src="mobile-menu.js"></script>
</body>
</html>
