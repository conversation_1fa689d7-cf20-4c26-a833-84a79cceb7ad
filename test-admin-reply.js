// Test script - Admin yanıt ekleme ve e-posta debug
const jwt = require('jsonwebtoken');

// JWT Secret (gerçek değeri .env'den alın)
const JWT_SECRET = 'K8mN9pQ2rS5tU7vW0xY3zA6bC9dE2fG5'; // .env.example'dan

// Admin token oluştur
const adminToken = jwt.sign({
    adminId: 'ed53610b-d41f-4467-bae4-2ba8763d669e',
    email: '<EMAIL>',
    role: 'super_admin',
    fullName: 'Admin',
    exp: Math.floor((Date.now() + 86400000) / 1000) // 24 saat
}, JWT_SECRET);

console.log('Admin Token:', adminToken);

// Doğrudan fonksiyon test et
async function testAdminReplyDirect() {
    try {
        // Support-add-reply fonksiyon<PERSON>u do<PERSON><PERSON><PERSON> ça<PERSON>ır
        const supportAddReply = require('./netlify/functions/support-add-reply.js');

        const mockEvent = {
            httpMethod: 'POST',
            headers: {
                'authorization': `Bearer ${adminToken}`,
                'content-type': 'application/json',
                'x-forwarded-for': '127.0.0.1'
            },
            body: JSON.stringify({
                ticketId: 'd8935b7d-3e26-479f-bd0b-0c071be9239b',
                message: 'Test admin yanıtı - e-posta debug için',
                isAdminReply: true
            })
        };

        const mockContext = {};

        console.log('Calling support-add-reply function directly...');
        const result = await supportAddReply.handler(mockEvent, mockContext);

        console.log('Function Result:', JSON.stringify(result, null, 2));

    } catch (error) {
        console.error('Direct test error:', error);
    }
}

// Token'ı göster ve test fonksiyonunu çağır
console.log('\n=== ADMIN TOKEN ===');
console.log(adminToken);
console.log('\n=== TESTING ADMIN REPLY DIRECT ===');
testAdminReplyDirect();
