const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Yetkilendirme gerekli' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token doğrulama
        let adminData;
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                adminId: decoded.adminId,
                email: decoded.email,
                role: decoded.role
            };
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token' })
            };
        }

        if (event.httpMethod === 'GET') {
            // Email verification durumlarını getir
            const action = event.queryStringParameters?.action || 'overview';

            if (action === 'overview') {
                return await getEmailOverview(headers);
            } else if (action === 'pending') {
                return await getPendingVerifications(headers);
            } else if (action === 'logs') {
                return await getVerificationLogs(headers);
            } else if (action === 'attempts') {
                return await getVerificationAttempts(headers);
            }
        }

        if (event.httpMethod === 'POST') {
            const requestBody = JSON.parse(event.body);
            const { action } = requestBody;

            if (action === 'resend_verification') {
                return await resendVerificationEmail(requestBody, adminData, headers);
            } else if (action === 'manual_verify') {
                return await manualVerifyEmail(requestBody, adminData, headers);
            } else if (action === 'mark_verified') {
                return await markApplicationEmailVerified(requestBody, adminData, headers);
            } else if (action === 'block_email') {
                return await blockEmail(requestBody, adminData, headers);
            } else if (action === 'unblock_email') {
                return await unblockEmail(requestBody, adminData, headers);
            }
        }

        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };

    } catch (error) {
        console.error('Admin email management error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası' })
        };
    }
};

// Email overview
async function getEmailOverview(headers) {
    try {
        // Toplam istatistikler
        const { data: totalUsers, error: totalError } = await supabase
            .from('users')
            .select('id, email_verified_at, status', { count: 'exact' });

        const { data: verificationLogs, error: logsError } = await supabase
            .from('email_verification_logs')
            .select('status', { count: 'exact' });

        const { data: recentLogs, error: recentError } = await supabase
            .from('email_verification_logs')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(10);

        const stats = {
            totalUsers: totalUsers?.length || 0,
            verifiedUsers: totalUsers?.filter(u => u.email_verified_at).length || 0,
            pendingVerification: totalUsers?.filter(u => !u.email_verified_at).length || 0,
            approvedUsers: totalUsers?.filter(u => u.status === 'approved').length || 0,
            emailsSent: verificationLogs?.filter(l => l.status === 'sent').length || 0,
            emailsVerified: verificationLogs?.filter(l => l.status === 'verified').length || 0,
            emailsExpired: verificationLogs?.filter(l => l.status === 'expired').length || 0
        };

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                stats: stats,
                recentLogs: recentLogs || []
            })
        };

    } catch (error) {
        console.error('Get email overview error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'İstatistikler getirilemedi' })
        };
    }
}

// Pending verifications
async function getPendingVerifications(headers) {
    try {
        const { data: pendingUsers, error } = await supabase
            .from('users')
            .select(`
                id, email, full_name, created_at, email_verified_at,
                email_verification_logs (
                    verification_token, sent_at, status
                )
            `)
            .is('email_verified_at', null)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Get pending verifications error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Bekleyen doğrulamalar getirilemedi' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                pendingUsers: pendingUsers || []
            })
        };

    } catch (error) {
        console.error('Get pending verifications error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Bekleyen doğrulamalar getirilemedi' })
        };
    }
}

// Verification logs
async function getVerificationLogs(headers) {
    try {
        const { data: logs, error } = await supabase
            .from('email_verification_logs')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(100);

        if (error) {
            console.error('Get verification logs error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Doğrulama logları getirilemedi' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                logs: logs || []
            })
        };

    } catch (error) {
        console.error('Get verification logs error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Doğrulama logları getirilemedi' })
        };
    }
}

// Verification attempts
async function getVerificationAttempts(headers) {
    try {
        const { data: attempts, error } = await supabase
            .from('email_verification_attempts')
            .select('*')
            .order('last_attempt_at', { ascending: false })
            .limit(100);

        if (error) {
            console.error('Get verification attempts error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Doğrulama denemeleri getirilemedi' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                attempts: attempts || []
            })
        };

    } catch (error) {
        console.error('Get verification attempts error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Doğrulama denemeleri getirilemedi' })
        };
    }
}

// Resend verification email
async function resendVerificationEmail(requestBody, adminData, headers) {
    try {
        const { applicationId, email } = requestBody;

        if (!applicationId || !email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Application ID ve email gerekli' })
            };
        }

        // Send verification email fonksiyonunu çağır
        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-verification-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        });

        const result = await response.json();

        if (!response.ok) {
            return {
                statusCode: response.status,
                headers,
                body: JSON.stringify({ error: result.error || 'Email gönderilemedi' })
            };
        }

        // Admin log ekle
        try {
            await supabase.from('admin_logs').insert([{
                admin_id: adminData.adminId,
                admin_email: adminData.email,
                action: 'resend_verification_email',
                target_type: 'user',
                target_id: userId,
                details: { email: email },
                ip_address: 'admin_panel',
                user_agent: 'admin_panel'
            }]);
        } catch (logError) {
            console.error('Admin log error:', logError);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Doğrulama emaili yeniden gönderildi'
            })
        };

    } catch (error) {
        console.error('Resend verification email error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Email yeniden gönderilemedi' })
        };
    }
}

// Manual verify email
async function manualVerifyEmail(requestBody, adminData, headers) {
    try {
        const { userId, email } = requestBody;

        if (!userId || !email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı ID ve email gerekli' })
            };
        }

        // Auth user'ı manuel olarak doğrula
        const { error: confirmError } = await supabase.auth.admin.updateUserById(userId, {
            email_confirm: true
        });

        if (confirmError) {
            console.error('Manual email confirmation error:', confirmError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Email doğrulama işlemi başarısız' })
            };
        }

        // Users tablosunu güncelle
        await supabase
            .from('users')
            .update({ 
                email_verified_at: new Date().toISOString(),
                status: 'pending'
            })
            .eq('id', userId);

        // Admin log ekle
        try {
            await supabase.from('admin_logs').insert([{
                admin_id: adminData.adminId,
                admin_email: adminData.email,
                action: 'manual_verify_email',
                target_type: 'user',
                target_id: userId,
                details: { email: email },
                ip_address: 'admin_panel',
                user_agent: 'admin_panel'
            }]);
        } catch (logError) {
            console.error('Admin log error:', logError);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Email adresi manuel olarak doğrulandı'
            })
        };

    } catch (error) {
        console.error('Manual verify email error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Manuel doğrulama başarısız' })
        };
    }
}

// Email doğrulama mailini tekrar gönder (admin tarafından)
async function resendVerificationEmailByAdmin(email, adminData, headers) {
    try {
        console.log('Admin resending verification email for:', email);

        // Verification email gönder
        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-verification-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                resend: true,
                adminTriggered: true
            })
        });

        const result = await response.json();

        if (result.success) {
            // Admin log ekle
            await supabase.from('admin_logs').insert([{
                admin_id: adminData.id,
                admin_email: adminData.email,
                action: 'verification_email_resent_by_admin',
                target_type: 'email',
                target_id: email,
                details: {
                    email: email,
                    triggered_by: 'admin'
                },
                ip_address: 'admin_panel',
                user_agent: 'admin_action'
            }]);

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Doğrulama e-postası admin tarafından tekrar gönderildi.'
                })
            };
        } else {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: result.error || 'Email gönderimi başarısız.'
                })
            };
        }

    } catch (error) {
        console.error('Admin resend verification email error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Email gönderimi başarısız.' })
        };
    }
}

// Application email'ini admin tarafından doğrulanmış olarak işaretle
async function markApplicationEmailVerified(requestBody, adminData, headers) {
    try {
        const { applicationId, email } = requestBody;

        if (!applicationId || !email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Application ID ve email gerekli' })
            };
        }

        console.log('Marking application email as verified by admin:', { applicationId, email });

        // Application'da email_verified_at güncelle
        const { error: updateError } = await supabase
            .from('applications')
            .update({
                email_verified_at: new Date().toISOString()
            })
            .eq('id', applicationId);

        if (updateError) {
            console.error('Update application error:', updateError);
            throw updateError;
        }

        // Email verification log oluştur/güncelle
        const { data: existingLog } = await supabase
            .from('email_verification_logs')
            .select('*')
            .eq('email', email)
            .single();

        if (existingLog) {
            // Mevcut log'u güncelle
            await supabase
                .from('email_verification_logs')
                .update({
                    status: 'verified',
                    verified_at: new Date().toISOString()
                })
                .eq('id', existingLog.id);
        } else {
            // Yeni log oluştur
            await supabase
                .from('email_verification_logs')
                .insert([{
                    email: email,
                    user_id: null,
                    verification_token: 'admin_verified_' + Date.now(),
                    status: 'verified',
                    verified_at: new Date().toISOString()
                }]);
        }

        // Admin log ekle
        await supabase.from('admin_logs').insert([{
            admin_id: adminData.adminId,
            admin_email: adminData.email,
            action: 'application_email_marked_verified',
            target_type: 'application',
            target_id: applicationId,
            details: {
                email: email,
                verified_by: 'admin_override'
            },
            ip_address: 'admin_panel',
            user_agent: 'admin_action'
        }]);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Email admin tarafından doğrulanmış olarak işaretlendi.'
            })
        };

    } catch (error) {
        console.error('Mark application email as verified error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Email doğrulama işlemi başarısız.' })
        };
    }
}
