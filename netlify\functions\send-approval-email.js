const { sendEmail } = require('./utils/email-service');
const { getAccountApprovedEmailTemplate } = require('./email-templates/account-approved');
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY
);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const requestBody = JSON.parse(event.body);
        const { email, firstName, lastName } = requestBody;

        // DETAYLI LOGLAMA - SORUN TESPİTİ İÇİN
        console.log('=== SEND-APPROVAL-EMAIL DEBUG INFO ===');
        console.log('Raw request body:', event.body);
        console.log('Parsed request body:', JSON.stringify(requestBody, null, 2));
        console.log('Extracted email:', email);
        console.log('Extracted firstName:', firstName);
        console.log('Extracted lastName:', lastName);
        console.log('Request headers:', JSON.stringify(event.headers, null, 2));
        console.log('=== END SEND-APPROVAL-EMAIL DEBUG INFO ===');

        if (!email || !firstName || !lastName) {
            console.error('Missing required fields:', { email: !!email, firstName: !!firstName, lastName: !!lastName });
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    error: 'E-posta, ad ve soyad gerekli'
                })
            };
        }

        console.log('Sending approval email to:', email);

        // Email ayarlarını Supabase'den al
        console.log('🔧 Fetching email settings from Supabase...');
        const { data: emailSettings, error: settingsError } = await supabase
            .from('system_settings')
            .select('setting_key, setting_value, setting_type')
            .in('setting_key', [
                'email_enabled',
                'email_service_provider',
                'email_test_mode',
                'email_test_address',
                'brevo_api_key',
                'brevo_sender_email',
                'brevo_sender_name',
                'smtp_host',
                'smtp_port',
                'smtp_username',
                'smtp_password',
                'smtp_secure',
                'smtp_sender_email',
                'smtp_sender_name'
            ])
            .eq('is_active', true);

        if (settingsError) {
            console.error('❌ Failed to fetch email settings:', settingsError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Email ayarları alınamadı',
                    details: settingsError.message
                })
            };
        }

        console.log('✅ Email settings fetched:', emailSettings?.length, 'settings');

        // E-posta template'ini hazırla
        const emailHtml = getAccountApprovedEmailTemplate(firstName, lastName);

        // E-posta gönder
        console.log('🚀 Calling sendEmail function...');
        const emailResult = await sendEmail({
            to: email,
            subject: '🎉 Hesabınız Onaylandı - LegalAI',
            html: emailHtml,
            headers: {
                'X-Priority': '1',
                'X-MSMail-Priority': 'High',
                'Importance': 'high'
            },
            text: `🎉 HESABINIZ ONAYLANDI!

Merhaba ${firstName} ${lastName},

Harika haber! Hesabınız başarıyla onaylandı ve artık LegalAI sisteminin tüm özelliklerini kullanabilirsiniz.

🚀 ARTIK NELER YAPABİLİRSİNİZ?

✅ Hukuki belgeleri anında özetleyebilirsiniz
✅ PDF dosyalarını yükleyip analiz edebilirsiniz
✅ Günlük özet limitinizi kullanabilirsiniz
✅ Gelişmiş AI destekli analiz özelliklerinden faydalanabilirsiniz

🔐 SİSTEME GİRİŞ YAPIN:
https://hukukibelgeozetleme.netlify.app/login

Herhangi bir sorunuz olursa, destek ekibimizle iletişime geçmekten çekinmeyin. Size yardımcı olmaktan mutluluk duyarız.

---
LegalAI - Hukuki Belge Özetleme Sistemi
Bu e-posta otomatik olarak gönderilmiştir.
© 2025 LegalAI. Tüm hakları saklıdır.`
        }, emailSettings);

        console.log('📧 Email result received:', JSON.stringify(emailResult, null, 2));

        if (!emailResult.success) {
            console.error('❌ Email send error:', emailResult.error);
            console.error('❌ Email result details:', JSON.stringify(emailResult, null, 2));
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Email gönderilemedi',
                    details: emailResult.error
                })
            };
        }

        console.log('✅ Approval email sent successfully via', emailResult.provider);
        console.log('✅ Email ID:', emailResult.emailId);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Onay e-postası başarıyla gönderildi',
                emailId: emailResult.emailId,
                provider: emailResult.provider
            })
        };

    } catch (error) {
        console.error('Send approval email error:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'E-posta gönderilirken hata oluştu',
                details: error.message
            })
        };
    }
};
