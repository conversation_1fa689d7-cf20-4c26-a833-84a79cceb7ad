// Admin başvuru yönetimi function'ı
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    try {
        // Admin token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Admin token gerekli.' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token kontrolü
        let adminData;
        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                username: decoded.email, // email'i username olarak kullan
                email: decoded.email,
                id: decoded.adminId
            };
            console.log('Token verified for admin:', decoded.email);
        } catch (error) {
            console.error('Token verification failed:', error.message);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // GET - Başvuruları listele
        if (event.httpMethod === 'GET') {
            // Sadece henüz users tablosuna taşınmamış başvuruları getir (status != 'approved')
            const { data: applications, error } = await supabase
                .from('applications')
                .select('*')
                .neq('status', 'approved') // Approved olanlar users tablosuna taşınmış
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Applications fetch error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Başvurular getirilemedi.' })
                };
            }

            // E-posta doğrulama durumlarını ayrı ayrı getir
            const applicationsWithEmailStatus = await Promise.all(
                applications.map(async (app) => {
                    const { data: emailLog } = await supabase
                        .from('email_verification_logs')
                        .select('status, verified_at')
                        .eq('email', app.email)
                        .order('created_at', { ascending: false })
                        .limit(1)
                        .single();

                    const isEmailVerified = emailLog?.status === 'verified' || app.email_verified_at;
                    console.log(`Application ${app.email}: emailLog=${emailLog?.status}, app.email_verified_at=${app.email_verified_at}, isEmailVerified=${isEmailVerified}`);

                    return {
                        ...app,
                        email_verified: isEmailVerified,
                        email_verified_at: emailLog?.verified_at || app.email_verified_at
                    };
                })
            );

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({ applications: applicationsWithEmailStatus })
            };
        }

        // PUT - Başvuru onaylama/reddetme
        if (event.httpMethod === 'PUT') {
            console.log('PUT request received');
            const body = JSON.parse(event.body || '{}');
            const { applicationId, action, reason } = body;

            console.log('Request data:', { applicationId, action, reason });

            if (!applicationId || !action) {
                console.log('Missing required fields');
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Başvuru ID ve aksiyon gerekli.' })
                };
            }

            if (action === 'approve') {
                console.log('Starting approve process for:', applicationId);

                // Başvuruyu getir - GÜVENLIK KONTROLÜ İLE
                console.log('Fetching application with ID:', applicationId, 'Type:', typeof applicationId);

                const { data: application, error: fetchError } = await supabase
                    .from('applications')
                    .select('*')
                    .eq('id', applicationId)
                    .single();

                console.log('Fetch result:', { application, fetchError });

                // GÜVENLIK KONTROLÜ: ID'lerin eşleşip eşleşmediğini kontrol et
                if (application && application.id !== applicationId) {
                    console.error('CRITICAL ERROR: ID MISMATCH!', {
                        requestedId: applicationId,
                        fetchedId: application.id,
                        requestedIdType: typeof applicationId,
                        fetchedIdType: typeof application.id
                    });
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({ error: 'Veri tutarsızlığı tespit edildi.' })
                    };
                }

                if (fetchError || !application) {
                    console.log('Application not found');
                    return {
                        statusCode: 404,
                        headers,
                        body: JSON.stringify({ error: 'Başvuru bulunamadı.' })
                    };
                }

                // E-posta doğrulanmış mı kontrol et
                console.log('Checking email verification for:', application.email);
                console.log('Application email_verified_at:', application.email_verified_at);

                const { data: emailLog, error: emailLogError } = await supabase
                    .from('email_verification_logs')
                    .select('status, verified_at')
                    .eq('email', application.email)
                    .eq('status', 'verified')
                    .single();

                console.log('Email log result:', { emailLog, emailLogError });

                // E-posta doğrulanmış mı kontrol et (hem log'dan hem de application'dan)
                const isEmailVerified = (emailLog && emailLog.status === 'verified') || application.email_verified_at;

                console.log('Final email verification check:', {
                    email: application.email,
                    emailLogStatus: emailLog?.status,
                    applicationEmailVerifiedAt: application.email_verified_at,
                    isEmailVerified: isEmailVerified
                });

                // E-posta doğrulama kontrolü - sadece hiç doğrulama yapılmamışsa engelle
                if (!emailLog && !application.email_verified_at) {
                    console.log('Email not verified for:', application.email);
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'Bu başvuru henüz e-posta doğrulaması yapmamış.' })
                    };
                }

                console.log('Email verification confirmed for:', application.email);

                // Kullanıcı oluştur
                console.log('Creating user with application data:', {
                    first_name: application.first_name,
                    last_name: application.last_name,
                    email: application.email,
                    phone: application.phone,
                    profession: application.profession
                });

                // Kullanıcının orijinal şifre hash'ini kullan
                const userData = {
                    first_name: application.first_name || 'Ad',
                    last_name: application.last_name || 'Soyad',
                    email: application.email,
                    password_hash: application.password_hash, // Orijinal şifre hash'i!
                    status: 'approved',
                    email_verified_at: emailLog?.verified_at || application.email_verified_at || new Date().toISOString()
                };

                // Opsiyonel alanları ekle (eğer varsa)
                if (application.phone) userData.phone = application.phone;
                if (application.profession) userData.profession = application.profession;

                console.log('Final userData to insert:', userData);

                const { data: newUser, error: userError } = await supabase
                    .from('users')
                    .insert([userData])
                    .select()
                    .single();

                console.log('User creation result:', { newUser, userError });

                if (userError) {
                    console.error('User creation error details:', userError);
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'Kullanıcı oluşturulurken hata oluştu.',
                            details: userError.message,
                            code: userError.code
                        })
                    };
                }

                // Başvuruyu güncelle
                const { error: updateError } = await supabase
                    .from('applications')
                    .update({
                        status: 'approved',
                        processed_at: new Date().toISOString(),
                        processed_by: adminData.username
                    })
                    .eq('id', applicationId);

                if (updateError) {
                    console.error('Application update error:', updateError);
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({ error: 'Başvuru güncellenirken hata oluştu.' })
                    };
                }

                // Onay e-postası gönder
                try {
                    // DETAYLI LOGLAMA - SORUN TESPİTİ İÇİN
                    console.log('=== EMAIL SENDING DEBUG INFO ===');
                    console.log('Request applicationId:', applicationId);
                    console.log('Fetched application ID:', application.id);
                    console.log('Application email:', application.email);
                    console.log('Application first_name:', application.first_name);
                    console.log('Application last_name:', application.last_name);
                    console.log('Application created_at:', application.created_at);
                    console.log('Full application object:', JSON.stringify(application, null, 2));
                    console.log('=== END DEBUG INFO ===');

                    const emailPayload = {
                        email: application.email,
                        firstName: application.first_name,
                        lastName: application.last_name
                    };

                    console.log('Email payload being sent:', JSON.stringify(emailPayload, null, 2));

                    // GÜVENLIK KONTROLÜ: Email adresinin application ID ile eşleştiğini doğrula
                    const { data: verifyApplication, error: verifyError } = await supabase
                        .from('applications')
                        .select('email, first_name, last_name')
                        .eq('id', applicationId)
                        .single();

                    if (verifyError || !verifyApplication) {
                        console.error('VERIFICATION FAILED: Cannot re-fetch application for verification');
                        throw new Error('Email verification failed');
                    }

                    if (verifyApplication.email !== emailPayload.email ||
                        verifyApplication.first_name !== emailPayload.firstName ||
                        verifyApplication.last_name !== emailPayload.lastName) {
                        console.error('CRITICAL SECURITY ERROR: Email payload mismatch!', {
                            original: emailPayload,
                            verified: verifyApplication,
                            applicationId: applicationId
                        });
                        throw new Error('Email data mismatch detected - email sending aborted for security');
                    }

                    console.log('✅ Email payload verified successfully');

                    const emailResponse = await fetch(`${event.headers.origin || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-approval-email`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(emailPayload)
                    });

                    console.log('Email response status:', emailResponse.status);
                    console.log('Email response ok:', emailResponse.ok);

                    if (emailResponse.ok) {
                        const emailResponseData = await emailResponse.json();
                        console.log('✅ Approval email sent successfully:', emailResponseData);
                    } else {
                        const errorText = await emailResponse.text();
                        console.error('❌ Failed to send approval email. Status:', emailResponse.status);
                        console.error('❌ Error response:', errorText);

                        // Email hatası olduğunda kullanıcıya bildir
                        return {
                            statusCode: 200,
                            headers,
                            body: JSON.stringify({
                                success: true,
                                message: 'Başvuru onaylandı ve kullanıcı oluşturuldu, ancak bilgilendirme e-postası gönderilemedi.',
                                userId: newUser.id,
                                emailError: errorText
                            })
                        };
                    }
                } catch (emailError) {
                    console.error('❌ Email sending error (catch block):', emailError);
                    console.error('❌ Email error stack:', emailError.stack);

                    // Email hatası olduğunda kullanıcıya bildir
                    return {
                        statusCode: 200,
                        headers,
                        body: JSON.stringify({
                            success: true,
                            message: 'Başvuru onaylandı ve kullanıcı oluşturuldu, ancak bilgilendirme e-postası gönderilemedi.',
                            userId: newUser.id,
                            emailError: emailError.message
                        })
                    };
                }

                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify({
                        success: true,
                        message: 'Başvuru onaylandı, kullanıcı oluşturuldu ve bilgilendirme e-postası gönderildi.',
                        userId: newUser.id
                    })
                };

            } else if (action === 'reject') {
                console.log('Starting reject process for:', applicationId);

                if (!reason) {
                    console.log('Missing reject reason');
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'Red nedeni gerekli.' })
                    };
                }

                console.log('Rejecting with reason:', reason);

                // Başvuruyu reddet
                const { error } = await supabase
                    .from('applications')
                    .update({
                        status: 'rejected',
                        processed_at: new Date().toISOString(),
                        processed_by: adminData.username,
                        reject_reason: reason
                    })
                    .eq('id', applicationId);

                console.log('Reject update result:', { error });

                if (error) {
                    console.error('Reject error:', error);
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({ error: 'Başvuru reddedilirken hata oluştu.' })
                    };
                }

                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify({ 
                        success: true, 
                        message: 'Başvuru reddedildi.' 
                    })
                };
            }

            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz aksiyon.' })
            };
        }

        // DELETE - Başvuru silme
        if (event.httpMethod === 'DELETE') {
            const body = JSON.parse(event.body || '{}');
            const { applicationId } = body;

            if (!applicationId) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Application ID gerekli.' })
                };
            }

            // Başvuruyu kontrol et
            const { data: application, error: appError } = await supabase
                .from('applications')
                .select('*')
                .eq('id', applicationId)
                .single();

            if (appError || !application) {
                return {
                    statusCode: 404,
                    headers,
                    body: JSON.stringify({ error: 'Başvuru bulunamadı.' })
                };
            }

            // İlgili kayıtları sil
            try {
                // Email verification logs'u sil
                await supabase
                    .from('email_verification_logs')
                    .delete()
                    .eq('email', application.email);

                // Email verification attempts'i sil
                await supabase
                    .from('email_verification_attempts')
                    .delete()
                    .eq('email', application.email);

                // Başvuruyu sil
                const { error: deleteError } = await supabase
                    .from('applications')
                    .delete()
                    .eq('id', applicationId);

                if (deleteError) {
                    throw deleteError;
                }

                // Admin log ekle
                await supabase.from('admin_logs').insert([{
                    admin_id: adminData.id,
                    admin_email: adminData.email,
                    action: 'application_deleted',
                    target_type: 'application',
                    target_id: applicationId,
                    details: {
                        email: application.email,
                        first_name: application.first_name,
                        last_name: application.last_name
                    },
                    ip_address: event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown',
                    user_agent: event.headers['user-agent'] || 'unknown'
                }]);

                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify({
                        success: true,
                        message: 'Başvuru başarıyla silindi.'
                    })
                };

            } catch (deleteError) {
                console.error('Delete error:', deleteError);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Silme işlemi başarısız.' })
                };
            }
        }

        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };

    } catch (error) {
        console.error('Admin applications error:', error);
        console.error('Error stack:', error.stack);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası.',
                details: error.message,
                stack: error.stack
            })
        };
    }
};
