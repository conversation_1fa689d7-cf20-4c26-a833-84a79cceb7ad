<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Hukuki Belge Özetleme</title>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Admin Login -->
    <div class="admin-container" id="admin-login">
        <div class="admin-card">
            <div class="admin-header">
                <i class="fas fa-shield-alt admin-icon"></i>
                <h1>Admin Panel</h1>
                <p>Hukuki Belge Özetleme Sistemi</p>
            </div>
            <form class="admin-form" id="admin-login-form">
                <div class="form-group">
                    <label for="admin-email">E-posta Adresi:</label>
                    <input type="email" id="admin-email" required autocomplete="username">
                </div>
                <div class="form-group">
                    <label for="admin-password">Şifre:</label>
                    <input type="password" id="admin-password" required autocomplete="current-password">
                </div>
                <button type="submit" class="admin-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Giriş Yap
                </button>
            </form>
            <div class="admin-error" id="admin-error" style="display: none;"></div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div class="admin-dashboard" id="admin-dashboard" style="display: none;">
        <nav class="admin-nav">
            <div class="admin-nav-brand">
                <i class="fas fa-shield-alt"></i>
                <span>Admin Panel</span>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="admin-nav-menu" id="admin-nav-menu">
                <button class="admin-nav-btn active" data-tab="applications">
                    <i class="fas fa-user-plus"></i>
                    <span class="nav-text">Yeni Başvurular</span>
                </button>
                <button class="admin-nav-btn" data-tab="users">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Kayıtlı Kullanıcılar</span>
                </button>
                <button class="admin-nav-btn" data-tab="support">
                    <i class="fas fa-headset"></i>
                    <span class="nav-text">Destek Talepleri</span>
                </button>
                <button class="admin-nav-btn" data-tab="stats">
                    <i class="fas fa-chart-bar"></i>
                    <span class="nav-text">İstatistikler</span>
                </button>
                <button class="admin-nav-btn" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span class="nav-text">Ayarlar</span>
                </button>
                <button class="admin-nav-btn" data-tab="email-settings">
                    <i class="fas fa-envelope-open-text"></i>
                    <span class="nav-text">Email Ayarları</span>
                </button>
                <button class="admin-logout-btn" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="nav-text">Çıkış</span>
                </button>
            </div>
        </nav>

        <main class="admin-main">
            <!-- Başvurular Tab -->
            <div class="admin-tab active" id="applications-tab">
                <div class="admin-section-header">
                    <h2>Yeni Başvurular</h2>
                    <p class="section-description">
                        <i class="fas fa-info-circle"></i>
                        Tüm yeni başvurular. E-posta doğrulanmış başvurular admin onayı bekler. Admin onayladığında "Kayıtlı Kullanıcılar" sekmesine taşınır.
                    </p>
                    <div class="admin-stats">
                        <span class="stat-badge">
                            <i class="fas fa-clock"></i>
                            <span id="pending-count">0</span> Bekleyen
                        </span>
                    </div>
                </div>
                <div class="applications-list" id="applications-list">
                    <!-- Başvurular buraya yüklenecek -->
                </div>
            </div>

            <!-- Kullanıcılar Tab -->
            <div class="admin-tab" id="users-tab">
                <div class="admin-section-header">
                    <h2>Kayıtlı Kullanıcılar</h2>
                    <p class="section-description">
                        <i class="fas fa-info-circle"></i>
                        E-posta doğrulaması yapmış kullanıcılar. Pending: Admin onayı bekliyor, Approved: Aktif kullanıcı, Suspended: Askıya alınmış.
                    </p>
                    <div class="admin-stats">
                        <span class="stat-badge">
                            <i class="fas fa-check"></i>
                            <span id="approved-count">0</span> Aktif
                        </span>
                        <span class="stat-badge">
                            <i class="fas fa-chart-line"></i>
                            <span id="daily-usage-count">0</span> Günlük Kullanım
                        </span>
                        <button class="reset-all-quotas-btn" onclick="resetAllQuotas()"
                                title="Tüm kullanıcıların günlük özet haklarını sıfırla">
                            <i class="fas fa-refresh"></i>
                            Tüm Hakları Sıfırla
                        </button>
                        <button class="cleanup-pending-btn" onclick="cleanupPendingUsers()"
                                title="Pending kullanıcıları applications tablosuna taşı">
                            <i class="fas fa-broom"></i>
                            Pending Kullanıcıları Temizle
                        </button>
                    </div>
                </div>
                <div class="users-list" id="users-list">
                    <!-- Kullanıcılar buraya yüklenecek -->
                </div>
            </div>

            <!-- İstatistikler Tab -->
            <div class="admin-tab" id="stats-tab">
                <div class="admin-section-header">
                    <h2>Sistem İstatistikleri</h2>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="total-summaries">0</div>
                            <div class="stat-label">Toplam Özet</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="total-users">0</div>
                            <div class="stat-label">Toplam Kullanıcı</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="today-summaries">0</div>
                            <div class="stat-label">Bugünkü Özetler</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="total-trials">0</div>
                            <div class="stat-label">Toplam Deneme</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="unique-ips">0</div>
                            <div class="stat-label">Benzersiz IP</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="avg-daily-limit">0</div>
                            <div class="stat-label">Ort. Günlük Limit</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="users-at-limit">0</div>
                            <div class="stat-label">Limitine Ulaşan</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number" id="today-trials">0</div>
                            <div class="stat-label">Bugünkü Denemeler</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Destek Talepleri Tab -->
            <div class="admin-tab" id="support-tab">
                <div class="admin-section-header">
                    <h2>Destek Talepleri</h2>
                    <p class="section-description">
                        <i class="fas fa-info-circle"></i>
                        Kullanıcıların gönderdiği destek taleplerini yönetin, yanıtlayın ve durumlarını güncelleyin.
                    </p>
                    <div class="admin-stats">
                        <span class="stat-badge">
                            <i class="fas fa-clock"></i>
                            <span id="open-tickets-count">0</span> Açık
                        </span>
                        <span class="stat-badge">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span id="urgent-tickets-count">0</span> Acil
                        </span>
                        <span class="stat-badge">
                            <i class="fas fa-reply"></i>
                            <span id="pending-replies-count">0</span> Yanıt Bekleyen
                        </span>
                    </div>
                </div>

                <!-- Filtreler -->
                <div class="support-filters" style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; align-items: end;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a5568;">Durum:</label>
                            <select id="support-status-filter" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 5px;">
                                <option value="">Tümü</option>
                                <option value="open">Açık</option>
                                <option value="in_progress">Devam Ediyor</option>
                                <option value="waiting">Beklemede</option>
                                <option value="resolved">Çözüldü</option>
                                <option value="closed">Kapatıldı</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a5568;">Öncelik:</label>
                            <select id="support-priority-filter" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 5px;">
                                <option value="">Tümü</option>
                                <option value="urgent">Acil</option>
                                <option value="high">Yüksek</option>
                                <option value="normal">Normal</option>
                                <option value="low">Düşük</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a5568;">Kategori:</label>
                            <select id="support-category-filter" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 5px;">
                                <option value="">Tümü</option>
                                <!-- Kategoriler dinamik yüklenecek -->
                            </select>
                        </div>
                        <div>
                            <button onclick="loadSupportTickets()" style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-search"></i> Filtrele
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Destek Talepleri Listesi -->
                <div class="support-tickets-list" id="support-tickets-list">
                    <!-- Destek talepleri buraya yüklenecek -->
                </div>
            </div>

            <!-- Ticket Detail Modal -->
            <div id="ticket-detail-modal" class="modal" style="display: none;">
                <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
                    <div class="modal-header">
                        <h2 id="ticket-modal-title">Ticket Detayı</h2>
                        <span class="modal-close" onclick="closeTicketDetailModal()">&times;</span>
                    </div>
                    <div class="modal-body" id="ticket-modal-body">
                        <!-- Ticket details will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Ayarlar Tab -->
            <div class="admin-tab" id="settings-tab">
                <div class="admin-section-header">
                    <h2>Sistem Ayarları</h2>
                </div>

                <!-- Şifre Değiştirme -->
                <div class="settings-section">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-key"></i> Şifre Değiştir</h3>
                            <p>Admin hesabınızın şifresini değiştirin</p>
                        </div>
                        <form class="password-change-form" id="password-change-form">
                            <div class="form-group">
                                <label for="current-password">Mevcut Şifre:</label>
                                <input type="password" id="current-password" required>
                            </div>
                            <div class="form-group">
                                <label for="new-password">Yeni Şifre:</label>
                                <input type="password" id="new-password" required minlength="6">
                            </div>
                            <div class="form-group">
                                <label for="confirm-password">Yeni Şifre (Tekrar):</label>
                                <input type="password" id="confirm-password" required minlength="6">
                            </div>
                            <button type="submit" class="settings-btn">
                                <i class="fas fa-save"></i>
                                Şifreyi Değiştir
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Admin Bilgileri -->
                <div class="settings-section">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-user"></i> Admin Bilgileri</h3>
                        </div>
                        <div class="admin-info" id="admin-info">
                            <!-- Admin bilgileri buraya yüklenecek -->
                        </div>
                    </div>
                </div>

                <!-- Webhook Ayarları -->
                <div class="settings-section">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-link"></i> Otomatik Hak Sıfırlama Webhook</h3>
                            <p>Günlük hak sıfırlama için güvenli webhook URL'i oluşturun</p>
                        </div>
                        <div class="webhook-section">
                            <div class="webhook-info">
                                <div class="info-box warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <div>
                                        <strong>Güvenlik Uyarısı:</strong>
                                        <p>Bu URL'i güvenli bir yerde saklayın. URL'e sahip olan herkes tüm kullanıcıların haklarını sıfırlayabilir.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="webhook-actions">
                                <div class="webhook-buttons">
                                    <button class="webhook-btn permanent" onclick="generatePermanentWebhookUrl()">
                                        <i class="fas fa-link"></i>
                                        Kalıcı Webhook URL Oluştur
                                    </button>
                                    <button class="webhook-btn temporary" onclick="generateTemporaryWebhookUrl()">
                                        <i class="fas fa-clock"></i>
                                        Geçici Webhook URL (24 saat)
                                    </button>
                                </div>
                                <div class="webhook-result" id="webhook-result" style="display: none;">
                                    <label id="webhook-label">Webhook URL:</label>
                                    <div class="url-container">
                                        <input type="text" id="webhook-url" readonly>
                                        <button class="copy-btn" onclick="copyWebhookUrl()">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <div class="webhook-usage">
                                        <h4>Kullanım:</h4>
                                        <ul id="webhook-usage-list">
                                            <li>Bu URL'i cron job servislerinde kullanabilirsiniz</li>
                                            <li>Günlük otomatik sıfırlama için zamanlanabilir</li>
                                            <li>GET veya POST request ile tetiklenebilir</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Ayarları -->
                <div class="settings-section">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-key"></i> API Ayarları</h3>
                            <p>Gemini API key'lerini yönetin ve sistem durumunu izleyin</p>
                        </div>
                        <div class="api-section">
                            <!-- Model Bilgisi ve Seçimi -->
                            <div class="model-info-section">
                                <div class="model-info-card" id="model-info-card">
                                    <!-- Model bilgisi buraya yüklenecek -->
                                </div>
                                <div class="model-selection-card" id="model-selection-card" style="display: none;">
                                    <!-- Model seçimi buraya yüklenecek -->
                                </div>
                            </div>

                            <!-- API Key Durumu -->
                            <div class="api-status-section">
                                <div class="section-title">
                                    <h4>API Key Durumu</h4>
                                    <button class="refresh-btn" onclick="loadApiStatus()">
                                        <i class="fas fa-sync-alt"></i>
                                        Yenile
                                    </button>
                                </div>
                                <div class="api-keys-grid" id="api-keys-grid">
                                    <!-- API key'ler buraya yüklenecek -->
                                </div>
                            </div>

                            <!-- API Logları -->
                            <div class="api-logs-section">
                                <div class="section-title">
                                    <h4>API Logları</h4>
                                    <div class="log-filters">
                                        <select id="log-filter-status">
                                            <option value="">Tüm Durumlar</option>
                                            <option value="success">Başarılı</option>
                                            <option value="error">Hatalı</option>
                                        </select>
                                        <select id="log-filter-key">
                                            <option value="">Tüm Key'ler</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="api-logs-container">
                                    <div class="api-logs-list" id="api-logs-list">
                                        <!-- API logları buraya yüklenecek -->
                                    </div>
                                </div>
                            </div>

                            <!-- API İstatistikleri -->
                            <div class="api-stats-section">
                                <div class="section-title">
                                    <h4>Son 24 Saat İstatistikleri</h4>
                                </div>
                                <div class="api-stats-grid" id="api-stats-grid">
                                    <!-- İstatistikler buraya yüklenecek -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Ayarları Tab -->
            <div class="admin-tab" id="email-settings-tab">
                <div class="admin-section-header">
                    <h2>Email Ayarları
                        <span class="email-status-indicator" id="email-status-indicator">
                            <i class="fas fa-circle"></i> <span id="email-status-text">Yükleniyor...</span>
                        </span>
                    </h2>
                    <p class="section-description">
                        <i class="fas fa-info-circle"></i>
                        Email servis sağlayıcısını seçin ve ayarlarını yapılandırın. Resend, Brevo veya SMTP kullanabilirsiniz.
                    </p>
                </div>





                <!-- Brevo Ayarları -->
                <div class="settings-section" id="brevo-settings" style="display: block;">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-envelope-open"></i> Brevo Ayarları</h3>
                            <p>Brevo (SendinBlue) API ayarlarını yapılandırın</p>
                        </div>
                        <div class="email-settings-form">
                            <div class="form-group">
                                <label for="brevo-api-key">API Key:</label>
                                <input type="password" id="brevo-api-key" placeholder="xkeysib-xxxxxxxxxx">
                                <small>Brevo dashboard'dan alacağınız API key</small>
                            </div>
                            <div class="form-group">
                                <label for="brevo-sender-email">Gönderen Email:</label>
                                <input type="email" id="brevo-sender-email" placeholder="<EMAIL>">
                                <small>Brevo'da doğrulanmış email adresi</small>
                            </div>
                            <div class="form-group">
                                <label for="brevo-sender-name">Gönderen İsim:</label>
                                <input type="text" id="brevo-sender-name" placeholder="LegalAI">
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Genel Email Ayarları -->
                <div class="settings-section">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-cogs"></i> Genel Ayarlar</h3>
                            <p>Email gönderimi genel ayarları</p>
                        </div>
                        <div class="email-settings-form">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="email-enabled" checked>
                                    <span class="checkmark"></span>
                                    Email gönderimi aktif
                                </label>
                                <small>Bu seçenek kapalıysa hiçbir email gönderilmez</small>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="email-test-mode">
                                    <span class="checkmark"></span>
                                    Test modu
                                </label>
                                <small>Test modunda tüm emailler test adresine gönderilir</small>
                            </div>
                            <div class="form-group">
                                <label for="email-test-address">Test Email Adresi:</label>
                                <input type="email" id="email-test-address" placeholder="<EMAIL>">
                                <small>Test modunda kullanılacak email adresi</small>
                            </div>
                            <div class="form-group">
                                <label for="admin-notification-emails">Admin Bildirim Emailleri:</label>
                                <textarea id="admin-notification-emails" rows="3" placeholder='["<EMAIL>", "<EMAIL>"]'></textarea>
                                <small>JSON array formatında admin email adresleri</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Test ve Kaydet -->
                <div class="settings-section">
                    <div class="settings-card">
                        <div class="settings-header">
                            <h3><i class="fas fa-check-circle"></i> Test ve Kaydet</h3>
                            <p>Ayarları test edin ve kaydedin</p>
                        </div>
                        <div class="email-actions">
                            <button class="test-email-btn" onclick="testEmailSettings()">
                                <i class="fas fa-paper-plane"></i>
                                Email Ayarlarını Test Et
                            </button>
                            <button class="save-email-btn" onclick="saveEmailSettings()">
                                <i class="fas fa-save"></i>
                                Ayarları Kaydet
                            </button>
                        </div>
                        <div class="email-test-result" id="email-test-result" style="display: none;">
                            <!-- Test sonucu buraya gelecek -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin.js"></script>
</body>
</html>
