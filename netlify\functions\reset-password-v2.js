const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');

exports.handler = async (event, context) => {
    console.log('Reset password v2 function called');
    
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Environment variables check
        if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
            console.error('Missing Supabase environment variables');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Server configuration error' })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { token, email, password, confirmPassword } = body;

        console.log('Reset password request:', { email, hasToken: !!token, hasPassword: !!password });

        if (!token || !email || !password || !confirmPassword) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Tüm alanlar gereklidir.' })
            };
        }

        // Şifre validasyonu
        if (password.length < 6) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Şifre en az 6 karakter olmalıdır.' })
            };
        }

        // Şifre eşleşme kontrolü
        if (password !== confirmPassword) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Şifreler eşleşmiyor.' })
            };
        }

        // Initialize Supabase
        const supabase = createClient(
            process.env.SUPABASE_URL,
            process.env.SUPABASE_SERVICE_ROLE_KEY
        );

        console.log('Supabase client initialized');

        // Token hash'i oluştur
        const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

        // Token'ı veritabanından kontrol et
        const { data: resetToken, error: tokenError } = await supabase
            .from('password_reset_tokens')
            .select('*')
            .eq('token_hash', tokenHash)
            .eq('email', email)
            .is('used_at', null)
            .gte('expires_at', new Date().toISOString())
            .single();

        console.log('Token validation:', { found: !!resetToken, error: tokenError });

        if (tokenError || !resetToken) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ 
                    error: 'Geçersiz veya süresi dolmuş şifre sıfırlama linki.' 
                })
            };
        }

        // Kullanıcıyı kontrol et
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, status')
            .eq('id', resetToken.user_id)
            .eq('email', email)
            .single();

        console.log('User validation:', { found: !!user, error: userError });

        if (userError || !user) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
            };
        }

        // Kullanıcı durumu kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ 
                    error: 'Hesabınız henüz onaylanmamış.' 
                })
            };
        }

        // Yeni şifreyi hash'le
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        console.log('Password hashed, updating user');

        // Şifreyi güncelle
        const { error: updateError } = await supabase
            .from('users')
            .update({ 
                password_hash: passwordHash,
                updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

        if (updateError) {
            console.error('Password update error:', updateError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Şifre güncellenirken hata oluştu.' })
            };
        }

        // Token'ı kullanıldı olarak işaretle
        await supabase
            .from('password_reset_tokens')
            .update({ 
                used_at: new Date().toISOString(),
                ip_address: event.headers['x-forwarded-for']?.split(',')[0] || 'unknown',
                user_agent: event.headers['user-agent'] || null
            })
            .eq('id', resetToken.id);

        // Kullanıcının diğer aktif token'larını da temizle
        await supabase
            .from('password_reset_tokens')
            .update({ used_at: new Date().toISOString() })
            .eq('user_id', user.id)
            .is('used_at', null)
            .neq('id', resetToken.id);

        console.log('Password reset successful:', { userId: user.id, email: user.email });

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Şifreniz başarıyla güncellendi. Artık yeni şifrenizle giriş yapabilirsiniz.'
            })
        };

    } catch (error) {
        console.error('Reset password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Şifre sıfırlanırken hata oluştu.',
                details: error.message 
            })
        };
    }
};
