// Destek Kaydı Detaylarını Getiren API Endpoint

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece GET metodunu kabul et
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Kullanıcının varlığını kontrol et
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, status')
            .eq('id', decoded.userId)
            .single();

        if (userError || !user) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
            };
        }

        // Kullanıcı onaylanmış mı kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({ 
                    error: 'Destek kayıtlarını görüntülemek için hesabınızın onaylanmış olması gerekir.' 
                })
            };
        }

        // Ticket ID'sini al
        const queryParams = event.queryStringParameters || {};
        const ticketId = queryParams.id;

        if (!ticketId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Ticket ID gerekli.' })
            };
        }

        // Ticket detaylarını getir (sadece kullanıcının kendi ticketı)
        const { data: ticket, error: ticketError } = await supabase
            .from('support_tickets')
            .select(`
                id,
                ticket_number,
                subject,
                description,
                priority,
                status,
                reply_count,
                last_reply_at,
                last_reply_by,
                is_read_by_user,
                created_at,
                updated_at,
                closed_at,
                close_reason,
                support_categories(
                    id,
                    name,
                    color,
                    icon
                )
            `)
            .eq('id', ticketId)
            .eq('user_id', user.id)
            .single();

        if (ticketError || !ticket) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'Ticket bulunamadı veya erişim yetkiniz yok.' })
            };
        }

        // Ticket yanıtlarını getir
        const { data: replies, error: repliesError } = await supabase
            .from('support_ticket_replies')
            .select(`
                id,
                message,
                is_admin_reply,
                is_internal_note,
                created_at,
                user_id,
                admin_id
            `)
            .eq('ticket_id', ticketId)
            .eq('is_internal_note', false) // Internal notları kullanıcıya gösterme
            .order('created_at', { ascending: true });

        if (repliesError) {
            console.error('Replies fetch error:', repliesError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Yanıtlar getirilemedi.' })
            };
        }

        // Admin bilgilerini al (yanıtlarda admin adı göstermek için)
        const adminIds = replies ? replies
            .filter(reply => reply.is_admin_reply && reply.admin_id)
            .map(reply => reply.admin_id) : [];

        let adminInfo = {};
        if (adminIds.length > 0) {
            const { data: admins, error: adminError } = await supabase
                .from('admins')
                .select('id, full_name')
                .in('id', [...new Set(adminIds)]);

            if (!adminError && admins) {
                adminInfo = admins.reduce((acc, admin) => {
                    acc[admin.id] = admin.full_name;
                    return acc;
                }, {});
            }
        }

        // Yanıtları formatla
        const formattedReplies = replies ? replies.map(reply => ({
            id: reply.id,
            message: reply.message,
            is_admin_reply: reply.is_admin_reply,
            created_at: reply.created_at,
            author_name: reply.is_admin_reply 
                ? (adminInfo[reply.admin_id] || 'Destek Ekibi')
                : `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Siz'
        })) : [];

        // Ticket'ı okundu olarak işaretle (eğer admin'den yanıt gelmişse)
        if (ticket.last_reply_by === 'admin' && !ticket.is_read_by_user) {
            await supabase
                .from('support_tickets')
                .update({ is_read_by_user: true })
                .eq('id', ticketId);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                ticket: {
                    ...ticket,
                    replies: formattedReplies
                }
            })
        };

    } catch (error) {
        console.error('Support ticket details error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}
