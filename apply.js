// Apply Form JavaScript - Single Step with Email Verification After Submit
document.addEventListener('DOMContentLoaded', function() {
    // FontAwesome kontrolünü devre dışı bırak - modern CSS ile iconlar çalışıyor
    // checkFontAwesome();
    setupApplyForm();
});

// FontAwesome fallback sistemi kaldırıldı - Modern CSS ile iconlar çalışıyor

function setupApplyForm() {
    const form = document.getElementById('apply-form');
    const submitBtn = document.getElementById('apply-submit');

    // Add floating label effect for modern UI
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            const formGroup = this.closest('.form-group');
            const label = formGroup ? formGroup.querySelector('label') : null;
            if (label) {
                label.classList.add('text-indigo-600');
            }
        });

        input.addEventListener('blur', function() {
            const formGroup = this.closest('.form-group');
            const label = formGroup ? formGroup.querySelector('label') : null;
            if (label) {
                label.classList.remove('text-indigo-600');
            }
        });
    });

    // Form validation
    form.addEventListener('input', validateForm);
    
    // Form submission
    form.addEventListener('submit', handleSubmit);
    
    // Password confirmation
    document.getElementById('confirm-password').addEventListener('input', checkPasswordMatch);
}

function validateForm() {
    const form = document.getElementById('apply-form');
    const submitBtn = document.getElementById('apply-submit');
    const formData = new FormData(form);
    
    let isValid = true;
    
    // Check required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'password', 'confirmPassword', 'profession', 'reason'];
    
    for (const field of requiredFields) {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            isValid = false;
            break;
        }
    }
    
    // Check password match
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    if (password !== confirmPassword) {
        isValid = false;
    }
    
    // Check terms
    const terms = formData.get('terms');
    if (!terms) {
        isValid = false;
    }
    
    submitBtn.disabled = !isValid;
}

function checkPasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const confirmInput = document.getElementById('confirm-password');
    
    if (confirmPassword && password !== confirmPassword) {
        confirmInput.style.borderColor = '#dc2626';
        confirmInput.setCustomValidity('Şifreler eşleşmiyor');
    } else {
        confirmInput.style.borderColor = '';
        confirmInput.setCustomValidity('');
    }
}

async function handleSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = document.getElementById('apply-submit');
    const btnText = document.getElementById('btn-text');
    const spinner = document.getElementById('loading-spinner');

    // Show loading state
    if (btnText && spinner) {
        btnText.style.display = 'none';
        spinner.style.display = 'inline-flex';
    }
    submitBtn.disabled = true;
    submitBtn.style.opacity = '0.8';
    submitBtn.style.cursor = 'not-allowed';

    try {
        // Önce başvuruyu kaydet
        const applicationData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            password: formData.get('password'),
            profession: formData.get('profession'),
            reason: formData.get('reason')
        };
        
        const response = await fetch('/.netlify/functions/user-apply', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(applicationData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Başvuru başarılı, şimdi e-posta doğrulama gönder
            await sendEmailVerification(applicationData.email);
        } else {
            showError(data.error || 'Başvuru gönderilirken hata oluştu.');
        }
        
    } catch (error) {
        console.error('Apply error:', error);
        showError('Başvuru gönderilirken hata oluştu.');
    } finally {
        // Hide loading state
        if (btnText && spinner) {
            btnText.style.display = 'inline-flex';
            spinner.style.display = 'none';
        }
        submitBtn.disabled = false;
        submitBtn.style.opacity = '1';
        submitBtn.style.cursor = 'pointer';
    }
}

// E-posta doğrulama gönder
async function sendEmailVerification(email) {
    try {
        console.log('Sending email verification to:', email);

        const response = await fetch('/.netlify/functions/send-verification-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showEmailVerificationSent(email);
        } else {
            showError(result.error || 'E-posta doğrulama gönderilemedi.');
        }

    } catch (error) {
        console.error('Email verification error:', error);
        showError('E-posta doğrulama gönderilirken hata oluştu.');
    }
}

function showEmailVerificationSent(email) {
    const formContainer = document.getElementById('apply-form-container');
    const successDiv = document.getElementById('apply-success');

    // Hide form and show success message
    formContainer.classList.add('hidden');
    successDiv.classList.remove('hidden');

    // Update success message
    const successTitle = successDiv.querySelector('h3');
    const successMessage = successDiv.querySelector('p');

    successTitle.textContent = 'Başvurunuz Alındı!';
    successMessage.innerHTML = `
        Başvurunuz başarıyla kaydedildi. <strong>${email}</strong> adresine e-posta doğrulama linki gönderildi.
        <br><br>
        <strong>Sonraki Adımlar:</strong>
        <br>1. E-posta kutunuzu kontrol edin
        <br>2. Doğrulama linkine tıklayın
        <br>3. Admin onayını bekleyin (24-48 saat)
    `;

    successDiv.style.display = 'block';
}

function showSuccess() {
    document.getElementById('apply-form').style.display = 'none';
    document.getElementById('apply-success').style.display = 'block';
}

function showError(message) {
    const errorDiv = document.getElementById('apply-error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');

    // Scroll to error
    errorDiv.scrollIntoView({ behavior: 'smooth' });

    // Hide after 10 seconds
    setTimeout(() => {
        errorDiv.classList.add('hidden');
    }, 10000);
}

// Email verification setup
function setupEmailVerification() {
    const emailForm = document.getElementById('email-verification-form');
    const resendBtn = document.getElementById('resend-verification-btn');
    const changeEmailBtn = document.getElementById('change-email-btn');

    if (emailForm) {
        emailForm.addEventListener('submit', handleEmailVerification);
    }

    if (resendBtn) {
        resendBtn.addEventListener('click', handleResendVerification);
    }

    if (changeEmailBtn) {
        changeEmailBtn.addEventListener('click', handleChangeEmail);
    }
}

// URL parametrelerini kontrol et
function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const verified = urlParams.get('verified');
    const email = urlParams.get('email');

    if (verified === 'true' && email) {
        // E-posta doğrulandı, 2. adıma geç
        verifiedEmail = email;
        showStep2();
    }
}

// E-posta doğrulama işlemi
async function handleEmailVerification(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const email = formData.get('email');
    const submitBtn = document.getElementById('send-verification-btn');

    if (!isValidEmail(email)) {
        showError('Lütfen geçerli bir e-posta adresi girin.');
        return;
    }

    try {
        // Buton durumunu güncelle
        setButtonLoading(submitBtn, true, 'Gönderiliyor...');

        const response = await fetch('/.netlify/functions/send-verification-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showVerificationSent(email);
            startApplyCooldown(60); // 60 saniye cooldown
        } else {
            showError(result.error || 'E-posta gönderilemedi. Lütfen tekrar deneyin.');
        }

    } catch (error) {
        console.error('Email verification error:', error);
        showError('Bağlantı hatası. Lütfen tekrar deneyin.');
    } finally {
        setButtonLoading(submitBtn, false, 'Doğrulama Kodu Gönder');
    }
}

// Doğrulama e-postası gönderildi durumunu göster
function showVerificationSent(email) {
    document.getElementById('email-verification-form').style.display = 'none';
    document.getElementById('verification-sent').style.display = 'block';
    document.getElementById('sent-email').textContent = email;
}

// Cooldown timer için global değişkenler
let applyCooldownTimer = null;
let applyCooldownSeconds = 0;

// Yeniden gönder işlemi
async function handleResendVerification() {
    const email = document.getElementById('sent-email').textContent;
    const resendBtn = document.getElementById('resend-verification-btn');

    // Cooldown kontrolü
    if (applyCooldownSeconds > 0) {
        showError(`Lütfen ${applyCooldownSeconds} saniye bekleyin.`);
        return;
    }

    try {
        setButtonLoading(resendBtn, true, 'Gönderiliyor...');

        const response = await fetch('/.netlify/functions/send-verification-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showSuccess('Doğrulama e-postası yeniden gönderildi.');
            startApplyCooldown(60); // 60 saniye cooldown
        } else {
            if (response.status === 429) {
                showError(result.error || 'Çok fazla deneme. Lütfen daha sonra tekrar deneyin.');
                startApplyCooldown(300); // 5 dakika cooldown rate limit durumunda
            } else {
                showError(result.error || 'E-posta gönderilemedi.');
            }
        }

    } catch (error) {
        console.error('Resend verification error:', error);
        showError('Bağlantı hatası. Lütfen tekrar deneyin.');
    } finally {
        if (applyCooldownSeconds === 0) {
            setButtonLoading(resendBtn, false, 'Yeniden Gönder');
        }
    }
}

function startApplyCooldown(seconds) {
    applyCooldownSeconds = seconds;
    const resendBtn = document.getElementById('resend-verification-btn');

    if (applyCooldownTimer) {
        clearInterval(applyCooldownTimer);
    }

    applyCooldownTimer = setInterval(() => {
        applyCooldownSeconds--;

        if (applyCooldownSeconds > 0) {
            resendBtn.disabled = true;
            resendBtn.innerHTML = `<i class="fas fa-clock"></i> Tekrar gönder (${applyCooldownSeconds}s)`;
        } else {
            resendBtn.disabled = false;
            resendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Yeniden Gönder';
            clearInterval(applyCooldownTimer);
            applyCooldownTimer = null;
        }
    }, 1000);
}

// E-posta değiştir
function handleChangeEmail() {
    document.getElementById('verification-sent').style.display = 'none';
    document.getElementById('email-verification-form').style.display = 'block';
    document.getElementById('email-verify').value = '';
    clearResendTimer();
}

// 2. adıma geç
function showStep2() {
    document.getElementById('step-1').style.display = 'none';
    document.getElementById('step-2').style.display = 'block';
    document.getElementById('verified-email').textContent = verifiedEmail;
    currentStep = 2;
}

// Eski timer sistemi kaldırıldı - artık startApplyCooldown kullanılıyor

// E-posta validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Buton loading durumu
function setButtonLoading(button, isLoading, text) {
    if (isLoading) {
        button.disabled = true;
        button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
    } else {
        button.disabled = false;
        button.innerHTML = `<i class="fas fa-paper-plane"></i> ${text}`;
    }
}
