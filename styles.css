/* Font Awesome Import - CDN'den yükleme */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css');

/* Font Awesome Override - Güçlendirilmiş */
.fa, .fas, .far, .fal, .fad, .fab, i[class*="fa-"] {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    display: inline-block !important;
    text-rendering: auto !important;
    speak: none !important;
}

.far {
    font-weight: 400 !important;
}

.fab {
    font-family: "Font Awesome 6 Brands", "Font Awesome 6 Pro" !important;
    font-weight: 400 !important;
}

/* Icon görünürl<PERSON><PERSON> zorlaması */
i[class*="fa-"] {
    min-width: 1em;
    text-align: center;
    vertical-align: baseline;
}

/* Spin animasyonu */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Reset ve Temel Stiller */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Mobil viewport fix */
@viewport {
    width: device-width;
    zoom: 1.0;
}

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

:root {
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;
    --gray-50: #f8fafc;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--light-color);
    min-height: 100vh;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 50;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.nav-icon {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: color 0.2s;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.nav-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
}

.nav-btn:hover {
    background: var(--primary-dark);
}

/* Mobile Navigation */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-700);
    cursor: pointer;
    padding: 0.75rem;
    z-index: 1001;
    position: relative;
    min-width: 48px;
    min-height: 48px;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.mobile-menu-toggle:active {
    background: var(--gray-200);
    transform: scale(0.95);
}

.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    background: var(--white);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    display: flex;
    flex-direction: column;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s;
    text-align: center;
}

.mobile-nav-link:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.mobile-nav-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.mobile-nav-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.mobile-nav-btn:hover {
    background: var(--primary-dark);
}

.mobile-nav-btn.secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.mobile-nav-btn.secondary:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* User Menu Dropdown */
.user-menu-dropdown {
    position: fixed;
    background: var(--white);
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--gray-200);
    min-width: 200px;
    z-index: 1000;
    overflow: hidden;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    color: var(--gray-700);
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.user-menu-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.user-menu-item i {
    width: 1.25rem;
    text-align: center;
    font-size: 0.875rem;
}

.user-menu-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 0.25rem 0;
}

/* Profile Page Styles */
.profile-body {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    min-height: 100vh;
    font-family: 'Inter', sans-serif;
}

.profile-container {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 2rem;
    min-height: calc(100vh - 4rem);
}

.profile-card {
    background: var(--white);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 800px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.profile-avatar {
    margin-bottom: 1rem;
}

.profile-avatar i {
    font-size: 4rem;
    color: var(--primary-color);
}

.profile-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.profile-header p {
    color: var(--gray-600);
    font-size: 1rem;
}

.profile-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.profile-section {
    background: var(--gray-50);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-header h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.section-header h3 i {
    width: 1.5rem;
    height: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.75rem;
}

.profile-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-item span {
    color: var(--gray-900);
    font-size: 1rem;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.status-badge {
    display: inline-block !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 1rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    width: fit-content !important;
}

.status-badge.active {
    background: #dcfce7 !important;
    color: #166534 !important;
    border-color: #bbf7d0 !important;
}

.status-badge.suspended {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border-color: #fecaca !important;
}

.status-badge.demo {
    background: #f3e8ff !important;
    color: #7c3aed !important;
    border-color: #ddd6fe !important;
}

.status-badge.pending {
    background: #fef3c7 !important;
    color: #d97706 !important;
    border-color: #fde68a !important;
}

.usage-reason {
    background: var(--white);
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
}

.usage-reason p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.profile-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.profile-btn {
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.profile-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.profile-btn.secondary:hover {
    background: var(--gray-200);
    transform: translateY(-1px);
}

.profile-btn.danger {
    background: var(--danger-color);
    color: var(--white);
}

.profile-btn.danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Access Control */
.access-control {
    padding: 5rem 0;
    background: var(--gray-50);
}

.access-card {
    max-width: 500px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 1.5rem;
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.access-icon {
    width: 5rem;
    height: 5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: var(--white);
    font-size: 2rem;
}

.access-card h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.access-card p {
    color: var(--gray-600);
    margin-bottom: 2rem;
    font-size: 1.125rem;
}

.access-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.access-btn {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s;
}

.access-btn.primary {
    background: var(--primary-color);
    color: var(--white);
}

.access-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.access-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.access-btn.secondary:hover {
    background: var(--gray-200);
    transform: translateY(-2px);
}

/* Trial Limit */
.trial-limit {
    padding: 5rem 0;
    background: var(--gray-50);
}

.trial-card {
    max-width: 600px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 1.5rem;
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.trial-icon {
    width: 5rem;
    height: 5rem;
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: var(--white);
    font-size: 2rem;
}

.trial-card h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.trial-card p {
    color: var(--gray-600);
    margin-bottom: 2rem;
    font-size: 1.125rem;
    line-height: 1.6;
}

.trial-benefits {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    text-align: left;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
}

.benefit-item i {
    color: var(--success-color);
    font-size: 1.25rem;
    width: 1.5rem;
    text-align: center;
}

.benefit-item span {
    font-weight: 500;
    color: var(--gray-700);
}

.trial-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.trial-btn {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s;
}

.trial-btn.primary {
    background: var(--primary-color);
    color: var(--white);
}

.trial-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.trial-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.trial-btn.secondary:hover {
    background: var(--gray-200);
    transform: translateY(-2px);
}

/* Admin Panel Styles */
.admin-body, .login-body, .apply-body, .verify-body {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Email Verification Styles */
.step-container {
    margin-bottom: 2rem;
}

.step-header {
    text-align: center;
    margin-bottom: 2rem;
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.step-header h2 {
    margin: 0.5rem 0;
    color: var(--gray-800);
    font-size: 1.5rem;
}

.step-header p {
    margin: 0;
    color: var(--gray-600);
    font-size: 1rem;
}

.email-verification-form {
    margin-bottom: 2rem;
}

.verify-btn {
    width: 100%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.verify-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.verify-btn:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
}

.verification-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #e0f2fe;
    border-radius: 0.5rem;
    color: #0277bd;
    font-size: 0.875rem;
}

.verification-sent {
    text-align: center;
    padding: 2rem;
    background: #f0fdf4;
    border-radius: 1rem;
    border: 2px solid #22c55e;
}

.success-message h3 {
    color: #15803d;
    margin: 1rem 0;
    font-size: 1.25rem;
}

.success-message p {
    color: #166534;
    margin-bottom: 1rem;
}

.email-display {
    font-weight: 600;
    color: var(--primary-color);
    background: var(--white);
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
    border: 1px solid var(--gray-200);
}

.verification-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 1.5rem 0;
}

.resend-btn,
.change-email-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.resend-btn {
    background: var(--warning-color);
    color: var(--white);
}

.resend-btn:hover {
    background: #d97706;
}

.resend-btn:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
}

.change-email-btn {
    background: var(--gray-200);
    color: var(--gray-700);
}

.change-email-btn:hover {
    background: var(--gray-300);
}

.verification-timer {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-top: 1rem;
}

.verified-email-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #f0fdf4;
    border-radius: 0.75rem;
    border: 1px solid #22c55e;
    margin-bottom: 2rem;
    color: #15803d;
}

.verified-email-display i {
    color: #22c55e;
    font-size: 1.2rem;
}

.admin-container, .login-container {
    width: 100%;
    max-width: 500px;
}

.apply-container {
    width: 100%;
    max-width: 600px;
}

.admin-card, .login-card, .apply-card {
    background: var(--white);
    border-radius: 1.5rem;
    padding: 3rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.admin-header, .login-header, .apply-header {
    margin-bottom: 2rem;
}

.admin-icon, .login-icon, .apply-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* Apply Info Styles */
.apply-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    border: 1px solid var(--gray-200);
}

.apply-info .info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    color: var(--gray-700);
    font-size: 0.95rem;
}

.apply-info .info-item:not(:last-child) {
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
}

.apply-info .info-item i {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.admin-header h1, .login-header h1, .apply-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.admin-header p, .login-header p, .apply-header p {
    color: var(--gray-600);
    font-size: 1rem;
}

.admin-form, .login-form, .apply-form {
    text-align: left;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.75rem;
}

.form-group label i {
    width: 1.5rem;
    height: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.75rem;
    flex-shrink: 0;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s;
    background: var(--white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:hover, .form-group select:hover, .form-group textarea:hover {
    border-color: var(--gray-300);
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-group small {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.admin-btn, .login-btn, .apply-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border: none;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
    position: relative;
    overflow: hidden;
}

.admin-btn:hover, .login-btn:hover, .apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 64, 175, 0.4);
}

.admin-btn:active, .login-btn:active, .apply-btn:active {
    transform: translateY(0);
}

.admin-btn:disabled, .login-btn:disabled, .apply-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.admin-error, .login-error, .apply-error {
    background: #fef2f2;
    color: #dc2626;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid #fecaca;
    font-size: 0.9rem;
}

.admin-success, .login-success, .apply-success {
    background: #f0fdf4;
    color: #16a34a;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid #bbf7d0;
    font-size: 0.9rem;
}

/* Settings Styles */
.settings-section {
    margin-bottom: 2rem;
}

.settings-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.settings-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.settings-header h3 {
    color: var(--gray-900);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-header p {
    color: var(--gray-600);
    margin: 0;
    font-size: 0.9rem;
}

.password-change-form {
    max-width: 400px;
}

.password-change-form .form-group {
    margin-bottom: 1rem;
}

.password-change-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
    font-weight: 500;
}

.password-change-form input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.password-change-form input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.settings-btn {
    background: var(--primary-600);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-btn:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
}

.settings-btn:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
}

.admin-info {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.info-label {
    font-weight: 500;
    color: var(--gray-700);
}

.info-value {
    color: var(--gray-900);
}

/* Admin Dashboard */
.admin-dashboard {
    min-height: 100vh;
    background: var(--gray-50);
}

.admin-nav {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
    position: relative;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-700);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    margin-left: auto;
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.admin-nav-menu {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.admin-nav-btn {
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-nav-btn:hover, .admin-nav-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

.admin-logout-btn {
    padding: 0.75rem 1.5rem;
    background: var(--danger-color);
    color: var(--white);
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-logout-btn:hover {
    background: #dc2626;
}

.admin-main {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.admin-tab {
    display: none;
}

.admin-tab.active {
    display: block;
}

.admin-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-section-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
}

.admin-stats {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.stat-badge {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Application Cards */
.application-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    transition: all 0.3s;
}

.application-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.app-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.app-date {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.app-details {
    margin-bottom: 1.5rem;
}

.app-details p {
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.app-actions {
    display: flex;
    gap: 1rem;
}

.approve-btn, .reject-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.approve-btn {
    background: var(--success-color);
    color: var(--white);
}

.approve-btn:hover {
    background: #059669;
}

.reject-btn {
    background: var(--danger-color);
    color: var(--white);
}

.reject-btn:hover {
    background: #dc2626;
}

/* Application Sections */
.application-section {
    margin-bottom: 2rem;
}

.application-section .section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    border-left: 4px solid var(--primary-color);
}

.applications-grid {
    display: grid;
    gap: 1rem;
}

.email-pending-note {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--warning-color);
    font-style: italic;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    background: var(--warning-bg);
    border-radius: 0.5rem;
    border: 1px solid var(--warning-border);
}

.email-verified {
    color: var(--success-color);
    font-weight: 500;
}

.email-not-verified {
    color: var(--danger-color);
    font-weight: 500;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: white;
    font-size: 0.875rem;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-top: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: normal;
    transition: all 0.2s;
}

.checkbox-label:hover {
    color: var(--primary-color);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--gray-300);
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    flex-shrink: 0;
    margin-top: 0.125rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-size: 0.875rem;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.checkbox-text {
    line-height: 1.5;
    font-size: 0.95rem;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Apply Success */
.apply-success {
    text-align: center;
    padding: 2rem;
}

.apply-success i {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1.5rem;
}

.apply-success h3 {
    font-size: 1.5rem;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.apply-success p {
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.3s;
}

.back-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Login Links */
.login-links {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
}

.apply-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s;
}

.apply-link:hover {
    color: var(--primary-dark);
}

/* No Data */
.no-data {
    text-align: center;
    padding: 3rem;
    color: var(--gray-500);
    font-style: italic;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: var(--gray-600);
}

/* User Cards */
.user-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    transition: all 0.3s;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* User Limits Section */
.user-limits {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
    border-left: 4px solid var(--primary);
}

.limit-input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.25rem;
    font-size: 0.875rem;
    text-align: center;
    margin-left: 0.5rem;
}

.limit-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.usage-count {
    font-weight: 600;
    color: var(--success);
}

.usage-count.at-limit {
    color: var(--danger);
    background: var(--danger-light);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Profile Limit Info Styles */
.limit-info-card {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-lighter));
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--primary-200);
}

.limit-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.limit-stat {
    text-align: center;
    background: var(--white);
    padding: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.limit-stat .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
}

.limit-stat .stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.limit-progress {
    background: var(--white);
    padding: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--success);
    border-radius: 4px;
    transition: all 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* Daily Limit Exceeded Message Styles */
.limit-exceeded-message {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--danger-light), var(--warning-light));
    border-radius: 1rem;
    border: 2px solid var(--danger);
    margin: 1rem 0;
}

.limit-exceeded-message i {
    font-size: 3rem;
    color: var(--danger);
    margin-bottom: 1rem;
}

.limit-exceeded-message h3 {
    color: var(--danger);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.limit-exceeded-message p {
    color: var(--gray-700);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.limit-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.limit-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--white);
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 100px;
}

.limit-stat .stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.limit-stat .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

/* Custom Limit Modal Styles */
.limit-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.limit-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.limit-modal {
    background: white;
    border-radius: 1rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s ease;
}

.limit-modal-overlay.show .limit-modal {
    transform: scale(1) translateY(0);
}

.limit-modal-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.limit-modal-icon {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.limit-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.limit-modal-content {
    padding: 2rem;
}

.limit-modal-text {
    font-size: 1.1rem;
    color: #374151;
    text-align: center;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.limit-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.limit-stat-card {
    background: #f9fafb;
    padding: 1.5rem 1rem;
    border-radius: 0.75rem;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: white;
    font-weight: 500;
}

.limit-modal-footer-text {
    font-size: 0.95rem;
    color: #6b7280;
    text-align: center;
    margin-top: 1.5rem;
    margin-bottom: 0;
    line-height: 1.5;
}

.limit-modal-actions {
    padding: 1.5rem 2rem 2rem;
    text-align: center;
    border-top: 1px solid #e5e7eb;
}

.limit-modal-actions .btn {
    min-width: 120px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.text-danger {
    color: #dc2626 !important;
}

.text-success {
    color: #059669 !important;
}

.text-muted {
    color: #6b7280 !important;
}

/* Responsive */
@media (max-width: 640px) {
    .limit-modal {
        width: 95%;
        margin: 1rem;
    }

    .limit-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .limit-modal-header,
    .limit-modal-content,
    .limit-modal-actions {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.user-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.user-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.user-status.approved {
    background: #dcfce7;
    color: #166534;
}

.user-status.suspended {
    background: #fef2f2;
    color: #dc2626;
}

.user-details {
    margin-bottom: 1.5rem;
}

.user-details p {
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.user-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.suspend-btn, .activate-btn, .delete-btn, .reset-quota-btn, .approve-btn, .reject-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 1;
    visibility: visible;
    min-width: fit-content;
    white-space: nowrap;
}

.suspend-btn {
    background: var(--warning-color);
    color: var(--white);
}

.suspend-btn:hover {
    background: #d97706;
}

.activate-btn {
    background: var(--success-color);
    color: var(--white);
}

.activate-btn:hover {
    background: #059669;
}

.delete-btn {
    background: var(--danger-color);
    color: var(--white);
}

.delete-btn:hover {
    background: #dc2626;
}

.reset-quota-btn {
    background: var(--primary-color);
    color: var(--white);
    opacity: 1;
    visibility: visible;
}

.reset-quota-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.approve-btn {
    background: var(--success-color);
    color: var(--white);
}

.approve-btn:hover {
    background: #38a169;
}

.reject-btn {
    background: var(--danger-color);
    color: var(--white);
}

.reject-btn:hover {
    background: #c53030;
}

/* User sections */
.user-section {
    margin-bottom: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: 0.5rem;
    color: var(--gray-700);
    font-size: 1.1rem;
    font-weight: 600;
}

.user-grid {
    display: grid;
    gap: 1rem;
}

/* Status-based styling */
.user-card.pending {
    border-left: 4px solid var(--warning-color);
    background: #fffbeb;
}

.user-card.approved {
    border-left: 4px solid var(--success-color);
}

.user-card.suspended {
    border-left: 4px solid var(--danger-color);
    background: #fef2f2;
}

/* Email verification status */
.email-verified {
    color: var(--success-color);
    font-weight: 600;
}

.email-not-verified {
    color: var(--danger-color);
    font-weight: 600;
}

/* Section descriptions */
.section-description {
    margin: 0.5rem 0 1rem 0;
    padding: 0.75rem 1rem;
    background: var(--gray-100);
    border-radius: 0.5rem;
    color: var(--gray-600);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-description i {
    color: var(--primary-color);
}

/* Email Status Indicator */
.email-status-indicator {
    font-size: 0.875rem;
    font-weight: 500;
    margin-left: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.email-status-indicator.active {
    background: #dcfce7;
    color: #166534;
}

.email-status-indicator.disabled {
    background: #fef2f2;
    color: #dc2626;
}

.email-status-indicator.loading {
    background: #f3f4f6;
    color: #6b7280;
}

.email-status-indicator i {
    font-size: 0.75rem;
}

/* Status messages */
.status-messages {
    margin-bottom: 1.5rem;
    display: none;
}

.status-message {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.status-message.success {
    background: #f0f9ff;
    border-left-color: var(--success-color);
    color: #065f46;
}

.status-message.warning {
    background: #fffbeb;
    border-left-color: var(--warning-color);
    color: #92400e;
}

.status-message.info {
    background: #eff6ff;
    border-left-color: var(--primary-color);
    color: #1e40af;
}

.status-message.error {
    background: #fef2f2;
    border-left-color: var(--danger-color);
    color: #991b1b;
}

.message-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
}

.message-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.message-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Status badges */
.status-badge.verified {
    background: var(--success-color);
    color: white;
}

.status-badge.not-verified {
    background: var(--danger-color);
    color: white;
}

.status-badge.unknown {
    background: var(--gray-400);
    color: white;
}

.status-badge.pending {
    background: var(--warning-color);
    color: white;
}

.status-badge.active {
    background: var(--success-color);
    color: white;
}

.status-badge.suspended {
    background: var(--danger-color);
    color: white;
}

/* Status info grid for modals */
.status-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 1.5rem 0;
}

.status-info-card {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    text-align: center;
}

.status-info-card .status-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.status-info-card .status-value {
    font-weight: 600;
    font-size: 1rem;
}

.status-info-card .status-value.pending {
    color: var(--warning-color);
}

.status-info-card .status-value.approved {
    color: var(--success-color);
}

.status-info-card .status-value.suspended {
    color: var(--danger-color);
}

.status-info-card .status-value.verified {
    color: var(--success-color);
}

.status-info-card .status-value.not-verified {
    color: var(--danger-color);
}

.reset-all-quotas-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: var(--white);
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    margin-left: auto;
}

.reset-all-quotas-btn:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

.reset-all-quotas-btn:active {
    transform: translateY(0);
}

.cleanup-pending-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    color: var(--white);
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
    margin-left: 0.5rem;
}

.cleanup-pending-btn:hover {
    background: linear-gradient(135deg, #0284c7, #0369a1);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
}

.cleanup-pending-btn:active {
    transform: translateY(0);
}

/* Webhook Styles */
.webhook-section {
    margin-top: 1rem;
}

.webhook-info {
    margin-bottom: 1.5rem;
}

.info-box {
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.info-box.warning {
    background: #fef3c7;
    border-color: #f59e0b;
    color: #92400e;
}

.info-box i {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.webhook-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.webhook-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.webhook-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    min-width: 200px;
}

.webhook-btn.permanent {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: var(--white);
}

.webhook-btn.permanent:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
}

.webhook-btn.temporary {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: var(--white);
}

.webhook-btn.temporary:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
}

.webhook-result {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.webhook-result label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.url-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.url-container input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-family: monospace;
    font-size: 0.875rem;
    background: var(--white);
}

.copy-btn {
    padding: 0.75rem;
    background: var(--gray-600);
    color: var(--white);
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s;
}

.copy-btn:hover {
    background: var(--gray-700);
}

.webhook-usage {
    margin-top: 1rem;
}

.webhook-usage h4 {
    margin-bottom: 0.5rem;
    color: var(--gray-800);
}

.webhook-usage ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--gray-600);
}

.webhook-usage li {
    margin-bottom: 0.25rem;
}

/* API Management Styles */
.api-section {
    margin-top: 1rem;
}

.model-info-section,
.api-status-section,
.api-logs-section,
.api-stats-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
}

/* Model Info Styles */
.model-info-section {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 2px solid #0ea5e9;
}

.model-info-card {
    background: var(--white);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.model-info-content {
    padding: 0;
}

.model-info-header {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    color: var(--white);
}

.model-icon {
    font-size: 2rem;
    margin-right: 1rem;
    opacity: 0.9;
}

.model-details {
    flex: 1;
}

.model-details h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    opacity: 0.9;
}

.model-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.model-status {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.model-change-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.model-change-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.model-info-body {
    padding: 1.5rem;
}

.model-description {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f0f9ff;
    border-radius: 0.5rem;
    border-left: 4px solid #0ea5e9;
    color: #0c4a6e;
}

.model-description i {
    color: #0ea5e9;
    margin-top: 0.125rem;
}

.model-technical {
    display: grid;
    gap: 0.75rem;
}

.tech-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.tech-label {
    font-weight: 600;
    color: var(--gray-700);
}

.tech-value {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--gray-900);
    background: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid var(--gray-300);
}

.model-info-error {
    text-align: center;
    padding: 2rem;
    color: var(--error-color);
    font-style: italic;
}

/* Model Selection Styles */
.model-selection-card {
    background: var(--white);
    border-radius: 0.75rem;
    margin-top: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid #f59e0b;
}

.model-selection-content {
    padding: 0;
}

.model-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--white);
}

.model-selection-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.close-selection-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s;
}

.close-selection-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.model-options-grid {
    padding: 1.5rem;
    display: grid;
    gap: 1rem;
}

.model-option {
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s;
    background: var(--white);
}

.model-option:hover {
    border-color: #f59e0b;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.model-option.selected {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f0fdf4, #ffffff);
}

.model-option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.model-option-name {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 1rem;
}

.model-selected-icon {
    color: var(--success-color);
    font-size: 1.25rem;
}

.model-option-id {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--gray-600);
    background: var(--gray-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    display: inline-block;
}

.model-option-actions {
    display: flex;
    gap: 0.5rem;
}

.model-test-btn,
.model-select-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    flex: 1;
}

.model-test-btn {
    background: var(--warning-color);
    color: var(--white);
}

.model-test-btn:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.model-select-btn {
    background: var(--success-color);
    color: var(--white);
}

.model-select-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-title h4 {
    margin: 0;
    color: var(--gray-800);
    font-weight: 600;
}

.refresh-btn {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.refresh-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* API Keys Grid */
.api-keys-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.api-key-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.3s;
}

.api-key-card.active {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f0fdf4, #ffffff);
}

.api-key-card.missing {
    border-color: var(--gray-300);
    background: var(--gray-100);
    opacity: 0.7;
}

.api-key-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.api-key-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--gray-800);
}

.api-key-name i {
    color: var(--primary-color);
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background: var(--success-color);
    color: var(--white);
}

.status-badge.missing {
    background: var(--gray-500);
    color: var(--white);
}

.api-key-preview {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--gray-600);
    background: var(--gray-100);
    padding: 0.5rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.api-key-actions {
    display: flex;
    gap: 0.5rem;
}

.api-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    flex: 1;
}

.api-btn.test {
    background: var(--warning-color);
    color: var(--white);
}

.api-btn.test:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.api-btn.activate {
    background: var(--success-color);
    color: var(--white);
}

.api-btn.activate:hover {
    background: #059669;
    transform: translateY(-1px);
}

.api-btn.clear-manual {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.api-btn.clear-manual:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
}

.api-management-controls {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
}

.manual-selection-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.5rem;
    color: #92400e;
    font-size: 0.875rem;
}

.auto-failover-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f0fdf4;
    border: 1px solid #10b981;
    border-radius: 0.5rem;
    color: #065f46;
    font-size: 0.875rem;
}

.api-key-missing {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
}

/* API Logs */
.log-filters {
    display: flex;
    gap: 1rem;
}

.log-filters select {
    padding: 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    background: var(--white);
}

.api-logs-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    background: var(--white);
}

.api-logs-list {
    padding: 0.5rem;
}

.api-log-item {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    transition: background-color 0.2s;
}

.api-log-item:hover {
    background: var(--gray-50);
}

.api-log-item.success {
    border-left: 4px solid var(--success-color);
}

.api-log-item.error {
    border-left: 4px solid var(--error-color);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.log-key {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--gray-800);
}

.log-time {
    font-size: 0.875rem;
    color: var(--gray-500);
}

.log-details {
    margin-top: 0.5rem;
}

.log-type {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--gray-200);
    color: var(--gray-700);
}

.response-code {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--primary-color);
    color: var(--white);
}

.response-time {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--info-color);
    color: var(--white);
}

.log-message {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
    color: #dc2626;
    font-size: 0.875rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.no-logs,
.no-stats {
    text-align: center;
    padding: 2rem;
    color: var(--gray-500);
    font-style: italic;
}

/* API Stats */
.api-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.api-stat-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1rem;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.stat-header i {
    color: var(--primary-color);
}

.stat-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-value.success {
    color: var(--success-color);
}

.metric-value.error {
    color: var(--error-color);
}

.metric-value.total {
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.stat-success-rate {
    text-align: center;
    font-size: 0.875rem;
    color: var(--gray-600);
    padding: 0.5rem;
    background: var(--gray-50);
    border-radius: 0.375rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-highlight {
    background: linear-gradient(45deg, var(--accent-color), #fbbf24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.875rem;
    color: white;
    opacity: 1;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    padding: 2rem;
    text-align: center;
    max-width: 300px;
}

.hero-card-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.hero-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.hero-card p {
    opacity: 0.9;
}

/* Document Types Section */
.document-types {
    padding: 5rem 0;
    background: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.document-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.document-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.document-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.document-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.document-card:hover::before {
    transform: scaleX(1);
}

.document-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 1.5rem;
}

.document-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.document-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.document-format {
    background: var(--gray-100);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.format-tag {
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

/* Features Section - Minimal */
.features-minimal {
    padding: 3rem 0;
    background: var(--gray-50);
}

.section-header-minimal {
    text-align: center;
    margin-bottom: 2rem;
}

.section-title-small {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.features-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.feature-minimal {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--white);
    padding: 1rem 1.5rem;
    border-radius: 2rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.feature-minimal:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.feature-minimal i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.feature-minimal span {
    font-weight: 500;
    color: var(--gray-700);
}

/* Document Types - Minimal */
.document-types-minimal {
    padding: 3rem 0;
    background: var(--white);
}

.document-types-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    max-width: 800px;
    margin: 0 auto;
}

.document-type-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--gray-50);
    padding: 0.75rem 1.25rem;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.document-type-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
}

.document-type-item i {
    font-size: 1.125rem;
}

.document-type-item span {
    font-weight: 500;
    font-size: 0.875rem;
}

/* Header Stiller */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Ana İçerik */
.main-section {
    padding: 5rem 0;
    background: var(--white);
}

.main-content {
    background: var(--white);
    border-radius: 1.5rem;
    padding: 3rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
}

/* Sekmeler */
.tabs-container {
    margin-bottom: 3rem;
}

.tabs-header {
    display: flex;
    background: var(--gray-100);
    border-radius: 1rem;
    padding: 0.5rem;
    gap: 0.5rem;
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    border-radius: 0.75rem;
}

.tab-btn:hover {
    background: var(--white);
    color: var(--gray-800);
}

.tab-btn.active {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: var(--shadow);
}

.tab-icon {
    font-size: 1.125rem;
}

/* Sekme İçerikleri */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Upload Alanı */
.upload-section {
    margin-bottom: 2rem;
}

.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--gray-50);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--blue-50);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: var(--blue-100);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.upload-area h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-area p {
    color: #666;
    margin-bottom: 5px;
}

.file-info {
    font-size: 0.9rem;
    color: #999;
}

/* Dosya Detayları */
.file-details {
    margin-top: 20px;
}

.file-info-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.file-icon {
    font-size: 2rem;
    margin-right: 15px;
    color: #667eea;
}

.file-meta {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.file-size {
    color: #666;
    font-size: 0.9rem;
}

.remove-file {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.remove-file:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* Metin Girişi */
.text-input-section {
    margin-bottom: 30px;
}

.input-label {
    display: block;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.text-input {
    width: 100%;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
}

.text-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-counter {
    text-align: right;
    margin-top: 10px;
    font-size: 0.9rem;
    color: #666;
}

.char-count {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.char-count.warning {
    color: #f59e0b;
}

.char-count.error {
    color: #dc2626;
}

.char-count i {
    font-size: 1rem;
}

.char-limit-warning {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    background: currentColor;
    color: var(--white);
    border-radius: 0.25rem;
    opacity: 0.9;
}

/* Butonlar */
.submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border: none;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading Spinner */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sonuç Alanı */
.result-section {
    margin-top: 30px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 1px solid #e9ecef;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.result-header h3 {
    color: #333;
    font-size: 1.3rem;
}

.copy-btn {
    padding: 10px 20px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.result-content {
    background: white;
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #dee2e6;
    line-height: 1.8;
    font-size: 1rem;
    color: #333;
}

/* Mesaj Alanları */
.error-message, .success-message {
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 500;
}

/* Admin Email Yönetimi Butonları */
.btn-small {
    padding: 4px 8px;
    font-size: 11px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
}

.btn-info {
    background: #3b82f6;
    color: white;
}

.btn-info:hover {
    background: #2563eb;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.email-actions {
    border-left: 3px solid #f59e0b;
}

.email-not-verified {
    color: #dc2626;
    font-weight: bold;
}

.email-verified {
    color: #059669;
    font-weight: bold;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error-icon, .success-icon {
    font-size: 1.5rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: auto;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: #60a5fa;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    color: #cbd5e1;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #cbd5e1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #60a5fa;
}

.footer-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #cbd5e1;
}

.footer-bottom {
    border-top: 1px solid #475569;
    padding-top: 1rem;
    text-align: center;
}

.footer-bottom p {
    margin: 0.25rem 0;
    color: #94a3b8;
}

.footer-location-text {
    font-size: 0.9rem;
    font-style: italic;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.75rem 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #60a5fa;
}

.breadcrumb-item.active {
    color: white;
    font-weight: 500;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer {
        padding: 2rem 0 1rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .nav-container {
        position: relative;
    }

    .features-list,
    .document-types-list {
        flex-direction: column;
        align-items: center;
    }

    /* Container Mobile */
    .container {
        padding: 0 1rem;
    }

    /* Tab System Mobile */
    .tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tab-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem;
        font-size: 1rem;
    }

    /* Form Elements Mobile */
    .upload-area {
        padding: 2rem 1rem;
        min-height: 200px;
    }

    .text-input {
        min-height: 200px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .submit-btn {
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
    }

    /* Result Section Mobile */
    .result-content {
        padding: 1rem;
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .copy-btn {
        width: 100%;
        padding: 0.75rem;
        margin-top: 1rem;
    }

    /* Character Count Mobile */
    .char-count {
        font-size: 0.8rem;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }

    .char-limit-warning {
        margin-left: 0;
        margin-top: 0.25rem;
        font-size: 0.7rem;
    }

    .tabs-header {
        flex-direction: column;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 2.5rem;
    }

    .result-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    /* Form responsive styles */
    .apply-container, .admin-container, .login-container {
        max-width: 90%;
        margin: 1rem auto;
    }

    .apply-card, .admin-card, .login-card {
        padding: 2rem 1.5rem;
        border-radius: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .apply-info {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .apply-info .info-item {
        padding: 0.5rem 0;
        font-size: 0.9rem;
    }

    .apply-info .info-item i {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.8rem;
    }

    .form-group label i {
        width: 1.25rem;
        height: 1.25rem;
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.8rem;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab-button {
        border-radius: 8px;
        margin-bottom: 5px;
    }
    
    .upload-area {
        padding: 30px 10px;
    }

    /* Small mobile form styles */
    .apply-card, .admin-card, .login-card {
        padding: 1.5rem 1rem;
        margin: 0.5rem;
    }

    .admin-icon, .login-icon, .apply-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .apply-header h1, .admin-header h1, .login-header h1 {
        font-size: 1.5rem;
    }

    .apply-info .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        text-align: left;
    }

    .checkbox-text {
        font-size: 0.875rem;
    }

    /* Profile page responsive */
    .profile-container {
        padding: 1rem;
    }

    .profile-card {
        padding: 1.5rem;
    }

    .info-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .profile-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .profile-btn {
        justify-content: center;
    }
}

/* Admin Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 25px 30px 20px;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
}

.modal-close {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
    background: none;
    border: none;
}

.modal-close:hover {
    color: #fed7d7;
}

.modal-body {
    padding: 30px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .modal {
        padding: 10px;
    }

    .modal-content {
        max-height: 95vh;
    }

    .modal-header {
        padding: 20px 20px 15px;
    }

    .modal-body {
        padding: 20px;
    }
}

/* Email Settings Styles */
.email-provider-selection {
    margin-top: 1rem;
}

.provider-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.provider-option {
    cursor: pointer;
}

.provider-option input[type="radio"] {
    display: none;
}

.provider-card {
    border: 2px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s;
    background: var(--white);
}

.provider-option input[type="radio"]:checked + .provider-card {
    border-color: var(--primary-color);
    background: var(--primary-50);
}

.provider-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.provider-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.provider-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-900);
    font-weight: 600;
}

.provider-info p {
    margin: 0;
    color: var(--gray-600);
    font-size: 0.875rem;
}

.email-settings-form {
    margin-top: 1rem;
}

.email-settings-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.email-settings-form .form-group {
    margin-bottom: 1.5rem;
}

.email-settings-form .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.email-settings-form .form-group input,
.email-settings-form .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.3s;
}

.email-settings-form .form-group input:focus,
.email-settings-form .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.email-settings-form .form-group small {
    display: block;
    margin-top: 0.25rem;
    color: var(--gray-500);
    font-size: 0.75rem;
}

.email-settings-form .checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.email-settings-form .checkbox-label input[type="checkbox"] {
    display: none;
}

.email-settings-form .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s;
}

.email-settings-form .checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.email-settings-form .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.email-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.test-email-btn,
.save-email-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.test-email-btn {
    background: var(--warning-color);
    color: white;
}

.test-email-btn:hover {
    background: #f59e0b;
}

.save-email-btn {
    background: var(--success-color);
    color: white;
}

.save-email-btn:hover {
    background: #059669;
}

/* Admin Dashboard Mobile Responsive */
@media (max-width: 768px) {
    /* Admin Navigation Mobile */
    .admin-nav {
        flex-direction: column;
        gap: 0;
        padding: 1rem;
        position: relative;
    }

    .admin-nav-brand {
        justify-content: space-between;
        font-size: 1.125rem;
        width: 100%;
    }

    .mobile-menu-toggle {
        display: block !important;
    }

    .admin-nav-menu {
        display: none;
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        margin-top: 1rem;
        background: var(--white);
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: var(--shadow-md);
    }

    .admin-nav-menu.show {
        display: flex;
    }

    .admin-nav-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        min-width: auto;
        flex: 1;
        max-width: 150px;
    }

    .admin-logout-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        width: 100%;
        max-width: 200px;
    }

    .admin-main {
        padding: 1rem;
    }

    .admin-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .admin-section-header h2 {
        font-size: 1.5rem;
    }

    .admin-stats {
        width: 100%;
        justify-content: flex-start;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .stat-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
    }

    /* Stats Grid Mobile */
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    /* Applications and Users Lists Mobile */
    .application-card,
    .user-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .application-header,
    .user-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .application-info,
    .user-info {
        width: 100%;
    }

    .application-actions,
    .user-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        margin-top: 1rem;
    }

    .application-actions .btn,
    .user-actions .btn {
        width: 100%;
        justify-content: center;
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    /* Settings Mobile */
    .settings-section {
        margin-bottom: 1.5rem;
    }

    .settings-card {
        padding: 1rem !important;
        margin: 0 !important;
        border-radius: 0.75rem !important;
    }

    .settings-header {
        margin-bottom: 1rem !important;
        padding-bottom: 0.75rem !important;
        border-bottom: 1px solid var(--gray-200) !important;
    }

    .settings-header h3 {
        font-size: 1.125rem !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.25rem !important;
        margin: 0 !important;
    }

    .settings-header p {
        font-size: 0.875rem !important;
        margin: 0.5rem 0 0 0 !important;
        color: var(--gray-600) !important;
    }

    .password-change-form {
        max-width: 100%;
    }

    .settings-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    .admin-info {
        gap: 0.75rem !important;
        display: grid !important;
    }

    .admin-info .info-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.5rem !important;
        padding: 0.75rem !important;
        background: var(--gray-50) !important;
        border-radius: 0.5rem !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .admin-info .info-item label {
        font-size: 0.75rem;
    }

    .admin-info .info-item span,
    .admin-info .info-item .info-value {
        width: 100%;
        padding: 0.5rem;
        font-size: 0.875rem;
        word-break: break-all;
    }

    .admin-info .info-item .info-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.25rem;
    }

    .info-box {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .info-box i {
        font-size: 1rem;
        margin-top: 0;
    }

    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .password-change-form .form-group {
        margin-bottom: 1rem;
    }

    .password-change-form .form-group label {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .password-change-form .form-group input {
        width: 100%;
        padding: 0.75rem;
        font-size: 16px; /* iOS zoom prevention */
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        box-sizing: border-box;
    }

    .webhook-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .webhook-section {
        margin-top: 0.5rem;
    }

    .webhook-info {
        margin-bottom: 1rem;
    }

    .webhook-actions {
        gap: 0.75rem;
    }

    .webhook-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .webhook-btn {
        width: 100%;
        justify-content: center;
        padding: 0.875rem;
        font-size: 0.875rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .webhook-btn i {
        font-size: 1.125rem;
    }

    .webhook-result {
        padding: 1rem;
        margin-top: 1rem;
    }

    .webhook-result label {
        font-size: 0.875rem;
        margin-bottom: 0.75rem;
    }

    .url-container {
        flex-direction: column;
        gap: 0.75rem;
    }

    .url-container input {
        padding: 0.75rem;
        font-size: 0.8rem;
        word-break: break-all;
    }

    .copy-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    .webhook-usage {
        margin-top: 0.75rem;
    }

    .webhook-usage h4 {
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    .webhook-usage ul {
        padding-left: 1rem;
        font-size: 0.85rem;
    }

    .webhook-usage li {
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    /* Email Settings Mobile */
    .provider-options {
        flex-direction: column;
        gap: 1rem;
    }

    .provider-card {
        padding: 1rem;
    }

    .email-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .test-email-btn,
    .save-email-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    /* API Settings Mobile */
    .api-keys-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .api-key-card {
        padding: 1rem;
    }

    .api-stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .log-filters {
        flex-direction: column;
        gap: 0.5rem;
    }

    .log-filters select {
        width: 100% !important;
    }

    /* API Section Mobile */
    .api-section {
        margin-top: 0.5rem;
    }

    .model-info-section,
    .api-status-section,
    .api-logs-section,
    .api-stats-section {
        margin-bottom: 1.5rem;
        padding: 1rem;
    }

    .model-info-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .model-icon {
        font-size: 1.5rem;
    }

    .model-info-body {
        padding: 1rem;
    }

    .model-description {
        flex-direction: column;
        gap: 0.5rem;
    }

    .model-features {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .section-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .section-title h4 {
        font-size: 1rem;
    }

    .refresh-btn {
        width: 100%;
        justify-content: center;
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .api-logs-container {
        max-height: 300px;
    }

    .api-log-item {
        padding: 0.75rem;
    }

    .log-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .log-details {
        flex-direction: column;
        gap: 0.25rem;
    }

    /* Support Tickets Mobile */
    .support-filters {
        padding: 1rem !important;
    }

    .support-filters > div {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .support-ticket-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .ticket-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .ticket-meta {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .ticket-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .ticket-actions {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-top: 0;
    }

    .ticket-subject {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .ticket-description {
        font-size: 0.875rem;
        -webkit-line-clamp: 3;
    }

    /* Legacy ticket-card support */
    .ticket-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* User Section Title Mobile */
    .user-section-title {
        font-size: 1.125rem !important;
        padding: 0.75rem !important;
        flex-direction: row !important;
        gap: 0.5rem !important;
    }

    .user-section-title i {
        font-size: 1rem !important;
    }

    /* Modal Mobile */
    .modal-content {
        max-width: 95% !important;
        margin: 1rem !important;
        max-height: 90vh !important;
        padding: 0;
    }

    .modal-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .modal-body {
        padding: 1rem;
        max-height: calc(90vh - 120px);
        overflow-y: auto;
    }

    .modal-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    /* Form Elements Mobile */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem;
    }

    /* Table Mobile */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    table {
        min-width: 600px;
    }

    /* Utility Classes Mobile */
    .hidden-mobile {
        display: none !important;
    }

    .show-mobile {
        display: block !important;
    }

    .text-mobile-center {
        text-align: center !important;
    }

    .flex-mobile-column {
        flex-direction: column !important;
    }

    .gap-mobile-small {
        gap: 0.5rem !important;
    }

    .padding-mobile-small {
        padding: 0.5rem !important;
    }

    .margin-mobile-small {
        margin: 0.5rem !important;
    }
}

/* Support Ticket Cards */
.support-ticket-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.support-ticket-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.ticket-info {
    flex: 1;
}

.ticket-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.ticket-number {
    font-weight: 700;
    color: var(--gray-900);
    font-size: 1.1rem;
}

.ticket-subject {
    margin: 0 0 0.5rem 0;
    color: var(--gray-900);
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
}

.ticket-details {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--gray-600);
    font-size: 0.875rem;
    flex-wrap: wrap;
}

.ticket-details span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.ticket-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.reply-count {
    color: var(--gray-600);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.ticket-description {
    margin: 0;
    color: var(--gray-700);
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.priority-badge {
    padding: 0.125rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
}

.priority-urgent { background: #dc2626; }
.priority-high { background: #ea580c; }
.priority-normal { background: #2563eb; }
.priority-low { background: #16a34a; }

.status-badge {
    padding: 0.125rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
}

.status-open { background: #2563eb; }
.status-in_progress { background: #ea580c; }
.status-waiting { background: #ca8a04; }
.status-resolved { background: #16a34a; }
.status-closed { background: #6b7280; }

.new-message-badge {
    background: #fed7d7;
    color: #c53030;
    padding: 0.25rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
}

/* User Section Title */
.user-section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 1rem 0;
    padding: 0.75rem 1rem;
    background: var(--gray-100);
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-color);
}

.user-section-title i {
    color: var(--primary-color);
    font-size: 1.125rem;
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
    .admin-nav-btn {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        max-width: 120px;
    }

    .admin-section-header h2 {
        font-size: 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .application-card,
    .user-card,
    .ticket-card,
    .support-ticket-card {
        padding: 0.75rem;
    }

    .settings-card {
        padding: 0.75rem;
    }

    .ticket-meta {
        gap: 0.25rem;
    }

    .ticket-details {
        gap: 0.25rem;
    }

    .ticket-subject {
        font-size: 0.875rem;
    }

    .ticket-description {
        font-size: 0.8rem;
    }

    /* API Section Small Mobile */
    .model-info-section,
    .api-status-section,
    .api-logs-section,
    .api-stats-section {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .model-info-header {
        padding: 0.75rem;
    }

    .model-info-body {
        padding: 0.75rem;
    }

    .section-title {
        padding: 0.5rem;
    }

    .section-title h4 {
        font-size: 0.9rem;
    }

    .refresh-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .api-logs-container {
        max-height: 250px;
    }

    .api-log-item {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .webhook-result {
        padding: 0.75rem;
    }

    .webhook-result label {
        font-size: 0.8rem;
    }

    .url-container input {
        padding: 0.5rem;
        font-size: 0.75rem;
    }

    .copy-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .webhook-usage h4 {
        font-size: 0.875rem;
    }

    .webhook-usage ul {
        font-size: 0.8rem;
    }

    /* User Section Title Small Mobile */
    .user-section-title {
        font-size: 1rem !important;
        padding: 0.5rem !important;
        gap: 0.375rem !important;
    }

    .user-section-title i {
        font-size: 0.875rem !important;
    }

    .modal-content {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.5rem;
    }

    .password-change-form .form-group input {
        padding: 0.625rem;
        font-size: 16px;
    }

    .password-change-form .form-group label {
        font-size: 0.8rem;
    }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
    .admin-nav-btn {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        max-width: 120px;
    }

    .admin-section-header h2 {
        font-size: 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .application-card,
    .user-card,
    .ticket-card,
    .support-ticket-card {
        padding: 0.75rem;
    }

    .settings-card {
        padding: 0.75rem;
    }

    .settings-header h3 {
        font-size: 1rem;
    }

    .settings-header p {
        font-size: 0.8rem;
    }

    .settings-btn {
        padding: 0.625rem;
        font-size: 0.8rem;
    }

    .admin-info .info-item {
        padding: 0.5rem;
    }

    .admin-info .info-item span,
    .admin-info .info-item .info-value {
        padding: 0.375rem;
        font-size: 0.8rem;
    }

    .admin-info .info-item .info-label {
        font-size: 0.7rem;
    }

    .info-box {
        padding: 0.5rem;
    }

    .webhook-btn {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    /* API Section Small Mobile */
    .model-info-section,
    .api-status-section,
    .api-logs-section,
    .api-stats-section {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .model-info-header {
        padding: 0.75rem;
    }

    .model-info-body {
        padding: 0.75rem;
    }

    .section-title {
        padding: 0.5rem;
    }

    .section-title h4 {
        font-size: 0.9rem;
    }

    .refresh-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .api-logs-container {
        max-height: 250px;
    }

    .api-log-item {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .webhook-result {
        padding: 0.75rem;
    }

    .webhook-result label {
        font-size: 0.8rem;
    }

    .url-container input {
        padding: 0.5rem;
        font-size: 0.75rem;
    }

    .copy-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .webhook-usage h4 {
        font-size: 0.875rem;
    }

    .webhook-usage ul {
        font-size: 0.8rem;
    }

    .ticket-meta {
        gap: 0.25rem;
    }

    .ticket-details {
        gap: 0.25rem;
    }

    .ticket-subject {
        font-size: 0.875rem;
    }

    .ticket-description {
        font-size: 0.8rem;
    }

    .modal-content {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.5rem;
    }

    .password-change-form .form-group input {
        padding: 0.625rem;
        font-size: 16px;
    }

    .password-change-form .form-group label {
        font-size: 0.8rem;
    }
}
