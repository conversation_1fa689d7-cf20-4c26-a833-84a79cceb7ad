<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Başvuru - LegalAI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="mobile-menu.css">
    <style>
        /* Modern Corporate Design System */
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-900: #1e3a8a;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
        }

        /* Navbar styles are in styles.css and mobile-menu.css */

        /* Modern Container */
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 2rem;
        }

        .form-wrapper {
            width: 100%;
            max-width: 1100px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            height: 90vh;
            max-height: 700px;
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
        }

        /* Left Panel - Ultra Compact Hero */
        .hero-panel {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .hero-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
            text-align: center;
        }

        .hero-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }

        .hero-title {
            font-size: 1.75rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
            backdrop-filter: blur(10px);
        }

        .feature-icon {
            font-size: 0.875rem;
            margin-right: 0.5rem;
            opacity: 0.9;
        }

        .feature-text {
            font-weight: 500;
            font-size: 0.8rem;
        }

        /* Form Panel - Ultra Compact */
        .form-panel {
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            background: var(--gray-50);
            overflow: hidden;
        }

        .form-header {
            text-align: center;
            margin-bottom: 1rem;
        }

        .form-title {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }

        .form-subtitle {
            color: var(--gray-600);
            font-size: 0.85rem;
        }

        /* Ultra Compact Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 0.6rem;
            margin-bottom: 0.6rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.2rem;
            font-size: 0.75rem;
        }

        .form-input {
            padding: 0.5rem 0.6rem;
            border: 1px solid var(--gray-200);
            border-radius: 0.375rem;
            font-size: 0.8rem;
            transition: all 0.2s ease;
            background: white;
            color: var(--gray-900);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input::placeholder {
            color: var(--gray-400);
        }

        .form-textarea {
            resize: none;
            height: 50px;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .checkbox-input {
            width: 0.875rem;
            height: 0.875rem;
            border: 1px solid var(--gray-300);
            border-radius: 0.2rem;
            margin-top: 0.1rem;
        }

        .checkbox-input:checked {
            background-color: var(--primary-500);
            border-color: var(--primary-500);
        }

        .checkbox-label {
            color: var(--gray-600);
            font-size: 0.75rem;
            line-height: 1.3;
        }

        .checkbox-label a {
            color: var(--primary-600);
            text-decoration: none;
            font-weight: 500;
        }

        .checkbox-label a:hover {
            text-decoration: underline;
        }

        .submit-button {
            width: 100%;
            padding: 0.6rem 1.25rem;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            border: none;
            border-radius: 0.375rem;
            font-weight: 600;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 3px 10px 0 rgba(59, 130, 246, 0.3);
        }

        .submit-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .success-message {
            text-align: center;
            padding: 3rem;
        }

        .success-icon {
            width: 4rem;
            height: 4rem;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .success-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .success-text {
            color: var(--gray-600);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-600);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: var(--primary-700);
            transform: translateY(-1px);
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: 0.75rem;
            margin-top: 1rem;
            font-size: 0.875rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .form-wrapper {
                grid-template-columns: 1fr;
                margin: 1rem;
                border-radius: 1rem;
                height: auto;
                max-height: none;
            }

            .hero-panel {
                padding: 1.5rem;
                text-align: center;
            }

            .hero-title {
                font-size: 1.75rem;
            }

            .form-panel {
                padding: 1.5rem;
            }

            .form-grid {
                grid-template-columns: 1fr 1fr;
                gap: 0.5rem;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                padding: 5rem 0.5rem 1rem;
            }

            .hero-panel,
            .form-panel {
                padding: 1rem;
            }

            .hero-title {
                font-size: 1.5rem;
            }

            .form-title {
                font-size: 1.25rem;
            }

            .form-input {
                padding: 0.5rem;
                font-size: 0.8rem;
            }

            .form-label {
                font-size: 0.7rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 0.4rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-balance-scale nav-icon"></i>
                <span class="nav-title">LegalAI</span>
            </div>

            <!-- Desktop Menu -->
            <div class="nav-menu">
                <a href="/" class="nav-link">Ana Sayfa</a>
                <button class="nav-btn" id="user-menu-btn" style="display: none;">
                    <i class="fas fa-user"></i>
                    <span id="user-name"></span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <a href="/apply" class="nav-link active" id="register-link">Üye Ol</a>
                <a href="/login" class="nav-link" id="login-link">Giriş Yap</a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle" type="button" aria-label="Menüyü aç/kapat">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobile-menu">
            <div class="mobile-menu-header">
                <div class="mobile-brand">
                    <i class="fas fa-balance-scale"></i>
                    <span>LegalAI</span>
                </div>
                <button class="mobile-menu-close" id="mobile-menu-close" type="button" aria-label="Menüyü kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mobile-menu-content">
                <div class="mobile-nav-section">
                    <a href="/" class="mobile-nav-link">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </div>

                <!-- Mobile User Menu (when logged in) -->
                <div class="mobile-nav-section" id="mobile-user-section" style="display: none;">
                    <div class="mobile-user-info">
                        <div class="mobile-user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="mobile-user-details">
                            <span class="mobile-user-name" id="mobile-user-name">Kullanıcı</span>
                            <span class="mobile-user-status">Aktif Üye</span>
                        </div>
                    </div>

                    <a href="/profile" class="mobile-nav-link">
                        <i class="fas fa-user-circle"></i>
                        <span>Profil</span>
                    </a>

                    <button class="mobile-nav-link logout-btn" id="mobile-logout-btn" type="button">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Çıkış Yap</span>
                    </button>
                </div>

                <!-- Mobile Auth Buttons (when not logged in) -->
                <div class="mobile-nav-section" id="mobile-auth-section">
                    <div class="mobile-auth-buttons">
                        <a href="/apply" class="mobile-auth-btn primary">
                            <i class="fas fa-user-plus"></i>
                            <span>Üye Ol</span>
                        </a>
                        <a href="/login" class="mobile-auth-btn secondary">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Giriş Yap</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- Main Container -->
    <div class="main-container">
        <div class="form-wrapper">
            <!-- Hero Panel -->
            <div class="hero-panel">
                <div class="hero-content">
                    <div class="hero-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h1 class="hero-title">LegalAI</h1>
                    <p class="hero-subtitle">Profesyonel hukuki belgelerinizi yapay zeka ile hızlı ve güvenli şekilde özetleyin</p>

                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="fas fa-brain feature-icon"></i>
                            <span class="feature-text">Akıllı Özetleme Teknolojisi</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt feature-icon"></i>
                            <span class="feature-text">Güvenli Veri Koruması</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-bolt feature-icon"></i>
                            <span class="feature-text">Hızlı İşlem Süreci</span>
                        </div>
                    </div>

                    <p style="opacity: 0.8; font-size: 0.9rem;">
                        Zaten hesabınız var mı?
                        <a href="/login" style="color: white; text-decoration: underline; font-weight: 600;">Giriş yapın</a>
                    </p>
                </div>
            </div>

            <!-- Form Panel -->
            <div class="form-panel">
                <div id="apply-form-container">
                    <div class="form-header">
                        <h2 class="form-title">Sistem Başvurusu</h2>
                        <p class="form-subtitle">LegalAI'ye katılın ve hukuki belgelerinizi kolayca özetleyin</p>
                    </div>

                    <form id="apply-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="first-name" class="form-label">Ad</label>
                                <input type="text" id="first-name" name="firstName" required
                                       class="form-input" placeholder="Adınız">
                            </div>

                            <div class="form-group">
                                <label for="last-name" class="form-label">Soyad</label>
                                <input type="text" id="last-name" name="lastName" required
                                       class="form-input" placeholder="Soyadınız">
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">E-posta</label>
                                <input type="email" id="email" name="email" required
                                       class="form-input" placeholder="<EMAIL>">
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">Telefon</label>
                                <input type="tel" id="phone" name="phone" required
                                       class="form-input" placeholder="555 123 45 67">
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Şifre</label>
                                <input type="password" id="password" name="password" required minlength="6"
                                       class="form-input" placeholder="Min 6 karakter">
                            </div>

                            <div class="form-group">
                                <label for="confirm-password" class="form-label">Şifre Tekrar</label>
                                <input type="password" id="confirm-password" name="confirmPassword" required
                                       class="form-input" placeholder="Şifre tekrar">
                            </div>

                            <div class="form-group">
                                <label for="profession" class="form-label">Meslek</label>
                                <select id="profession" name="profession" required class="form-input form-select">
                                    <option value="">Seçiniz</option>
                                    <option value="avukat">Avukat</option>
                                    <option value="hakim">Hakim</option>
                                    <option value="savcı">Savcı</option>
                                    <option value="katip">Katip</option>
                                    <option value="hukuk-ogrencisi">Hukuk Öğrencisi</option>
                                    <option value="akademisyen">Akademisyen</option>
                                    <option value="diger">Diğer</option>
                                </select>
                            </div>

                            <div class="form-group full-width" style="grid-column: 1 / -1;">
                                <label for="reason" class="form-label">Kullanım Amacı</label>
                                <textarea id="reason" name="reason" required
                                          class="form-input form-textarea"
                                          placeholder="Sistemi hangi amaçla kullanacaksınız?"></textarea>
                            </div>
                        </div>

                        <div class="checkbox-group">
                            <input type="checkbox" id="terms" name="terms" required class="checkbox-input">
                            <label for="terms" class="checkbox-label">
                                <a href="#" class="text-primary-600">Kullanım şartlarını</a> okudum ve kabul ediyorum
                            </label>
                        </div>

                        <button type="submit" id="apply-submit" class="submit-button">
                            <span id="btn-text">
                                <i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i>
                                Başvuru Gönder
                            </span>
                            <span id="loading-spinner" style="display: none; margin-left: 0.5rem;">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </form>
                </div>

                <div id="apply-success" style="display: none;">
                    <div class="success-message">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="success-title">Başvurunuz Alındı!</h3>
                        <p class="success-text">
                            Başvurunuz başarıyla gönderildi. E-posta doğrulama linki gönderilecektir.
                            <br><br>
                            <strong>Sonraki Adımlar:</strong><br>
                            1. E-posta kutunuzu kontrol edin<br>
                            2. Doğrulama linkine tıklayın<br>
                            3. Admin onayını bekleyin (24-48 saat)
                        </p>
                        <a href="/" class="back-button">
                            <i class="fas fa-home"></i>
                            Ana Sayfaya Dön
                        </a>
                    </div>
                </div>

                <div id="apply-error" class="error-message" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="apply.js"></script>
    <script src="mobile-menu.js"></script>
</body>
</html>
