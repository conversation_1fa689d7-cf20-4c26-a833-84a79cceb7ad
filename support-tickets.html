<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Destek Kayıtlarım - Hukuki Belge Özetleme</title>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .support-tickets-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }

        .support-tickets-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            margin: 0 auto;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .page-title h1 {
            color: #2d3748;
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }

        .page-title .icon {
            font-size: 2.5rem;
            color: #667eea;
        }

        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 600;
            color: #4a5568;
            font-size: 0.9rem;
        }

        .filter-select {
            padding: 8px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            font-size: 0.9rem;
            min-width: 120px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .tickets-grid {
            display: grid;
            gap: 20px;
        }

        .ticket-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .ticket-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            gap: 15px;
        }

        .ticket-info {
            flex: 1;
        }

        .ticket-number {
            font-size: 0.85rem;
            color: #718096;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .ticket-subject {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .ticket-meta {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .ticket-category {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .ticket-date {
            color: #718096;
            font-size: 0.85rem;
        }

        .ticket-badges {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-end;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-open {
            background: #fed7d7;
            color: #c53030;
        }

        .status-in_progress {
            background: #bee3f8;
            color: #2b6cb0;
        }

        .status-waiting {
            background: #faf089;
            color: #975a16;
        }

        .status-resolved {
            background: #c6f6d5;
            color: #2f855a;
        }

        .status-closed {
            background: #e2e8f0;
            color: #4a5568;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-low {
            background: #c6f6d5;
            color: #2f855a;
        }

        .priority-normal {
            background: #bee3f8;
            color: #2b6cb0;
        }

        .priority-high {
            background: #faf089;
            color: #975a16;
        }

        .priority-urgent {
            background: #fed7d7;
            color: #c53030;
        }

        .ticket-description {
            color: #4a5568;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-top: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .reply-count {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #718096;
            font-size: 0.85rem;
            margin-top: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state .icon {
            font-size: 4rem;
            color: #cbd5e0;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .empty-state p {
            font-size: 1rem;
            margin-bottom: 25px;
        }

        .loading-state {
            text-align: center;
            padding: 40px;
            color: #718096;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #5a67d8;
            transform: translateX(-5px);
        }

        .back-link i {
            margin-right: 8px;
        }

        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            padding: 25px 30px 20px;
            border-bottom: 2px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #2d3748;
            font-size: 1.5rem;
        }

        .modal-close {
            font-size: 2rem;
            color: #718096;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .modal-close:hover {
            color: #e53e3e;
        }

        .modal-body {
            padding: 30px;
        }

        @media (max-width: 768px) {
            .support-tickets-card {
                padding: 20px 15px;
                margin: 10px;
            }

            .page-header {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .page-title h1 {
                font-size: 1.5rem;
            }

            .filters-section {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .filter-group {
                justify-content: space-between;
            }

            .ticket-header {
                flex-direction: column;
                align-items: stretch;
            }

            .ticket-badges {
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
            }

            .ticket-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .modal-content {
                margin: 10px;
                max-height: 95vh;
            }

            .modal-header {
                padding: 20px 20px 15px;
            }

            .modal-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="support-tickets-container">
        <div class="support-tickets-card">
            <a href="/profile" class="back-link">
                <i class="fas fa-arrow-left"></i>
                Profil Sayfasına Dön
            </a>

            <div class="page-header">
                <div class="page-title">
                    <i class="fas fa-ticket-alt icon"></i>
                    <h1>Destek Kayıtlarım</h1>
                </div>
                <div class="header-actions">
                    <a href="/support" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Yeni Destek Talebi
                    </a>
                </div>
            </div>

            <div class="filters-section">
                <div class="filter-group">
                    <label for="status-filter">Durum:</label>
                    <select id="status-filter" class="filter-select">
                        <option value="">Tümü</option>
                        <option value="open">Açık</option>
                        <option value="in_progress">Devam Ediyor</option>
                        <option value="waiting">Beklemede</option>
                        <option value="resolved">Çözüldü</option>
                        <option value="closed">Kapatıldı</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="priority-filter">Öncelik:</label>
                    <select id="priority-filter" class="filter-select">
                        <option value="">Tümü</option>
                        <option value="urgent">Acil</option>
                        <option value="high">Yüksek</option>
                        <option value="normal">Normal</option>
                        <option value="low">Düşük</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort-filter">Sıralama:</label>
                    <select id="sort-filter" class="filter-select">
                        <option value="newest">En Yeni</option>
                        <option value="oldest">En Eski</option>
                        <option value="priority">Öncelik</option>
                        <option value="status">Durum</option>
                    </select>
                </div>
            </div>

            <div class="loading-state" id="loading-state">
                <div class="loading-spinner"></div>
                <p>Destek kayıtlarınız yükleniyor...</p>
            </div>

            <div class="tickets-grid" id="tickets-grid" style="display: none;">
                <!-- Tickets will be loaded here -->
            </div>

            <div class="empty-state" id="empty-state" style="display: none;">
                <i class="fas fa-inbox icon"></i>
                <h3>Henüz destek kaydınız bulunmuyor</h3>
                <p>İlk destek talebinizi oluşturmak için aşağıdaki butona tıklayın.</p>
                <a href="/support" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    İlk Destek Talebimi Oluştur
                </a>
            </div>
        </div>
    </div>

    <!-- Ticket Detail Modal -->
    <div id="ticket-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Destek Kaydı Detayı</h2>
                <span class="modal-close" onclick="closeTicketModal()">&times;</span>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Ticket details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="support-tickets.js"></script>
</body>
</html>
