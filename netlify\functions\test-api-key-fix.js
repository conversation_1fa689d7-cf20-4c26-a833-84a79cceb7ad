const { createClient } = require('@supabase/supabase-js');
const { apiKeyManager } = require('./api-key-manager');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        console.log('=== API Key Fix Test ===');
        
        // 1. System_settings tablosunu kontrol et
        console.log('1. Checking system_settings table...');
        const { data: settings, error: settingsError } = await supabase
            .from('system_settings')
            .select('*')
            .limit(5);

        if (settingsError) {
            console.error('System_settings table error:', settingsError);
            
            // Tablo yoksa olu<PERSON>
            if (settingsError.code === '42P01') { // relation does not exist
                console.log('Creating system_settings table...');
                
                const { error: createError } = await supabase.rpc('create_system_settings_table');
                
                if (createError) {
                    console.error('Failed to create table:', createError);
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'System_settings tablosu oluşturulamadı',
                            details: createError.message
                        })
                    };
                }
            }
        } else {
            console.log('System_settings table exists, records:', settings?.length || 0);
        }

        // 2. API Key Manager durumunu kontrol et
        console.log('2. Checking API Key Manager status...');
        const keyStatus = apiKeyManager.getKeyStatus();
        console.log('Current key status:', keyStatus);

        // 3. Manuel seçimi yükle
        console.log('3. Loading manual selection...');
        await apiKeyManager.loadManualSelection();
        const statusAfterLoad = apiKeyManager.getKeyStatus();
        console.log('Status after loading manual selection:', statusAfterLoad);

        // 4. Manuel API key ayarını kontrol et
        console.log('4. Checking manual_api_key setting in database...');
        const { data: manualKeySetting, error: manualKeyError } = await supabase
            .from('system_settings')
            .select('*')
            .eq('setting_key', 'manual_api_key')
            .single();

        console.log('Manual key setting:', manualKeySetting);
        console.log('Manual key error:', manualKeyError);

        // 5. Aktif key'i al
        console.log('5. Getting current API key...');
        const currentKey = await apiKeyManager.getCurrentApiKey();
        console.log('Current active key:', currentKey?.name || 'None');

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                test_results: {
                    system_settings_exists: !settingsError,
                    system_settings_records: settings?.length || 0,
                    manual_key_setting: manualKeySetting,
                    manual_key_error: manualKeyError,
                    api_key_manager_status: keyStatus,
                    status_after_manual_load: statusAfterLoad,
                    current_active_key: currentKey?.name || 'None',
                    manual_selection_loaded: statusAfterLoad.manuallySelectedKey
                }
            })
        };

    } catch (error) {
        console.error('Test error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Test hatası',
                details: error.message
            })
        };
    }
};
