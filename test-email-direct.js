// Do<PERSON>rudan e-posta gönderme testi
require('dotenv').config();

async function testEmailDirect() {
    try {
        console.log('🔧 Environment variables check:');
        console.log('SUPABASE_URL:', !!process.env.SUPABASE_URL);
        console.log('SUPABASE_SERVICE_KEY:', !!process.env.SUPABASE_SERVICE_KEY);
        console.log('JWT_SECRET:', !!process.env.JWT_SECRET);

        // Supabase client oluştur
        const { createClient } = require('@supabase/supabase-js');
        const supabase = createClient(
            process.env.SUPABASE_URL,
            process.env.SUPABASE_SERVICE_KEY
        );

        console.log('📧 Fetching email settings from Supabase...');
        
        // Email ayarlarını al
        const { data: emailSettings, error: settingsError } = await supabase
            .from('system_settings')
            .select('setting_key, setting_value, setting_type')
            .in('setting_key', [
                'email_enabled',
                'email_service_provider',
                'email_test_mode',
                'email_test_address',
                'brevo_api_key',
                'brevo_sender_email',
                'brevo_sender_name'
            ])
            .eq('is_active', true);

        if (settingsError) {
            console.error('❌ Settings error:', settingsError);
            return;
        }

        console.log('✅ Email settings loaded:', emailSettings.length, 'settings');
        emailSettings.forEach(setting => {
            console.log(`  ${setting.setting_key}: ${setting.setting_key.includes('api_key') ? '[HIDDEN]' : setting.setting_value}`);
        });

        // Email service'i test et
        const { sendEmail } = require('./netlify/functions/utils/email-service');
        
        console.log('\n📧 Testing email sending...');
        const emailResult = await sendEmail({
            to: '<EMAIL>', // Test kullanıcısının e-postası
            subject: 'Test E-posta - Admin Yanıt Debug',
            html: `
                <h2>Test E-posta</h2>
                <p>Bu bir test e-postasıdır. Admin yanıt sistemi debug edilmektedir.</p>
                <p>Gönderim zamanı: ${new Date().toLocaleString('tr-TR')}</p>
            `,
            text: 'Test e-posta - Admin yanıt debug'
        }, emailSettings);

        console.log('\n📧 Email result:', JSON.stringify(emailResult, null, 2));

    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testEmailDirect();
