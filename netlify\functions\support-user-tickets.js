// Kullanıcının Destek Kayıtlarını Getiren API Endpoint

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece GET metodunu kabul et
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Kullanıcının varlığını kontrol et
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, status')
            .eq('id', decoded.userId)
            .single();

        if (userError || !user) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
            };
        }

        // Kullanıcı onaylanmış mı kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({ 
                    error: 'Destek kayıtlarını görüntülemek için hesabınızın onaylanmış olması gerekir.' 
                })
            };
        }

        // Query parametrelerini al
        const queryParams = event.queryStringParameters || {};
        const status = queryParams.status;
        const priority = queryParams.priority;
        const limit = parseInt(queryParams.limit) || 50;
        const offset = parseInt(queryParams.offset) || 0;

        // Kullanıcının ticketlarını getir
        let query = supabase
            .from('support_tickets')
            .select(`
                id,
                ticket_number,
                subject,
                description,
                priority,
                status,
                reply_count,
                last_reply_at,
                last_reply_by,
                is_read_by_user,
                created_at,
                updated_at,
                support_categories(
                    id,
                    name,
                    color,
                    icon
                )
            `)
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .range(offset, offset + limit - 1);

        // Filtreleri uygula
        if (status) {
            query = query.eq('status', status);
        }

        if (priority) {
            query = query.eq('priority', priority);
        }

        const { data: tickets, error: ticketsError } = await query;

        if (ticketsError) {
            console.error('Tickets fetch error:', ticketsError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Destek kayıtları getirilemedi.' })
            };
        }

        // Toplam ticket sayısını al (pagination için)
        let countQuery = supabase
            .from('support_tickets')
            .select('id', { count: 'exact' })
            .eq('user_id', user.id);

        if (status) {
            countQuery = countQuery.eq('status', status);
        }

        if (priority) {
            countQuery = countQuery.eq('priority', priority);
        }

        const { count: totalCount, error: countError } = await countQuery;

        if (countError) {
            console.error('Count error:', countError);
        }

        // İstatistikleri hesapla
        const stats = {
            total: totalCount || 0,
            open: 0,
            in_progress: 0,
            waiting: 0,
            resolved: 0,
            closed: 0
        };

        // Durum bazında sayıları al
        const { data: statusCounts, error: statusError } = await supabase
            .from('support_tickets')
            .select('status')
            .eq('user_id', user.id);

        if (!statusError && statusCounts) {
            statusCounts.forEach(ticket => {
                if (stats.hasOwnProperty(ticket.status)) {
                    stats[ticket.status]++;
                }
            });
        }

        // Okunmamış yanıt sayısını hesapla
        const unreadReplies = tickets ? tickets.filter(ticket => 
            ticket.last_reply_by === 'admin' && !ticket.is_read_by_user
        ).length : 0;

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                tickets: tickets || [],
                pagination: {
                    total: totalCount || 0,
                    limit: limit,
                    offset: offset,
                    hasMore: (offset + limit) < (totalCount || 0)
                },
                stats: stats,
                unreadReplies: unreadReplies
            })
        };

    } catch (error) {
        console.error('Support user tickets error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}
