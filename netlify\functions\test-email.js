const { Resend } = require('resend');
const { getAccountApprovedEmailTemplate } = require('./email-templates/account-approved');

const resend = new Resend(process.env.RESEND_API_KEY);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const { email } = JSON.parse(event.body);

        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ 
                    error: 'E-posta adresi gerek<PERSON>' 
                })
            };
        }

        console.log('Sending test approval email to:', email);

        // Test e-posta template'ini hazırla
        const emailHtml = getAccountApprovedEmailTemplate('Test', 'Kullanıcı');

        // Test e-posta gönder
        const emailResult = await resend.emails.send({
            from: 'LegalAI <<EMAIL>>',
            to: [email],
            subject: '🧪 TEST - Hesabınız Onaylandı - LegalAI',
            html: emailHtml,
            headers: {
                'X-Priority': '1',
                'X-MSMail-Priority': 'High',
                'Importance': 'high'
            },
            text: `🧪 TEST E-POSTA

🎉 HESABINIZ ONAYLANDI!

Merhaba Test Kullanıcı,

Bu bir test e-postasıdır. E-posta template'inin düzgün çalışıp çalışmadığını kontrol etmek için gönderilmiştir.

🚀 ARTIK NELER YAPABİLİRSİNİZ?

✅ Hukuki belgeleri anında özetleyebilirsiniz
✅ PDF dosyalarını yükleyip analiz edebilirsiniz  
✅ Günlük özet limitinizi kullanabilirsiniz
✅ Gelişmiş AI destekli analiz özelliklerinden faydalanabilirsiniz

🔐 SİSTEME GİRİŞ YAPIN:
https://hukukibelgeozetleme.netlify.app/login

---
LegalAI - Hukuki Belge Özetleme Sistemi
Bu bir test e-postasıdır.
© 2025 LegalAI. Tüm hakları saklıdır.`
        });

        console.log('Test email sent successfully:', emailResult.data?.id);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Test e-postası başarıyla gönderildi',
                emailId: emailResult.data?.id,
                preview: emailHtml.substring(0, 500) + '...'
            })
        };

    } catch (error) {
        console.error('Send test email error:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Test e-posta gönderilirken hata oluştu',
                details: error.message
            })
        };
    }
};
