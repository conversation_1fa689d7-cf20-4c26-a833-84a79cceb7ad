// Trial kullanımı kaydetme function'ı
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // IP adresini al
        const clientIP = getClientIP(event);
        
        if (!clientIP) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'IP adresi alınamadı.' })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { userAgent, fingerprint, documentType } = body;

        // Supabase function ile trial kullanımını kaydet - Fallback mekanizması ile
        let data;
        try {
            const { data: rpcData, error: rpcError } = await supabase.rpc('record_trial_usage', {
                p_ip_address: clientIP,
                p_user_agent: userAgent || null,
                p_fingerprint: fingerprint || null,
                p_document_type: documentType || null
            });

            if (rpcError) {
                console.error('Trial record RPC error:', rpcError);
                // RPC fonksiyonu yoksa fallback kullan
                data = await recordTrialUsageFallback(clientIP, userAgent, fingerprint, documentType);
            } else {
                data = rpcData;
            }
        } catch (generalError) {
            console.error('Trial record general error:', generalError);
            // Genel hata durumunda fallback kullan
            data = await recordTrialUsageFallback(clientIP, userAgent, fingerprint, documentType);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                result: data
            })
        };

    } catch (error) {
        console.error('Record trial error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Trial kaydı sırasında hata oluştu.' })
        };
    }
};

// IP adresini güvenilir şekilde al
function getClientIP(event) {
    const headers = event.headers;
    
    let ip = headers['x-nf-client-connection-ip'] ||
             headers['x-forwarded-for'] ||
             headers['x-real-ip'] ||
             headers['cf-connecting-ip'] ||
             headers['x-client-ip'] ||
             event.requestContext?.identity?.sourceIp ||
             '127.0.0.1';

    if (ip.includes(',')) {
        ip = ip.split(',')[0].trim();
    }

    if (ip === '::1') {
        ip = '127.0.0.1';
    }

    return ip;
}

// 🛡️ GÜVENLİK: Trial kullanım kayıt fallback (RPC fonksiyonu çalışmadığında)
async function recordTrialUsageFallback(clientIP, userAgent, fingerprint, documentType) {
    try {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);

        // Mevcut kaydı bul
        const { data: usageRecord, error: selectError } = await supabase
            .from('trial_usage')
            .select('*')
            .eq('ip_address', clientIP)
            .single();

        let newUsageCount = 1;

        if (selectError && selectError.code === 'PGRST116') {
            // Kayıt yok, yeni oluştur
            const { error: insertError } = await supabase
                .from('trial_usage')
                .insert([{
                    ip_address: clientIP,
                    user_agent: userAgent,
                    fingerprint: fingerprint,
                    usage_count: 1,
                    first_used_at: new Date().toISOString(),
                    last_used_at: new Date().toISOString(),
                    created_at: new Date().toISOString()
                }]);

            if (insertError) {
                console.error('Fallback insert error:', insertError);
                throw insertError;
            }

            newUsageCount = 1;
        } else if (!selectError && usageRecord) {
            // Mevcut kaydı güncelle - 24 saat sıfırlama kaldırıldı
            // Sayacı artır (sıfırlama yok)
            newUsageCount = (usageRecord.usage_count || 0) + 1;

            const { error: updateError } = await supabase
                .from('trial_usage')
                .update({
                    usage_count: newUsageCount,
                    last_used_at: new Date().toISOString(),
                    user_agent: userAgent || usageRecord.user_agent,
                    fingerprint: fingerprint || usageRecord.fingerprint
                })
                .eq('ip_address', clientIP);

            if (updateError) {
                console.error('Fallback update error:', updateError);
                throw updateError;
            }
        } else {
            // Diğer veritabanı hataları
            console.error('Fallback select error:', selectError);
            throw selectError;
        }

        return {
            success: true,
            usage_count: newUsageCount,
            message: 'Trial kullanımı kaydedildi (fallback).',
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error('Fallback record general error:', error);
        // Hata durumunda bile başarılı gibi göster (logging amaçlı)
        return {
            success: true,
            usage_count: 1,
            message: 'Trial kullanımı kaydedildi (fallback error).',
            timestamp: new Date().toISOString()
        };
    }
}
