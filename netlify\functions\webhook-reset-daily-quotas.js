const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

// Kalıcı webhook secret key (32 karakter)
const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET || 'K8mN9pQ2rS5tU7vW0xY3zA6bC9dE2fG5';
const PERMANENT_WEBHOOK_KEY = process.env.PERMANENT_WEBHOOK_KEY || 'PermanentWebhook2024SecureKey789';

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing environment variables:', {
        SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
        SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY
    });
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Kalıcı webhook URL oluşturma
function generatePermanentWebhookUrl(baseUrl) {
    const secretHash = crypto.createHash('sha256').update(PERMANENT_WEBHOOK_KEY).digest('hex');
    const permanentToken = secretHash.substring(0, 32); // İlk 32 karakter
    return `${baseUrl}/.netlify/functions/webhook-reset-daily-quotas?key=${permanentToken}`;
}

// Kalıcı key doğrulama
function validatePermanentKey(providedKey) {
    const secretHash = crypto.createHash('sha256').update(PERMANENT_WEBHOOK_KEY).digest('hex');
    const validKey = secretHash.substring(0, 32);
    return providedKey === validKey;
}

// Geçici token oluşturma fonksiyonu (eski sistem - opsiyonel)
function generateSecureToken() {
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(16).toString('hex');
    const data = `${timestamp}-${randomBytes}`;
    const hash = crypto.createHmac('sha256', WEBHOOK_SECRET).update(data).digest('hex');
    return `${timestamp}-${randomBytes}-${hash}`;
}

// Geçici token doğrulama fonksiyonu (eski sistem - opsiyonel)
function validateToken(token) {
    try {
        const parts = token.split('-');
        if (parts.length !== 3) return false;

        const [timestamp, randomBytes, providedHash] = parts;
        const data = `${timestamp}-${randomBytes}`;
        const expectedHash = crypto.createHmac('sha256', WEBHOOK_SECRET).update(data).digest('hex');

        // Hash kontrolü
        if (providedHash !== expectedHash) return false;

        // Zaman kontrolü (24 saat geçerli)
        const tokenTime = parseInt(timestamp);
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 saat

        return (now - tokenTime) <= maxAge;
    } catch (error) {
        return false;
    }
}

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // GET request: Kalıcı webhook URL oluşturma
        if (event.httpMethod === 'GET' && event.queryStringParameters?.generate === 'permanent') {
            const baseUrl = event.headers.host ? 'https://' + event.headers.host : 'https://hukukibelgeozetleme.netlify.app';
            const permanentWebhookUrl = generatePermanentWebhookUrl(baseUrl);

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Kalıcı webhook URL oluşturuldu',
                    webhook_url: permanentWebhookUrl,
                    expires: 'Hiçbir zaman (kalıcı)',
                    note: 'Bu URL kalıcıdır ve süresiz kullanılabilir. Güvenli bir yerde saklayın.'
                })
            };
        }

        // GET request: Geçici token oluşturma (eski sistem - opsiyonel)
        if (event.httpMethod === 'GET' && event.queryStringParameters?.generate === 'token') {
            const token = generateSecureToken();
            const webhookUrl = `${event.headers.host ? 'https://' + event.headers.host : 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/webhook-reset-daily-quotas?token=${token}`;

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Geçici webhook URL oluşturuldu',
                    webhook_url: webhookUrl,
                    token: token,
                    expires_in: '24 hours',
                    note: 'Bu URL 24 saat sonra geçersiz olur.'
                })
            };
        }

        // POST veya GET request: Quota sıfırlama
        if (event.httpMethod === 'GET' || event.httpMethod === 'POST') {
            const permanentKey = event.queryStringParameters?.key;
            const temporaryToken = event.queryStringParameters?.token;

            let isValidAuth = false;
            let authType = '';

            // Kalıcı key kontrolü
            if (permanentKey && validatePermanentKey(permanentKey)) {
                isValidAuth = true;
                authType = 'permanent_key';
            }
            // Geçici token kontrolü (fallback)
            else if (temporaryToken && validateToken(temporaryToken)) {
                isValidAuth = true;
                authType = 'temporary_token';
            }

            if (!isValidAuth) {
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({
                        error: 'Yetkilendirme gerekli',
                        message: 'Geçerli bir key veya token parametresi sağlanmalıdır.',
                        hint: 'URL formatı: ?key=KALICI_KEY veya ?token=GECICI_TOKEN'
                    })
                };
            }

            console.log('Webhook triggered for daily quota reset');

            // Tüm aktif kullanıcıları al
            const { data: users, error: usersError } = await supabase
                .from('users')
                .select('id, email, first_name, last_name, daily_summary_count')
                .eq('status', 'approved');

            if (usersError) {
                console.error('Users fetch error:', usersError);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        error: 'Kullanıcılar getirilemedi',
                        details: usersError.message
                    })
                };
            }

            console.log(`Found ${users.length} active users to reset`);

            // Tüm kullanıcıların haklarını sıfırla
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
            
            const { data: resetData, error: resetError } = await supabase
                .from('users')
                .update({
                    daily_summary_count: 0,
                    last_summary_date: today
                })
                .eq('status', 'approved')
                .select('id, email, first_name, last_name');

            if (resetError) {
                console.error('Bulk reset error:', resetError);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        error: 'Toplu hak sıfırlama işlemi başarısız oldu',
                        details: resetError.message
                    })
                };
            }

            console.log(`Successfully reset quotas for ${resetData.length} users via webhook`);

            // Webhook log ekle
            try {
                const clientIP = event.headers['x-forwarded-for'] || 
                               event.headers['x-real-ip'] || 
                               event.requestContext?.identity?.sourceIp || 
                               'unknown';
                const userAgent = event.headers['user-agent'] || 'webhook';

                await supabase.from('admin_logs').insert([{
                    admin_id: null,
                    admin_email: 'webhook@system',
                    action: 'webhook_reset_all_quotas',
                    target_type: 'users',
                    target_id: 'all_approved_users',
                    details: {
                        affected_users: resetData.length,
                        reset_date: today,
                        trigger_method: 'webhook',
                        auth_type: authType,
                        auth_preview: authType === 'permanent_key' ? 'permanent_key_***' : (temporaryToken ? temporaryToken.substring(0, 10) + '...' : 'unknown')
                    },
                    ip_address: clientIP,
                    user_agent: userAgent
                }]);
            } catch (logError) {
                console.error('Webhook log error:', logError);
                // Log hatası ana işlemi etkilemesin
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: `Webhook ile ${resetData.length} kullanıcının günlük özet hakkı başarıyla sıfırlandı`,
                    affected_users: resetData.length,
                    reset_date: today,
                    timestamp: new Date().toISOString(),
                    trigger: 'webhook'
                })
            };
        }

        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };

    } catch (error) {
        console.error('Webhook error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası',
                details: error.message
            })
        };
    }
};
