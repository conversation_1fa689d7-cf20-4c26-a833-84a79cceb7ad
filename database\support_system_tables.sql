-- Destek Sistemi Veritabanı Tabloları
-- Hosting sitelerindeki destek sistemi özelliklerini içeren kapsamlı yapı

-- 1. Destek Kategorileri Tablosu
CREATE TABLE public.support_categories (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    name character varying(100) NOT NULL,
    description text,
    color character varying(7) DEFAULT '#007bff', -- Hex renk kodu
    icon character varying(50) DEFAULT 'fas fa-question-circle',
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT support_categories_pkey PRIMARY KEY (id),
    CONSTRAINT support_categories_name_unique UNIQUE (name)
);

-- 2. Ana Destek Ticketları Tablosu
CREATE TABLE public.support_tickets (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    ticket_number character varying(20) NOT NULL UNIQUE, -- ST-2024-000001 formatında
    user_id uuid NOT NULL,
    category_id uuid,
    assigned_to uuid, -- Admin ID
    subject character varying(255) NOT NULL,
    description text NOT NULL,
    priority character varying(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    status character varying(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting', 'resolved', 'closed')),
    user_email character varying(255) NOT NULL, -- Kullanıcı emaili (denormalized for performance)
    user_name character varying(255) NOT NULL, -- Kullanıcı adı (denormalized)
    last_reply_at timestamp with time zone DEFAULT now(),
    last_reply_by character varying(20) DEFAULT 'user', -- 'user' or 'admin'
    reply_count integer DEFAULT 0,
    is_read_by_admin boolean DEFAULT false,
    is_read_by_user boolean DEFAULT true,
    closed_at timestamp with time zone,
    closed_by uuid, -- Admin ID who closed
    close_reason text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT support_tickets_pkey PRIMARY KEY (id),
    CONSTRAINT support_tickets_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    CONSTRAINT support_tickets_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.support_categories(id) ON DELETE SET NULL,
    CONSTRAINT support_tickets_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.admins(id) ON DELETE SET NULL,
    CONSTRAINT support_tickets_closed_by_fkey FOREIGN KEY (closed_by) REFERENCES public.admins(id) ON DELETE SET NULL
);

-- 3. Ticket Yanıtları/Mesajları Tablosu
CREATE TABLE public.support_ticket_replies (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    ticket_id uuid NOT NULL,
    user_id uuid, -- Kullanıcı yanıtı ise dolu
    admin_id uuid, -- Admin yanıtı ise dolu
    message text NOT NULL,
    is_admin_reply boolean DEFAULT false,
    is_internal_note boolean DEFAULT false, -- Sadece adminler görebilir
    attachments jsonb, -- Dosya ekleri için JSON array
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT support_ticket_replies_pkey PRIMARY KEY (id),
    CONSTRAINT support_ticket_replies_ticket_id_fkey FOREIGN KEY (ticket_id) REFERENCES public.support_tickets(id) ON DELETE CASCADE,
    CONSTRAINT support_ticket_replies_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL,
    CONSTRAINT support_ticket_replies_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES public.admins(id) ON DELETE SET NULL,
    CONSTRAINT support_ticket_replies_check CHECK ((user_id IS NOT NULL AND admin_id IS NULL) OR (user_id IS NULL AND admin_id IS NOT NULL))
);

-- 4. Ticket Durum Geçmişi Tablosu (Audit Trail)
CREATE TABLE public.support_ticket_history (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    ticket_id uuid NOT NULL,
    changed_by uuid, -- Admin ID
    field_name character varying(50) NOT NULL, -- 'status', 'priority', 'assigned_to', etc.
    old_value text,
    new_value text,
    change_reason text,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT support_ticket_history_pkey PRIMARY KEY (id),
    CONSTRAINT support_ticket_history_ticket_id_fkey FOREIGN KEY (ticket_id) REFERENCES public.support_tickets(id) ON DELETE CASCADE,
    CONSTRAINT support_ticket_history_changed_by_fkey FOREIGN KEY (changed_by) REFERENCES public.admins(id) ON DELETE SET NULL
);

-- 5. Varsayılan Kategorileri Ekle
INSERT INTO public.support_categories (name, description, color, icon, sort_order) VALUES
('Teknik Destek', 'Sistem kullanımı ve teknik sorunlar', '#dc3545', 'fas fa-tools', 1),
('Hesap Sorunları', 'Giriş, şifre ve hesap yönetimi', '#ffc107', 'fas fa-user-cog', 2),
('Faturalandırma', 'Ödeme ve fatura ile ilgili sorular', '#28a745', 'fas fa-credit-card', 3),
('Özellik Talebi', 'Yeni özellik önerileri', '#17a2b8', 'fas fa-lightbulb', 4),
('Genel Sorular', 'Diğer sorular ve bilgi talebi', '#6c757d', 'fas fa-question-circle', 5);

-- İndeksler (Performans için)
CREATE INDEX idx_support_tickets_user_id ON public.support_tickets(user_id);
CREATE INDEX idx_support_tickets_status ON public.support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON public.support_tickets(priority);
CREATE INDEX idx_support_tickets_assigned_to ON public.support_tickets(assigned_to);
CREATE INDEX idx_support_tickets_category_id ON public.support_tickets(category_id);
CREATE INDEX idx_support_tickets_created_at ON public.support_tickets(created_at DESC);
CREATE INDEX idx_support_tickets_last_reply_at ON public.support_tickets(last_reply_at DESC);
CREATE INDEX idx_support_tickets_ticket_number ON public.support_tickets(ticket_number);

CREATE INDEX idx_support_ticket_replies_ticket_id ON public.support_ticket_replies(ticket_id);
CREATE INDEX idx_support_ticket_replies_created_at ON public.support_ticket_replies(created_at);

CREATE INDEX idx_support_ticket_history_ticket_id ON public.support_ticket_history(ticket_id);
CREATE INDEX idx_support_ticket_history_created_at ON public.support_ticket_history(created_at DESC);

-- Trigger fonksiyonu: Ticket numarası otomatik oluşturma
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TRIGGER AS $$
DECLARE
    year_part text;
    sequence_num integer;
    ticket_num text;
BEGIN
    -- Yıl kısmını al
    year_part := EXTRACT(YEAR FROM NOW())::text;
    
    -- Bu yıl için sıradaki numarayı bul
    SELECT COALESCE(MAX(CAST(SUBSTRING(ticket_number FROM 9) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM support_tickets
    WHERE ticket_number LIKE 'ST-' || year_part || '-%';
    
    -- Ticket numarasını oluştur (ST-2024-000001 formatında)
    ticket_num := 'ST-' || year_part || '-' || LPAD(sequence_num::text, 6, '0');
    
    NEW.ticket_number := ticket_num;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger: Yeni ticket oluşturulduğunda otomatik numara ver
CREATE TRIGGER trigger_generate_ticket_number
    BEFORE INSERT ON public.support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION generate_ticket_number();

-- Trigger fonksiyonu: Updated_at otomatik güncelleme
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger: Ticket güncellendiğinde updated_at'i güncelle
CREATE TRIGGER trigger_update_support_tickets_updated_at
    BEFORE UPDATE ON public.support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger: Kategori güncellendiğinde updated_at'i güncelle
CREATE TRIGGER trigger_update_support_categories_updated_at
    BEFORE UPDATE ON public.support_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger fonksiyonu: Ticket istatistiklerini güncelle
CREATE OR REPLACE FUNCTION update_ticket_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Yeni yanıt eklendiğinde
        UPDATE support_tickets 
        SET 
            reply_count = reply_count + 1,
            last_reply_at = NEW.created_at,
            last_reply_by = CASE WHEN NEW.is_admin_reply THEN 'admin' ELSE 'user' END,
            is_read_by_admin = CASE WHEN NEW.is_admin_reply THEN true ELSE false END,
            is_read_by_user = CASE WHEN NEW.is_admin_reply THEN false ELSE true END,
            updated_at = NOW()
        WHERE id = NEW.ticket_id;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger: Yanıt eklendiğinde ticket istatistiklerini güncelle
CREATE TRIGGER trigger_update_ticket_stats
    AFTER INSERT ON public.support_ticket_replies
    FOR EACH ROW
    EXECUTE FUNCTION update_ticket_stats();
