const { createClient } = require('@supabase/supabase-js');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        const testEmail = `test-${Date.now()}@gmail.com`; // Geçerli format
        const results = {
            timestamp: new Date().toISOString(),
            tests: []
        };

        // Test 1: Environment variables
        results.tests.push({
            name: 'Environment Variables',
            status: supabaseUrl && supabaseKey ? 'PASS' : 'FAIL',
            details: {
                SUPABASE_URL: !!supabaseUrl,
                SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
                SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY,
                SITE_URL: !!process.env.SITE_URL
            }
        });

        // Test 2: Supabase connection
        try {
            const { data: users, error: listError } = await supabase.auth.admin.listUsers();
            results.tests.push({
                name: 'Supabase Auth Connection',
                status: listError ? 'FAIL' : 'PASS',
                details: {
                    error: listError?.message,
                    userCount: users?.users?.length || 0
                }
            });
        } catch (error) {
            results.tests.push({
                name: 'Supabase Auth Connection',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Test 3: Generate link test (using invite method)
        try {
            // Invite yöntemi test edildi ve çalışıyor, onu kullan
            const { data: inviteTestData, error: inviteTestError } = await supabase.auth.admin.inviteUserByEmail(testEmail, {
                redirectTo: `${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/verify-email?token=test`
            });

            let linkData = inviteTestData;
            let linkError = inviteTestError;

            // Test kullanıcısını temizle
            if (inviteTestData?.user?.id) {
                await supabase.auth.admin.deleteUser(inviteTestData.user.id);
            }

            results.tests.push({
                name: 'Generate Verification Link',
                status: linkError ? 'FAIL' : 'PASS',
                details: {
                    error: linkError?.message,
                    linkGenerated: !!linkData?.user,
                    method: 'invite'
                }
            });
        } catch (error) {
            results.tests.push({
                name: 'Generate Verification Link',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Test 4: Invite user test
        try {
            const inviteTestEmail = `invite-test-${Date.now()}@gmail.com`;
            const { data: inviteData, error: inviteError } = await supabase.auth.admin.inviteUserByEmail(inviteTestEmail, {
                redirectTo: `${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/verify-email?token=test`
            });

            results.tests.push({
                name: 'Invite User Email',
                status: inviteError ? 'FAIL' : 'PASS',
                details: {
                    error: inviteError?.message,
                    inviteGenerated: !!inviteData?.user
                }
            });

            // Test kullanıcısını temizle
            if (inviteData?.user?.id) {
                await supabase.auth.admin.deleteUser(inviteData.user.id);
            }
        } catch (error) {
            results.tests.push({
                name: 'Invite User Email',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Test 5: Database tables
        try {
            const { data: logsData, error: logsError } = await supabase
                .from('email_verification_logs')
                .select('count', { count: 'exact' })
                .limit(1);

            const { data: attemptsData, error: attemptsError } = await supabase
                .from('email_verification_attempts')
                .select('count', { count: 'exact' })
                .limit(1);

            results.tests.push({
                name: 'Database Tables',
                status: (logsError || attemptsError) ? 'FAIL' : 'PASS',
                details: {
                    email_verification_logs: logsError ? logsError.message : 'OK',
                    email_verification_attempts: attemptsError ? attemptsError.message : 'OK'
                }
            });
        } catch (error) {
            results.tests.push({
                name: 'Database Tables',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Test 6: Auth settings check
        try {
            // Supabase project settings'i kontrol etmek için bir dummy user oluşturmayı dene
            const { data: settingsTest, error: settingsError } = await supabase.auth.admin.createUser({
                email: '<EMAIL>',
                email_confirm: false
            });

            if (settingsTest?.user?.id) {
                await supabase.auth.admin.deleteUser(settingsTest.user.id);
            }

            results.tests.push({
                name: 'Auth Settings',
                status: settingsError ? 'FAIL' : 'PASS',
                details: {
                    error: settingsError?.message,
                    canCreateUsers: !settingsError
                }
            });
        } catch (error) {
            results.tests.push({
                name: 'Auth Settings',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Overall status
        const failedTests = results.tests.filter(test => test.status === 'FAIL');
        const overallStatus = failedTests.length === 0 ? 'ALL_PASS' : 'SOME_FAIL';

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                overallStatus: overallStatus,
                summary: {
                    total: results.tests.length,
                    passed: results.tests.filter(test => test.status === 'PASS').length,
                    failed: failedTests.length
                },
                results: results,
                recommendations: getRecommendations(results.tests)
            })
        };

    } catch (error) {
        console.error('Test email config error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Test failed',
                details: error.message
            })
        };
    }
};

function getRecommendations(tests) {
    const recommendations = [];
    
    tests.forEach(test => {
        if (test.status === 'FAIL') {
            switch (test.name) {
                case 'Environment Variables':
                    recommendations.push('Check Netlify environment variables: SUPABASE_URL, SUPABASE_SERVICE_KEY, SITE_URL');
                    break;
                case 'Supabase Auth Connection':
                    recommendations.push('Verify Supabase project URL and service key');
                    break;
                case 'Generate Verification Link':
                    recommendations.push('Check Supabase Auth settings: Enable email confirmations');
                    break;
                case 'Invite User Email':
                    recommendations.push('Configure SMTP settings in Supabase Dashboard > Authentication > Settings');
                    break;
                case 'Database Tables':
                    recommendations.push('Run the SQL setup script to create required tables');
                    break;
                case 'Auth Settings':
                    recommendations.push('Check Supabase Auth permissions and RLS policies');
                    break;
            }
        }
    });

    if (recommendations.length === 0) {
        recommendations.push('All tests passed! Email verification should work correctly.');
    }

    return recommendations;
}
