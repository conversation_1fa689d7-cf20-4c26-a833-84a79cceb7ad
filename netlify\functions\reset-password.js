const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');

// Supabase client - will be initialized in handler
// const supabase = createClient(
//     process.env.SUPABASE_URL,
//     process.env.SUPABASE_SERVICE_ROLE_KEY
// );

// Rate limiting için basit in-memory store
const rateLimitStore = new Map();

function checkRateLimit(ip, action, maxAttempts = 5, windowMs = 15 * 60 * 1000) {
    const key = `${ip}:${action}`;
    const now = Date.now();
    
    if (!rateLimitStore.has(key)) {
        rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
        return { allowed: true };
    }
    
    const record = rateLimitStore.get(key);
    
    if (now > record.resetTime) {
        rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
        return { allowed: true };
    }
    
    if (record.count >= maxAttempts) {
        return { allowed: false, resetTime: record.resetTime };
    }
    
    record.count++;
    return { allowed: true };
}

function getClientIP(event) {
    return event.headers['x-forwarded-for']?.split(',')[0] || 
           event.headers['x-real-ip'] || 
           'unknown';
}

function validatePassword(password) {
    if (!password || password.length < 6) {
        return { valid: false, error: 'Şifre en az 6 karakter olmalıdır.' };
    }
    
    if (password.length > 128) {
        return { valid: false, error: 'Şifre çok uzun.' };
    }
    
    return { valid: true };
}

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Rate limiting
        const clientIP = getClientIP(event);
        const rateLimit = checkRateLimit(clientIP, 'reset-password', 5, 15 * 60 * 1000);

        if (!rateLimit.allowed) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({
                    error: 'Çok fazla şifre sıfırlama denemesi. 15 dakika sonra tekrar deneyin.',
                    retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
                })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { token, email, password, confirmPassword } = body;

        if (!token || !email || !password || !confirmPassword) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Tüm alanlar gereklidir.' })
            };
        }

        // Şifre validasyonu
        const passwordValidation = validatePassword(password);
        if (!passwordValidation.valid) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: passwordValidation.error })
            };
        }

        // Şifre eşleşme kontrolü
        if (password !== confirmPassword) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Şifreler eşleşmiyor.' })
            };
        }

        // Token hash'i oluştur
        const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

        // Supabase client oluştur
        const serviceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        const supabaseClient = createClient(
            process.env.SUPABASE_URL,
            serviceKey
        );

        // Token'ı veritabanından kontrol et
        const { data: resetToken, error: tokenError } = await supabaseClient
            .from('password_reset_tokens')
            .select('*')
            .eq('token_hash', tokenHash)
            .eq('email', email)
            .is('used_at', null)
            .gte('expires_at', new Date().toISOString())
            .single();

        if (tokenError || !resetToken) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ 
                    error: 'Geçersiz veya süresi dolmuş şifre sıfırlama linki.' 
                })
            };
        }

        // Kullanıcıyı kontrol et
        const { data: user, error: userError } = await supabaseClient
            .from('users')
            .select('id, email, status')
            .eq('id', resetToken.user_id)
            .eq('email', email)
            .single();

        if (userError || !user) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
            };
        }

        // Kullanıcı durumu kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ 
                    error: 'Hesabınız henüz onaylanmamış.' 
                })
            };
        }

        // Yeni şifreyi hash'le
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Şifreyi güncelle
        const { error: updateError } = await supabaseClient
            .from('users')
            .update({
                password_hash: passwordHash,
                updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

        if (updateError) {
            console.error('Password update error:', updateError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Şifre güncellenirken hata oluştu.' })
            };
        }

        // Token'ı kullanıldı olarak işaretle
        await supabaseClient
            .from('password_reset_tokens')
            .update({
                used_at: new Date().toISOString(),
                ip_address: clientIP,
                user_agent: event.headers['user-agent'] || null
            })
            .eq('id', resetToken.id);

        // Kullanıcının diğer aktif token'larını da temizle
        await supabaseClient
            .from('password_reset_tokens')
            .update({ used_at: new Date().toISOString() })
            .eq('user_id', user.id)
            .is('used_at', null)
            .neq('id', resetToken.id);

        console.log('Password reset successful:', { userId: user.id, email: user.email });

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Şifreniz başarıyla güncellendi. Artık yeni şifrenizle giriş yapabilirsiniz.'
            })
        };

    } catch (error) {
        console.error('Reset password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Şifre sıfırlanırken hata oluştu.' })
        };
    }
};
