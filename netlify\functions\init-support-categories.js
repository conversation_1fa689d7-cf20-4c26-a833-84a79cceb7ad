// Initialize Support Categories

import { supabase } from '../../supabase-config.js';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Mevcut kategorileri kontrol et
        const { data: existingCategories, error: checkError } = await supabase
            .from('support_categories')
            .select('id, name')
            .limit(1);

        if (checkError) {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ 
                    error: 'Kategori tablosu kontrol edilemedi',
                    details: checkError.message 
                })
            };
        }

        // Eğer kategoriler varsa, ekleme
        if (existingCategories && existingCategories.length > 0) {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Kategoriler zaten mevcut',
                    existingCount: existingCategories.length
                })
            };
        }

        // Varsayılan kategorileri ekle
        const defaultCategories = [
            {
                name: 'Teknik Destek',
                description: 'Sistem kullanımı ve teknik sorunlar',
                color: '#dc3545',
                icon: 'fas fa-tools',
                sort_order: 1
            },
            {
                name: 'Hesap Sorunları',
                description: 'Giriş, şifre ve hesap yönetimi',
                color: '#ffc107',
                icon: 'fas fa-user-cog',
                sort_order: 2
            },
            {
                name: 'Faturalandırma',
                description: 'Ödeme ve fatura ile ilgili sorular',
                color: '#28a745',
                icon: 'fas fa-credit-card',
                sort_order: 3
            },
            {
                name: 'Özellik Talebi',
                description: 'Yeni özellik önerileri',
                color: '#17a2b8',
                icon: 'fas fa-lightbulb',
                sort_order: 4
            },
            {
                name: 'Genel Sorular',
                description: 'Diğer sorular ve bilgi talebi',
                color: '#6c757d',
                icon: 'fas fa-question-circle',
                sort_order: 5
            }
        ];

        const { data: insertedCategories, error: insertError } = await supabase
            .from('support_categories')
            .insert(defaultCategories)
            .select();

        if (insertError) {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ 
                    error: 'Kategoriler eklenemedi',
                    details: insertError.message 
                })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Varsayılan kategoriler başarıyla eklendi',
                categories: insertedCategories
            })
        };

    } catch (error) {
        console.error('Init support categories error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Sunucu hatası',
                details: error.message 
            })
        };
    }
}
