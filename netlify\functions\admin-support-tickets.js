// Admin Destek Talepleri Yönetimi API Endpoint

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Admin kontrolü - detaylı debug ile
        const { data: allAdmins, error: allError } = await supabase
            .from('admins')
            .select('id, email, role, is_active')
            .eq('email', decoded.email);

        const { data: adminById, error: byIdError } = await supabase
            .from('admins')
            .select('id, email, role, is_active')
            .eq('id', decoded.adminId);

        // Aktif admin'i bul
        const activeAdmin = allAdmins?.find(a => a.is_active === true);

        if (!activeAdmin) {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({
                    error: 'Admin yetkisi gerekli.',
                    debug: {
                        tokenAdminId: decoded.adminId,
                        tokenEmail: decoded.email,
                        allAdminsError: allError?.message,
                        byIdError: byIdError?.message,
                        allAdminsCount: allAdmins?.length || 0,
                        allAdmins: allAdmins,
                        adminById: adminById,
                        activeAdminFound: !!activeAdmin
                    }
                })
            };
        }

        // Aktif admin bulundu, devam et
        const admin = activeAdmin;

        if (event.httpMethod === 'GET') {
            return await handleGetTickets(event, admin);
        } else if (event.httpMethod === 'PUT') {
            return await handleUpdateTicket(event, admin);
        } else {
            return {
                statusCode: 405,
                headers,
                body: JSON.stringify({ error: 'Method not allowed' })
            };
        }

    } catch (error) {
        console.error('Admin support tickets error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}

async function handleGetTickets(event, admin) {
    try {
        // Query parametrelerini al
        const queryParams = event.queryStringParameters || {};
        const status = queryParams.status;
        const priority = queryParams.priority;
        const category = queryParams.category;
        const limit = parseInt(queryParams.limit) || 50;
        const offset = parseInt(queryParams.offset) || 0;

        // Destek taleplerini getir
        let query = supabase
            .from('support_tickets')
            .select(`
                id,
                ticket_number,
                subject,
                description,
                priority,
                status,
                user_email,
                user_name,
                reply_count,
                last_reply_at,
                last_reply_by,
                is_read_by_admin,
                created_at,
                updated_at,
                support_categories(
                    id,
                    name,
                    color,
                    icon
                )
            `)
            .order('created_at', { ascending: false })
            .range(offset, offset + limit - 1);

        // Filtreleri uygula
        if (status) {
            query = query.eq('status', status);
        }

        if (priority) {
            query = query.eq('priority', priority);
        }

        if (category) {
            query = query.eq('category_id', category);
        }

        const { data: tickets, error: ticketsError } = await query;

        if (ticketsError) {
            console.error('Tickets fetch error:', ticketsError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Destek talepleri getirilemedi.' })
            };
        }

        // İstatistikleri hesapla
        const stats = await calculateTicketStats();

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                tickets: tickets || [],
                stats: stats,
                pagination: {
                    limit: limit,
                    offset: offset,
                    total: tickets ? tickets.length : 0
                }
            })
        };

    } catch (error) {
        console.error('Get tickets error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Destek talepleri getirilemedi.' })
        };
    }
}

async function handleUpdateTicket(event, admin) {
    try {
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz JSON formatı.' })
            };
        }

        const { ticketId, status, priority, assignedTo, closeReason } = requestData;

        if (!ticketId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Ticket ID gerekli.' })
            };
        }

        // Ticket'ın varlığını kontrol et
        const { data: ticket, error: ticketError } = await supabase
            .from('support_tickets')
            .select('id, status, priority, assigned_to')
            .eq('id', ticketId)
            .single();

        if (ticketError || !ticket) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'Ticket bulunamadı.' })
            };
        }

        // Güncellenecek alanları hazırla
        const updateData = {
            updated_at: new Date().toISOString()
        };

        if (status && status !== ticket.status) {
            updateData.status = status;
            
            if (status === 'closed') {
                updateData.closed_at = new Date().toISOString();
                updateData.closed_by = admin.id;
                if (closeReason) {
                    updateData.close_reason = closeReason;
                }
            }
        }

        if (priority && priority !== ticket.priority) {
            updateData.priority = priority;
        }

        if (assignedTo !== undefined && assignedTo !== ticket.assigned_to) {
            updateData.assigned_to = assignedTo || null;
        }

        // Ticket'ı güncelle
        const { data: updatedTicket, error: updateError } = await supabase
            .from('support_tickets')
            .update(updateData)
            .eq('id', ticketId)
            .select()
            .single();

        if (updateError) {
            console.error('Ticket update error:', updateError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Ticket güncellenemedi.' })
            };
        }

        // Değişiklik geçmişini kaydet
        const historyData = {
            ticket_id: ticketId,
            changed_by: admin.id,
            field_name: 'status_priority_assignment',
            old_value: JSON.stringify({
                status: ticket.status,
                priority: ticket.priority,
                assigned_to: ticket.assigned_to
            }),
            new_value: JSON.stringify({
                status: updateData.status || ticket.status,
                priority: updateData.priority || ticket.priority,
                assigned_to: updateData.assigned_to !== undefined ? updateData.assigned_to : ticket.assigned_to
            }),
            change_reason: `Admin güncelleme: ${admin.email}`
        };

        await supabase
            .from('support_ticket_history')
            .insert([historyData]);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Ticket başarıyla güncellendi.',
                ticket: updatedTicket
            })
        };

    } catch (error) {
        console.error('Update ticket error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Ticket güncellenemedi.' })
        };
    }
}

async function calculateTicketStats() {
    try {
        // Durum bazında sayıları al
        const { data: statusCounts, error: statusError } = await supabase
            .from('support_tickets')
            .select('status, priority')
            .neq('status', 'closed');

        if (statusError) {
            console.error('Status counts error:', statusError);
            return {};
        }

        const stats = {
            open: 0,
            in_progress: 0,
            waiting: 0,
            resolved: 0,
            urgent: 0,
            pending_replies: 0
        };

        if (statusCounts) {
            statusCounts.forEach(ticket => {
                if (stats.hasOwnProperty(ticket.status)) {
                    stats[ticket.status]++;
                }
                if (ticket.priority === 'urgent') {
                    stats.urgent++;
                }
            });
        }

        // Yanıt bekleyen ticketları say
        const { data: pendingReplies, error: pendingError } = await supabase
            .from('support_tickets')
            .select('id')
            .eq('last_reply_by', 'user')
            .eq('is_read_by_admin', false)
            .neq('status', 'closed');

        if (!pendingError && pendingReplies) {
            stats.pending_replies = pendingReplies.length;
        }

        return stats;

    } catch (error) {
        console.error('Calculate stats error:', error);
        return {};
    }
}
