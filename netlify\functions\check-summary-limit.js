const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow GET and POST requests
    if (!['GET', 'POST'].includes(event.httpMethod)) {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Get authorization header
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Authorization token required' })
            };
        }

        const token = authHeader.substring(7);

        // Verify and decode JWT token
        let userData;
        try {
            userData = jwt.verify(token, process.env.JWT_SECRET);
            console.log('JWT verified successfully:', { userId: userData.userId, email: userData.email });
        } catch (e) {
            console.error('JWT verification failed:', e.message);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Invalid or expired token' })
            };
        }

        if (event.httpMethod === 'GET') {
            // Kullanıcının güncel durumunu veritabanından kontrol et
            const { data: currentUser, error: userError } = await supabase
                .from('users')
                .select('status, email_verified_at')
                .eq('id', userData.userId)
                .single();

            if (userError || !currentUser) {
                return {
                    statusCode: 404,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Kullanıcı bulunamadı.',
                        limit: { allowed: false, remaining: 0, limit_value: 0, used_today: 0 }
                    })
                };
            }

            // Admin onayı kontrolü (güncel status ile)
            if (currentUser.status !== 'approved') {
                return {
                    statusCode: 403,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Özet yapabilmek için admin onayı gereklidir.',
                        status: currentUser.status,
                        emailVerified: !!currentUser.email_verified_at,
                        limit: { allowed: false, remaining: 0, limit_value: 0, used_today: 0 }
                    })
                };
            }

            // Önce fonksiyon ile dene
            const { data, error } = await supabase.rpc('check_user_summary_limit', {
                user_id: userData.userId
            });

            if (error) {
                console.error('RPC function error:', error);
                // Fonksiyon yoksa fallback yap
                return await handleLimitCheckFallback(userData.userId, headers);
            }

            const limitInfo = data[0] || { allowed: false, remaining: 0, limit_value: 0, used_today: 0 };

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    limit: limitInfo
                })
            };
        }

        if (event.httpMethod === 'POST') {
            // Özet yapıldıktan sonra sayacı artır
            const { data, error } = await supabase.rpc('increment_summary_count', {
                user_id: userData.userId
            });

            if (error) {
                console.error('Count increment error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Sayaç güncellenemedi.' })
                };
            }

            if (!data) {
                return {
                    statusCode: 403,
                    headers,
                    body: JSON.stringify({ 
                        error: 'Günlük özet limitiniz dolmuş.',
                        limit_exceeded: true
                    })
                };
            }

            // Güncellenmiş limit bilgilerini al
            const { data: limitData, error: limitError } = await supabase.rpc('check_user_summary_limit', {
                user_id: userData.userId
            });

            const limitInfo = limitData && limitData[0] ? limitData[0] : { remaining: 0, limit_value: 0, used_today: 0 };

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Sayaç güncellendi.',
                    limit: limitInfo
                })
            };
        }

    } catch (error) {
        console.error('Summary limit check error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};

// Fallback fonksiyonu - RPC fonksiyonları yoksa kullanılır
async function handleLimitCheckFallback(userId, headers) {
    try {
        console.log('Using fallback limit check for user:', userId);

        // Kullanıcının güncel bilgilerini al
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('daily_summary_limit, daily_summary_count, last_summary_date')
            .eq('id', userId)
            .single();

        if (userError || !user) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Kullanıcı bulunamadı.',
                    limit: { allowed: false, remaining: 0, limit_value: 0, used_today: 0 }
                })
            };
        }

        const limit = user.daily_summary_limit || 5;
        const used = user.daily_summary_count || 0;
        const remaining = Math.max(0, limit - used);
        const allowed = used < limit;

        // Otomatik günlük sıfırlama kaldırıldı - sadece webhook ile sıfırlama

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                limit: {
                    allowed: allowed,
                    remaining: remaining,
                    limit_value: limit,
                    used_today: used
                }
            })
        };

    } catch (error) {
        console.error('Fallback limit check error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Limit kontrolü yapılamadı.',
                limit: { allowed: false, remaining: 0, limit_value: 0, used_today: 0 }
            })
        };
    }
}
