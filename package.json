{"name": "hukuki-belge-ozetleme", "version": "1.0.0", "description": "Hukuki belge özetleme web uygulaması", "main": "index.html", "scripts": {"build": "npm install && npm list @sendinblue/client", "dev": "netlify dev", "start": "node server.js", "serve": "node server.js", "test-email": "node -e \"console.log('Testing Brevo SDK...'); try { require('@sendinblue/client'); console.log('✅ Brevo SDK available'); } catch(e) { console.log('❌ Brevo SDK not available:', e.message); }\""}, "dependencies": {"@google/generative-ai": "^0.21.0", "@sendinblue/client": "^3.3.1", "@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.8"}, "devDependencies": {"netlify-cli": "^17.0.0"}, "keywords": ["hukuk", "özet", "pdf", "netlify"], "author": "Hu<PERSON>ki Belge Özetleme <PERSON>", "license": "MIT"}