// Admin şifre sıfırlama fonksiyonu
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const { email, newPassword } = JSON.parse(event.body || '{}');

        if (!email || !newPassword) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Email ve yeni şifre gerekli' })
            };
        }

        // Şifreyi hash'le
        const passwordHash = await bcrypt.hash(newPassword, 10);

        // Admin şifresini güncelle
        const { data, error } = await supabase
            .from('admins')
            .update({ 
                password_hash: passwordHash,
                updated_at: new Date().toISOString()
            })
            .eq('email', email)
            .select();

        if (error) {
            console.error('Password update error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Şifre güncellenemedi' })
            };
        }

        if (!data || data.length === 0) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'Admin bulunamadı' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true, 
                message: 'Admin şifresi başarıyla güncellendi',
                admin: {
                    email: data[0].email,
                    full_name: data[0].full_name
                }
            })
        };

    } catch (error) {
        console.error('Reset password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Şifre sıfırlama işlemi başarısız' })
        };
    }
};
