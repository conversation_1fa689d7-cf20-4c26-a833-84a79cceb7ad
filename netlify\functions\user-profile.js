const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Content-Type': 'application/json; charset=utf-8'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Get authorization header
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Authorization token required' })
            };
        }

        const token = authHeader.substring(7);

        // Verify and decode JWT token
        let userData;
        try {
            userData = jwt.verify(token, process.env.JWT_SECRET);
            console.log('JWT verified successfully:', { userId: userData.userId, email: userData.email });
        } catch (e) {
            console.error('JWT verification failed:', e.message);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Invalid or expired token' })
            };
        }

        // Get user profile from database using userId from JWT
        console.log('Fetching user profile for:', userData.userId);

        // Önce tüm kullanıcıları al, sonra filtrele (RLS bypass)
        const { data: allUsers, error: fetchError } = await supabase
            .from('users')
            .select('*');

        if (fetchError) {
            console.error('Database fetch error:', fetchError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Database fetch error',
                    message: fetchError.message,
                    code: fetchError.code
                })
            };
        }

        console.log('Total users fetched:', allUsers?.length || 0);

        // Kullanıcıyı manuel olarak filtrele (tüm statuslar dahil)
        const user = allUsers?.find(u =>
            u.id === userData.userId
        );

        if (!user) {
            console.log('User not found in results');
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({
                    error: 'User not found or not approved',
                    userId: userData.userId,
                    totalUsers: allUsers?.length || 0
                })
            };
        }



        console.log('User found:', { id: user.id, email: user.email, status: user.status });

        // Update last login time
        try {
            await supabase
                .from('users')
                .update({ last_login: new Date().toISOString() })
                .eq('id', user.id);
        } catch (updateError) {
            console.warn('Could not update last login:', updateError.message);
        }

        // Return user profile (exclude sensitive data)
        const userProfile = {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            phone: user.phone,
            profession: user.profession,
            reason: user.reason,
            status: user.status,
            created_at: user.created_at,
            last_login: user.last_login,
            daily_summary_limit: user.daily_summary_limit || 5,
            daily_summary_count: user.daily_summary_count || 0,
            last_summary_date: user.last_summary_date
        };

        console.log('Returning user profile:', userProfile);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                user: userProfile
            })
        };

    } catch (error) {
        console.error('Profile fetch error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};
