// Profile Page JavaScript

// UTF-8 string düzeltme fonksiyonu
function fixUTF8String(str) {
    if (!str) return str;

    try {
        // Eğer string zaten doğru UTF-8 ise, olduğu gibi döndür
        if (str === decodeURIComponent(encodeURIComponent(str))) {
            return str;
        }

        // UTF-8 düzeltme dene
        return decodeURIComponent(escape(str));
    } catch (e) {
        // Hata durumunda orijinal string'i döndür
        console.warn('UTF-8 fix failed for string:', str, e);
        return str;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // FontAwesome kontrolünü devre dışı bırak - modern CSS ile iconlar çalışıyor
    // checkFontAwesome();

    // Authentication kontrolü
    const authResult = checkUserAuth();
    if (authResult) {
        loadUserProfile();
        setupUserMenu();
    }
});

// FontAwesome fallback sistemi kaldırıldı - Modern CSS ile iconlar çalışıyor

function checkUserAuth() {
    const token = localStorage.getItem('userToken');
    const userData = localStorage.getItem('userData');

    console.log('Profile Auth Check:', {
        hasToken: !!token,
        hasUserData: !!userData,
        tokenLength: token ? token.length : 0,
        tokenPreview: token ? token.substring(0, 50) + '...' : 'none'
    });

    // Token varsa devam et, yoksa demo mode
    if (!token && !userData) {
        console.log('No auth data found, using demo mode');
        setDemoData();
        return true;
    }

    try {
        // JWT token'ı decode etmeye çalış
        let tokenData = null;

        // JWT token format kontrolü
        if (token && token.includes('.')) {
            try {
                // JWT token'ın payload kısmını al (base64 decode)
                const parts = token.split('.');
                if (parts.length === 3) {
                    // JWT formatında
                    let payload = parts[1];
                    // Base64 padding ekle
                    while (payload.length % 4) {
                        payload += '=';
                    }

                    // UTF-8 safe decode
                    try {
                        const decodedPayload = atob(payload);
                        tokenData = JSON.parse(decodeURIComponent(escape(decodedPayload)));
                    } catch (utf8Error) {
                        // Fallback to simple decode
                        tokenData = JSON.parse(atob(payload));
                    }

                    console.log('JWT Token data:', tokenData);
                } else {
                    console.warn('Invalid JWT format, treating as session token');
                    return true;
                }
            } catch (decodeError) {
                console.warn('JWT decode failed:', decodeError.message);
                console.log('Token preview:', token.substring(0, 50) + '...');
                // Decode edilemiyorsa geçerli kabul et
                return true;
            }
        } else {
            console.warn('Token is not JWT format, treating as session token');
            return true;
        }

        // Token süresi kontrolü - sadece JWT'de exp varsa kontrol et
        if (tokenData && tokenData.exp) {
            const currentTime = Math.floor(Date.now() / 1000);
            const timeLeft = tokenData.exp - currentTime;

            console.log('Token expiry check:', {
                exp: tokenData.exp,
                current: currentTime,
                timeLeft: timeLeft + ' seconds',
                expired: timeLeft <= 0
            });

            // Sadece gerçekten süresi dolmuşsa (negatif değer) yönlendir
            if (timeLeft <= -300) { // 5 dakika tolerans
                console.log('Token really expired, redirecting...');
                localStorage.removeItem('userToken');
                localStorage.removeItem('userData');
                alert('Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.');
                window.location.href = '/login';
                return false;
            } else if (timeLeft <= 0) {
                console.warn('Token expired but within tolerance, continuing...');
            }
        } else {
            console.log('No expiry in token, treating as valid');
        }

        // Kullanıcı adını navbar'da göster
        const userName = document.getElementById('user-name');
        if (userName) {
            const firstName = fixUTF8String(tokenData.firstName || 'Kullanıcı');
            const lastName = fixUTF8String(tokenData.lastName || '');
            const fullName = `${firstName} ${lastName}`.trim();
            console.log('Setting user name from token:', {
                originalFirstName: tokenData.firstName,
                originalLastName: tokenData.lastName,
                fixedFirstName: firstName,
                fixedLastName: lastName,
                fullName: fullName,
                charCodes: fullName.split('').map(c => c.charCodeAt(0))
            });
            userName.textContent = fullName;
        }

        return true;

    } catch (error) {
        console.error('Token decode error:', error);

        // Eğer userData varsa onu kullanmayı dene
        if (userData) {
            try {
                const userDataObj = JSON.parse(userData);
                console.log('Using userData fallback:', userDataObj);

                const userName = document.getElementById('user-name');
                if (userName) {
                    const firstName = fixUTF8String(userDataObj.firstName || 'Kullanıcı');
                    const lastName = fixUTF8String(userDataObj.lastName || '');
                    userName.textContent = `${firstName} ${lastName}`.trim();
                }
                return true;
            } catch (userDataError) {
                console.error('UserData parse error:', userDataError);
            }
        }

        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        alert('Oturum bilgileri geçersiz. Lütfen tekrar giriş yapın.');
        window.location.href = '/login.html';
        return false;
    }
}

async function loadUserProfile() {
    const token = localStorage.getItem('userToken');
    const userData = localStorage.getItem('userData');

    console.log('Loading user profile...');

    // Önce localStorage'dan temel bilgileri yükle (hızlı görüntüleme için)
    if (userData) {
        try {
            const userInfo = JSON.parse(userData);
            console.log('Loading basic info from localStorage:', userInfo);

            // Temel bilgileri hemen göster
            document.getElementById('profile-first-name').textContent = fixUTF8String(userInfo.first_name || userInfo.firstName || '-');
            document.getElementById('profile-last-name').textContent = fixUTF8String(userInfo.last_name || userInfo.lastName || '-');
            document.getElementById('profile-email').textContent = userInfo.email || '-';

            // Navbar'da kullanıcı adını göster
            const userName = document.getElementById('user-name');
            if (userName) {
                const firstName = fixUTF8String(userInfo.first_name || userInfo.firstName || 'Kullanıcı');
                const lastName = fixUTF8String(userInfo.last_name || userInfo.lastName || '');
                userName.textContent = `${firstName} ${lastName}`.trim();
            }
        } catch (e) {
            console.warn('UserData parse failed:', e);
        }
    }

    // JWT token'dan veri al
    let tokenData = {};
    if (token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            tokenData = payload;
            console.log('JWT Token data:', tokenData);
        } catch (e) {
            console.warn('JWT parse failed:', e);
        }
    }

    // Supabase'den detaylı profil bilgilerini çek
    if (token) {
        try {
            console.log('Fetching detailed profile from Supabase...');

            const response = await fetch('/.netlify/functions/user-profile', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Profile API response status:', response.status);

            if (response.ok) {
                const profileData = await response.json();
                console.log('Supabase profile data:', profileData);

                if (profileData.success && profileData.user) {
                    const user = profileData.user;

                    // Tüm profil bilgilerini güncelle
                    document.getElementById('profile-first-name').textContent = user.first_name || '-';
                    document.getElementById('profile-last-name').textContent = user.last_name || '-';
                    document.getElementById('profile-email').textContent = user.email || '-';
                    document.getElementById('profile-phone').textContent = user.phone || '-';
                    document.getElementById('profile-profession').textContent = formatProfession(user.profession) || '-';

                    // Hesap durumu bilgilerini güncelle (API verisi öncelikli)
                    const combinedUserData = {
                        ...user,
                        emailVerified: !!user.email_verified_at || tokenData.emailVerified,
                        status: user.status // API'den gelen status kullan (en güncel)
                    };
                    console.log('Combined user data for status update:', combinedUserData);
                    console.log('Status priority: API =', user.status, ', JWT =', tokenData.status, ', Final =', combinedUserData.status);

                    updateAccountStatus(combinedUserData);
                    document.getElementById('profile-reason').textContent = user.reason || '-';

                    // Tarih formatları
                    if (user.created_at) {
                        document.getElementById('profile-created-at').textContent =
                            new Date(user.created_at).toLocaleDateString('tr-TR', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            });
                    }

                    if (user.last_login) {
                        document.getElementById('profile-last-login').textContent =
                            new Date(user.last_login).toLocaleDateString('tr-TR', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                    } else {
                        document.getElementById('profile-last-login').textContent = 'İlk giriş';
                    }

                    // Durum badge'i
                    const statusElement = document.getElementById('profile-status');
                    if (user.status === 'approved') {
                        statusElement.textContent = 'Aktif';
                        statusElement.className = 'status-badge active';
                    } else if (user.status === 'suspended') {
                        statusElement.textContent = 'Askıda';
                        statusElement.className = 'status-badge suspended';
                    } else {
                        statusElement.textContent = 'Beklemede';
                        statusElement.className = 'status-badge pending';
                    }

                    // Navbar'da kullanıcı adını güncelle
                    const userName = document.getElementById('user-name');
                    if (userName) {
                        const firstName = fixUTF8String(user.first_name);
                        const lastName = fixUTF8String(user.last_name);
                        userName.textContent = `${firstName} ${lastName}`.trim();
                    }

                    // Günlük limit bilgilerini yükle (sadece approved kullanıcılar için)
                    if (combinedUserData.status === 'approved') {
                        loadDailyLimitInfo();
                    } else {
                        // Pending/suspended kullanıcılar için limit bilgilerini gizle
                        hideLimitInfo();
                    }

                    // Profil verilerinden limit bilgilerini de göster
                    if (user.daily_summary_limit !== undefined) {
                        displayLimitInfo({
                            limit_value: user.daily_summary_limit,
                            used_today: user.daily_summary_count,
                            remaining: Math.max(0, user.daily_summary_limit - user.daily_summary_count),
                            allowed: user.daily_summary_count < user.daily_summary_limit
                        });
                    }

                    console.log('Profile loaded successfully from Supabase');

                } else {
                    console.warn('Server response unsuccessful:', profileData);
                    console.error('Profile error:', profileData.error);
                    // API başarısız olursa JWT token'dan veri yükle
                    loadFromJWTToken(tokenData);
                }
            } else {
                console.error('Server response not ok:', response.status);

                if (response.status === 401) {
                    alert('Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.');
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('userData');
                    window.location.href = '/login';
                } else {
                    console.error('Profile fetch failed');
                    // API başarısız olursa JWT token'dan veri yükle
                    loadFromJWTToken(tokenData);
                }
            }
        } catch (error) {
            console.error('Profile API error:', error);
            // Hata durumunda JWT token'dan veri yükle
            loadFromJWTToken(tokenData);
        }
    } else {
        console.warn('No token available for profile fetch');
        setDefaultProfileValues();
    }
}

// JWT token'dan profil verilerini yükle (fallback)
function loadFromJWTToken(tokenData) {
    console.log('Loading profile from JWT token:', tokenData);

    if (tokenData && tokenData.email) {
        // Temel bilgileri JWT'den al
        document.getElementById('profile-first-name').textContent = fixUTF8String(tokenData.firstName || '-');
        document.getElementById('profile-last-name').textContent = fixUTF8String(tokenData.lastName || '-');
        document.getElementById('profile-email').textContent = tokenData.email || '-';

        // Navbar'da kullanıcı adını göster
        const userName = document.getElementById('user-name');
        if (userName) {
            const firstName = fixUTF8String(tokenData.firstName || 'Kullanıcı');
            const lastName = fixUTF8String(tokenData.lastName || '');
            userName.textContent = `${firstName} ${lastName}`.trim();
        }

        // Hesap durumu bilgilerini güncelle
        const userDataFromToken = {
            first_name: tokenData.firstName,
            last_name: tokenData.lastName,
            email: tokenData.email,
            status: tokenData.status,
            emailVerified: tokenData.emailVerified,
            phone: '-',
            profession: '-',
            created_at: null
        };

        updateAccountStatus(userDataFromToken);

        // Limit bilgilerini kontrol et
        if (tokenData.status === 'approved') {
            loadDailyLimitInfo();
            loadSupportTicketsCount(); // Destek kayıtları sayısını yükle
        } else {
            hideLimitInfo();
        }

        console.log('Profile loaded from JWT token');
    } else {
        console.warn('Invalid JWT token data');
        setDefaultProfileValues();
    }
}

function setDefaultProfileValues() {
    console.log('Setting default profile values');

    // API'den veri alamadığında default değerler
    document.getElementById('profile-phone').textContent = '-';
    document.getElementById('profile-profession').textContent = '-';
    document.getElementById('profile-reason').textContent = '-';
    document.getElementById('profile-created-at').textContent = '-';
    document.getElementById('profile-last-login').textContent = '-';

    // Status bilgileri
    const statusElement = document.getElementById('profile-status');
    statusElement.textContent = 'Veri Yüklenemedi';
    statusElement.className = 'status-badge unknown';

    // E-posta durumu
    const emailStatusElement = document.getElementById('profile-email-status');
    if (emailStatusElement) {
        emailStatusElement.textContent = 'Bilinmiyor';
        emailStatusElement.className = 'status-badge unknown';
    }

    // Status mesajlarını gizle
    const messagesContainer = document.getElementById('account-status-messages');
    if (messagesContainer) {
        messagesContainer.style.display = 'none';
    }

    setDefaultLimitInfo();
}

// Token'ı yenile (status değişikliği durumunda)
async function refreshUserToken() {
    const currentToken = localStorage.getItem('userToken');
    if (!currentToken) return;

    try {
        console.log('Refreshing user token...');

        // Mevcut token ile yeni token al
        const response = await fetch('/.netlify/functions/refresh-token', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.token) {
                localStorage.setItem('userToken', data.token);
                console.log('Token refreshed successfully');
            }
        } else {
            console.warn('Token refresh failed:', response.status);
        }
    } catch (error) {
        console.error('Token refresh error:', error);
    }
}

function setDemoData() {
    // Demo veriler - token olmadığında
    console.log('Setting demo data for profile');

    document.getElementById('profile-first-name').textContent = 'Demo';
    document.getElementById('profile-last-name').textContent = 'Kullanıcı';
    document.getElementById('profile-email').textContent = '<EMAIL>';
    document.getElementById('profile-phone').textContent = '0555 000 0000';
    document.getElementById('profile-profession').textContent = 'Test Kullanıcısı';
    document.getElementById('profile-reason').textContent = 'Demo amaçlı kullanım';
    document.getElementById('profile-created-at').textContent = new Date().toLocaleDateString('tr-TR');
    document.getElementById('profile-last-login').textContent = 'Şimdi';

    const statusElement = document.getElementById('profile-status');
    statusElement.textContent = 'Demo';
    statusElement.className = 'status-badge demo';

    // Navbar'da da demo kullanıcı adını göster
    const userName = document.getElementById('user-name');
    if (userName) {
        userName.textContent = 'Demo Kullanıcı';
    }
}

// Hata mesajı gösterme
function showError(message) {
    console.error('Profile Error:', message);
}

function formatProfession(profession) {
    const professionMap = {
        'avukat': 'Avukat',
        'hakim': 'Hakim',
        'savcı': 'Savcı',
        'katip': 'Katip',
        'hukuk-ogrencisi': 'Hukuk Öğrencisi',
        'akademisyen': 'Akademisyen',
        'diger': 'Diğer'
    };

    return professionMap[profession] || profession;
}

// Günlük limit bilgilerini yükle
async function loadDailyLimitInfo() {
    const token = localStorage.getItem('userToken');

    if (!token) {
        setDefaultLimitInfo();
        return;
    }

    try {
        console.log('Loading daily limit info...');

        const response = await fetch('/.netlify/functions/check-summary-limit', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Limit API response status:', response.status);

        if (response.ok) {
            const limitData = await response.json();
            console.log('Limit data:', limitData);

            if (limitData.success && limitData.limit) {
                const limit = limitData.limit;
                displayLimitInfo(limit);
                console.log('Daily limit info loaded successfully');
            } else {
                console.warn('Invalid limit data received');
                setDefaultLimitInfo();
            }
        } else {
            console.error('Failed to load limit info:', response.status);
            setDefaultLimitInfo();
        }
    } catch (error) {
        console.error('Error loading daily limit info:', error);
        setDefaultLimitInfo();
    }
}

// Limit bilgilerini göster
function displayLimitInfo(limit) {
    // Limit bilgilerini güncelle
    document.getElementById('daily-limit').textContent = limit.limit_value || 5;
    document.getElementById('used-today').textContent = limit.used_today || 0;
    document.getElementById('remaining-today').textContent = limit.remaining || 0;

    // Progress bar güncelle
    const usagePercentage = limit.limit_value > 0 ?
        Math.round((limit.used_today / limit.limit_value) * 100) : 0;

    document.getElementById('usage-percentage').textContent = usagePercentage;

    const progressFill = document.getElementById('usage-progress');
    progressFill.style.width = `${usagePercentage}%`;

    // Renk ayarla
    if (usagePercentage >= 100) {
        progressFill.style.backgroundColor = 'var(--danger)';
    } else if (usagePercentage >= 80) {
        progressFill.style.backgroundColor = 'var(--warning)';
    } else {
        progressFill.style.backgroundColor = 'var(--success)';
    }
}

// Default limit bilgilerini ayarla
function setDefaultLimitInfo() {
    displayLimitInfo({
        limit_value: 5,
        used_today: 0,
        remaining: 5
    });
}

function hideLimitInfo() {
    // Pending/suspended kullanıcılar için limit bilgilerini gizle
    displayLimitInfo({
        limit_value: 'Admin onayı gerekli',
        used_today: '-',
        remaining: '-'
    });
}

function setupUserMenu() {
    const userMenuBtn = document.getElementById('user-menu-btn');
    const userMenuDropdown = document.getElementById('user-menu-dropdown');
    
    if (userMenuBtn && userMenuDropdown) {
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleUserMenu();
        });
        
        // Dışarı tıklandığında menüyü kapat
        document.addEventListener('click', function(e) {
            if (!userMenuBtn.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                userMenuDropdown.style.display = 'none';
            }
        });
    }
}

function toggleUserMenu() {
    const userMenuDropdown = document.getElementById('user-menu-dropdown');
    const userMenuBtn = document.getElementById('user-menu-btn');
    
    if (userMenuDropdown.style.display === 'none' || !userMenuDropdown.style.display) {
        // Menüyü göster
        const rect = userMenuBtn.getBoundingClientRect();
        userMenuDropdown.style.display = 'block';
        userMenuDropdown.style.top = (rect.bottom + 5) + 'px';
        userMenuDropdown.style.right = (window.innerWidth - rect.right) + 'px';
    } else {
        // Menüyü gizle
        userMenuDropdown.style.display = 'none';
    }
}

function goToProfile() {
    // Zaten profil sayfasındayız, menüyü kapat
    document.getElementById('user-menu-dropdown').style.display = 'none';
}

function goHome() {
    window.location.href = '/';
}

function logout() {
    if (confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        window.location.href = '/';
    }
}

function showError(message) {
    // Basit error gösterimi
    alert(message);
}

// Hesap durumu mesajlarını güncelle
function updateAccountStatus(user) {
    console.log('updateAccountStatus called with:', user);

    const messagesContainer = document.getElementById('account-status-messages');
    const statusElement = document.getElementById('profile-status');
    const emailStatusElement = document.getElementById('profile-email-status');

    let messages = [];

    // E-posta durumu - hem API'den hem JWT'den kontrol et
    const isEmailVerified = user.email_verified_at || user.emailVerified;

    if (isEmailVerified) {
        emailStatusElement.textContent = 'Doğrulandı';
        emailStatusElement.className = 'status-badge verified';
    } else {
        emailStatusElement.textContent = 'Doğrulanmamış';
        emailStatusElement.className = 'status-badge not-verified';
        messages.push({
            type: 'warning',
            icon: '📧',
            title: 'E-posta Doğrulama Gerekli',
            text: 'E-posta adresinizi doğrulamanız gerekmektedir. E-posta kutunuzu kontrol edin.'
        });
    }

    // Hesap durumu
    if (user.status === 'pending') {
        statusElement.textContent = 'Admin Onayı Bekliyor';
        statusElement.className = 'status-badge pending';
        messages.push({
            type: 'info',
            icon: '⏳',
            title: 'Admin Onayı Bekleniyor',
            text: 'Hesabınız admin onayı beklemektedir. Onaylandıktan sonra tüm özellikleri kullanabileceksiniz.'
        });
    } else if (user.status === 'approved') {
        statusElement.textContent = 'Aktif';
        statusElement.className = 'status-badge active';
        if (isEmailVerified) {
            messages.push({
                type: 'success',
                icon: '✅',
                title: 'Hesabınız Aktif',
                text: 'Hesabınız aktif durumda. Tüm özellikleri kullanabilirsiniz.'
            });
        }
    } else if (user.status === 'suspended') {
        statusElement.textContent = 'Askıda';
        statusElement.className = 'status-badge suspended';
        messages.push({
            type: 'error',
            icon: '🚫',
            title: 'Hesap Askıda',
            text: 'Hesabınız askıya alınmıştır. Destek ile iletişime geçin.'
        });
    }

    // Mesajları göster
    if (messages.length > 0) {
        messagesContainer.innerHTML = messages.map(msg => `
            <div class="status-message ${msg.type}">
                <div class="message-icon">${msg.icon}</div>
                <div class="message-content">
                    <div class="message-title">${msg.title}</div>
                    <div class="message-text">${msg.text}</div>
                </div>
            </div>
        `).join('');
        messagesContainer.style.display = 'block';
    } else {
        messagesContainer.style.display = 'none';
    }
}

// Destek kayıtları sayısını yükle
async function loadSupportTicketsCount() {
    const token = localStorage.getItem('userToken');
    if (!token) return;

    try {
        const response = await fetch('/.netlify/functions/support-user-tickets?limit=1', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            const countElement = document.getElementById('support-tickets-count');

            if (data.stats && data.stats.total > 0) {
                const openCount = data.stats.open + data.stats.in_progress + data.stats.waiting;
                const unreadText = data.unreadReplies > 0 ? ` (${data.unreadReplies} yeni yanıt)` : '';

                if (openCount > 0) {
                    countElement.textContent = `${data.stats.total} kayıt, ${openCount} açık${unreadText}`;
                    countElement.style.color = '#fbbf24';
                } else {
                    countElement.textContent = `${data.stats.total} kayıt${unreadText}`;
                    countElement.style.color = 'rgba(255, 255, 255, 0.9)';
                }
            } else {
                countElement.textContent = 'Henüz kayıt yok';
                countElement.style.color = 'rgba(255, 255, 255, 0.7)';
            }
        }
    } catch (error) {
        console.error('Error loading support tickets count:', error);
    }
}
