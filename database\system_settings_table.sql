-- System Settings Tablosu zaten mevcut, sadece eksik ayarları ekleyeceğiz
-- Mevcut tablo yapısı:
-- - setting_value text NOT NULL (bizim kodda nullable)
-- - display_name column var (bizim kodda yok)
-- - setting_type column var (bizim kodda yok)

-- Updated_at trigger'ı ekle (eğer yoksa)
CREATE OR REPLACE FUNCTION update_system_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ı sil ve yeniden o<PERSON> (IF NOT EXISTS desteklenmiyor)
DROP TRIGGER IF EXISTS trigger_update_system_settings_updated_at ON public.system_settings;
CREATE TRIGGER trigger_update_system_settings_updated_at
    BEFORE UPDATE ON public.system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_system_settings_updated_at();

-- <PERSON><PERSON><PERSON><PERSON>lan Gemini model ayarını ekle (mevcut tablo yapısına uygun)
INSERT INTO public.system_settings (
    setting_key,
    setting_value,
    display_name,
    description,
    setting_type,
    options,
    is_active
)
VALUES (
    'gemini_model',
    'gemini-2.5-flash-lite-preview-06-17',
    'Gemini AI Model',
    'Aktif Gemini AI modeli',
    'select',
    '{
        "gemini-2.5-flash-lite-preview-06-17": "Gemini 2.5 Flash Lite Preview 06-17",
        "gemini-1.5-flash": "Gemini 1.5 Flash",
        "gemini-1.5-pro": "Gemini 1.5 Pro",
        "gemini-2.0-flash-exp": "Gemini 2.0 Flash Experimental"
    }'::jsonb,
    true
) ON CONFLICT (setting_key) DO UPDATE SET
    options = EXCLUDED.options,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Manuel API key ayarı için placeholder (mevcut tablo yapısına uygun)
-- NOT NULL constraint nedeniyle boş string kullanıyoruz
INSERT INTO public.system_settings (
    setting_key,
    setting_value,
    display_name,
    description,
    setting_type,
    is_active
)
VALUES (
    'manual_api_key',
    '',
    'Manuel API Key',
    'Manuel olarak seçilen API key',
    'hidden',
    false
) ON CONFLICT (setting_key) DO NOTHING;
