<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-posta Doğrulama - Hukuki Belge Özetleme</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .verify-container {
            width: 100%;
            max-width: 500px;
        }

        .verify-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            text-align: center;
        }

        .verify-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
        }

        .verify-header h1 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .verify-header p {
            opacity: 0.9;
            font-size: 1rem;
        }

        .verify-content {
            padding: 3rem 2rem;
        }

        .status-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
        }

        .status-icon.loading {
            background: #e3f2fd;
            color: #1976d2;
            animation: pulse 2s infinite;
        }

        .status-icon.success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-icon.error {
            background: #ffebee;
            color: #d32f2f;
        }

        .status-icon.warning {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-icon.info {
            background: #e3f2fd;
            color: #1976d2;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1a1a1a;
        }

        .status-message {
            font-size: 1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .verified-email {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin: 1.5rem 0;
            font-weight: 500;
            color: #495057;
        }

        .next-steps {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }

        .next-steps h3 {
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .next-steps ul {
            list-style: none;
        }

        .next-steps li {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
            color: #666;
        }

        .next-steps li i {
            margin-right: 0.8rem;
            color: #667eea;
            width: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .verify-footer {
            text-align: center;
            margin-top: 2rem;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .verify-content {
                padding: 2rem 1.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-card">
            <div class="verify-header">
                <h1>⚖️ LegalAI</h1>
                <p>E-posta Doğrulama Sistemi</p>
            </div>

            <!-- Loading State -->
            <div class="verify-content" id="verify-loading">
                <div class="status-icon loading">
                    <i class="fas fa-spinner loading-spinner"></i>
                </div>
                <h2 class="status-title">E-posta Doğrulanıyor...</h2>
                <p class="status-message">Lütfen bekleyin, e-posta adresiniz doğrulanıyor.</p>
            </div>

            <!-- Success State -->
            <div class="verify-content" id="verify-success" style="display: none;">
                <div class="status-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="status-title">E-posta Doğrulandı!</h2>
                <p class="status-message">E-posta adresiniz başarıyla doğrulandı.</p>
                <div class="verified-email" id="verified-email-display"></div>
                <div class="next-steps">
                    <h3>Sonraki Adımlar:</h3>
                    <ul>
                        <li><i class="fas fa-clock"></i> Hesabınız admin onayı bekliyor</li>
                        <li><i class="fas fa-envelope"></i> Onay durumu e-posta ile bildirilecek</li>
                        <li><i class="fas fa-hourglass-half"></i> Onay süreci 24-48 saat sürebilir</li>
                    </ul>
                </div>
                <div class="action-buttons">
                    <a href="/login" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Giriş Sayfasına Git
                    </a>
                    <a href="/" class="btn btn-secondary">
                        <i class="fas fa-home"></i>
                        Ana Sayfaya Dön
                    </a>
                </div>
            </div>

            <!-- Error State -->
            <div class="verify-content" id="verify-error" style="display: none;">
                <div class="status-icon error">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="status-title">Doğrulama Başarısız</h2>
                <p class="status-message" id="error-message">Bir hata oluştu.</p>
                <div class="error-details" id="error-details" style="display: none;"></div>
                <div class="action-buttons">
                    <a href="/apply" class="btn btn-primary">
                        <i class="fas fa-redo"></i>
                        Yeniden Başvur
                    </a>
                    <a href="/" class="btn btn-secondary">
                        <i class="fas fa-home"></i>
                        Ana Sayfaya Dön
                    </a>
                </div>
            </div>

            <!-- Already Verified State -->
            <div class="verify-content" id="verify-already" style="display: none;">
                <div class="status-icon info">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h2 class="status-title">Zaten Doğrulanmış</h2>
                <p class="status-message">Bu e-posta adresi zaten doğrulanmış durumda.</p>
                <div class="action-buttons">
                    <a href="/login" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Giriş Yap
                    </a>
                    <a href="/" class="btn btn-secondary">
                        <i class="fas fa-home"></i>
                        Ana Sayfaya Dön
                    </a>
                </div>
            </div>

            <!-- Expired State -->
            <div class="verify-content" id="verify-expired" style="display: none;">
                <div class="status-icon warning">
                    <i class="fas fa-clock"></i>
                </div>
                <h2 class="status-title">Doğrulama Süresi Dolmuş</h2>
                <p class="status-message">Doğrulama linkinin süresi dolmuş. Lütfen yeni bir doğrulama e-postası isteyin.</p>
                <div class="action-buttons">
                    <a href="/apply" class="btn btn-primary">
                        <i class="fas fa-envelope"></i>
                        Yeni Doğrulama E-postası İste
                    </a>
                    <a href="/" class="btn btn-secondary">
                        <i class="fas fa-home"></i>
                        Ana Sayfaya Dön
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="verify-footer">
            <p>&copy; 2024 Hukuki Belge Özetleme Sistemi. Tüm hakları saklıdır.</p>
        </div>
    </div>

    <script>
        // URL'den token'ı al
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');

        // Sayfa yüklendiğinde doğrulama işlemini başlat
        document.addEventListener('DOMContentLoaded', function() {
            if (token) {
                verifyEmail(token);
            } else {
                showError('Doğrulama token\'ı bulunamadı.');
            }
        });

        // E-posta doğrulama fonksiyonu
        async function verifyEmail(token) {
            try {
                showLoading();

                const response = await fetch('/.netlify/functions/verify-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: token })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showSuccess(result.email, result.message);
                } else {
                    // Hata türüne göre farklı durumlar göster
                    if (result.error && result.error.includes('zaten doğrulanmış')) {
                        showAlreadyVerified();
                    } else if (result.error && result.error.includes('süresi dolmuş')) {
                        showExpired();
                    } else {
                        showError(result.error || 'Doğrulama işlemi başarısız.');
                    }
                }

            } catch (error) {
                console.error('Verification error:', error);
                showError('Bağlantı hatası. Lütfen tekrar deneyin.');
            }
        }

        // Loading durumunu göster
        function showLoading() {
            hideAllStates();
            document.getElementById('verify-loading').style.display = 'block';
        }

        // Success durumunu göster
        function showSuccess(email, message) {
            hideAllStates();
            document.getElementById('verify-success').style.display = 'block';
            
            if (email) {
                document.getElementById('verified-email-display').innerHTML = 
                    `<strong>${email}</strong>`;
            }
        }

        // Error durumunu göster
        function showError(message, details = null) {
            hideAllStates();
            document.getElementById('verify-error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
            
            if (details) {
                document.getElementById('error-details').textContent = details;
                document.getElementById('error-details').style.display = 'block';
            }
        }

        // Already verified durumunu göster
        function showAlreadyVerified() {
            hideAllStates();
            document.getElementById('verify-already').style.display = 'block';
        }

        // Expired durumunu göster
        function showExpired() {
            hideAllStates();
            document.getElementById('verify-expired').style.display = 'block';
        }

        // Tüm durumları gizle
        function hideAllStates() {
            const states = [
                'verify-loading',
                'verify-success', 
                'verify-error',
                'verify-already',
                'verify-expired'
            ];
            
            states.forEach(stateId => {
                document.getElementById(stateId).style.display = 'none';
            });
        }
    </script>
</body>
</html>
