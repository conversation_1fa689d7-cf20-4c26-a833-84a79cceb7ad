// <PERSON><PERSON>ıcı başvuru function'ı - Supabase entegrasyonu
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY // Service key kullan (RLS bypass için)
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Content-Type': 'application/json; charset=utf-8'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const body = JSON.parse(event.body || '{}');
        const { firstName, lastName, email, phone, password, profession, reason } = body;

        // Basit validasyon
        if (!firstName || !lastName || !email || !phone || !password || !profession || !reason) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Tüm alanlar zorunludur.' })
            };
        }

        // E-posta format kontrolü
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz e-posta formatı.' })
            };
        }

        // Şifre uzunluk kontrolü
        if (password.length < 6) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Şifre en az 6 karakter olmalıdır.' })
            };
        }

        // E-posta zaten var mı kontrol et
        const { data: existingUser } = await supabase
            .from('users')
            .select('email')
            .eq('email', email)
            .single();

        if (existingUser) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Bu e-posta adresi zaten kayıtlı.' })
            };
        }

        // Bekleyen başvuru var mı kontrol et
        const { data: existingApplication } = await supabase
            .from('applications')
            .select('email')
            .eq('email', email)
            .eq('status', 'pending')
            .single();

        if (existingApplication) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Bu e-posta adresi için zaten bekleyen bir başvuru var.' })
            };
        }

        // Şifreyi hash'le
        const passwordHash = await bcrypt.hash(password, 10);

        // Başvuruyu Supabase'e kaydet
        const { data: application, error } = await supabase
            .from('applications')
            .insert([{
                email,
                first_name: firstName,
                last_name: lastName,
                phone,
                password_hash: passwordHash,
                profession,
                reason,
                status: 'pending'
            }])
            .select()
            .single();

        if (error) {
            console.error('Supabase error:', error);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Başvuru kaydedilirken bir hata oluştu.' })
            };
        }

        // Telegram bildirimi gönder
        try {
            await sendTelegramNotification({
                firstName,
                lastName,
                email,
                phone,
                profession,
                reason
            });
        } catch (telegramError) {
            console.error('Telegram notification failed:', telegramError);
            // Telegram hatası başvuru işlemini etkilemesin
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Başvurunuz başarıyla alındı. 24-48 saat içinde e-posta adresinize bilgilendirme yapılacaktır.',
                applicationId: application.id
            })
        };
        
    } catch (error) {
        console.error('User apply error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Başvuru işlenirken bir hata oluştu.' })
        };
    }
};

// Telegram bildirimi gönder
async function sendTelegramNotification(userInfo) {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const chatId = process.env.TELEGRAM_CHAT_ID;

    if (!botToken || !chatId) {
        console.log('Telegram bot credentials not configured, skipping notification');
        return;
    }

    // Yeni format ile Telegram bildirim fonksiyonunu çağır
    const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-telegram-notification`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'user_registration',
            userInfo: userInfo
        })
    });

    if (!response.ok) {
        const errorData = await response.json();
        console.error('Telegram notification failed:', errorData);
        throw new Error(`Telegram notification failed: ${errorData.error}`);
    } else {
        console.log('Telegram user registration notification sent successfully');
    }

    return new Promise((resolve, reject) => {
        const urlObj = new URL(telegramApiUrl);

        const options = {
            hostname: urlObj.hostname,
            port: 443,
            path: urlObj.pathname,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (response.ok) {
                        console.log('Telegram notification sent successfully');
                        resolve(response);
                    } else {
                        console.error('Telegram API Error:', response.description);
                        reject(new Error(response.description));
                    }
                } catch (parseError) {
                    console.error('Telegram response parse error:', parseError);
                    reject(parseError);
                }
            });
        });

        req.on('error', (error) => {
            console.error('Telegram request error:', error);
            reject(error);
        });

        req.write(postData);
        req.end();
    });
}
