<!DOCTYPE html>
<html>
<head>
    <title>Function Test</title>
</head>
<body>
    <h1>Function Test</h1>
    <button onclick="testFunction()">Test Function</button>
    <div id="result"></div>

    <script>
        async function testFunction() {
            try {
                const response = await fetch('/.netlify/functions/generate-summary', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: 'Test metni' })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
