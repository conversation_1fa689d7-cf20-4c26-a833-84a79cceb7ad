const { createClient } = require('@supabase/supabase-js');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Content-Type': 'application/json; charset=utf-8'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        let token;
        
        // Token'ı query parameter'dan veya body'den al
        if (event.httpMethod === 'GET') {
            token = event.queryStringParameters?.token;
        } else if (event.httpMethod === 'POST') {
            const body = JSON.parse(event.body);
            token = body.token;
        }

        if (!token) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Doğrulama token\'ı gerekli' })
            };
        }

        // IP adresini al
        const clientIP = event.headers['x-forwarded-for'] || 
                        event.headers['x-real-ip'] || 
                        event.requestContext?.identity?.sourceIp || 
                        'unknown';

        // URL'den token'ı al (Resend sistemi için basit token)
        const urlParams = new URLSearchParams(event.queryStringParameters || {});

        // Resend ile gönderilen e-postalarda basit token kullanıyoruz
        console.log('Verifying email with token:', token);
        // Manuel token kontrolü (Resend sistemi)
        const { data: verificationLog, error: logError } = await supabase
            .from('email_verification_logs')
            .select('*')
            .eq('verification_token', token)
            .eq('status', 'sent')
            .single();

        if (logError || !verificationLog) {
            console.error('Verification log error:', logError);
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz veya süresi dolmuş doğrulama token\'ı' })
            };
        }

        const email = verificationLog.email;

        // Token'ın süresi dolmuş mu kontrol et (24 saat)
        const tokenAge = new Date() - new Date(verificationLog.sent_at);
        const maxAge = 24 * 60 * 60 * 1000; // 24 saat

        if (tokenAge > maxAge) {
            // Token'ı expired olarak işaretle
            await supabase
                .from('email_verification_logs')
                .update({ status: 'expired' })
                .eq('verification_token', token);

            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    error: 'Doğrulama token\'ının süresi dolmuş. Lütfen yeni bir doğrulama emaili isteyin.'
                })
            };
        }

        // Applications tablosundan başvuruyu al
        const { data: application, error: appError } = await supabase
            .from('applications')
            .select('*')
            .eq('email', email)
            .eq('status', 'pending')
            .single();

        if (appError || !application) {
            console.error('Application lookup error:', appError);
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Başvuru bulunamadı veya zaten işlenmiş' })
            };
        }

        // Users tablosunda zaten var mı kontrol et
        const { data: existingUser } = await supabase
            .from('users')
            .select('id')
            .eq('email', email)
            .single();

        if (existingUser) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Bu e-posta adresi zaten kayıtlı' })
            };
        }

        // Application'da e-posta doğrulama durumunu güncelle (users tablosuna taşımadan)
        await supabase
            .from('applications')
            .update({
                email_verified_at: new Date().toISOString(),
                // status 'pending' olarak kalır, admin onayı bekler
            })
            .eq('id', application.id);



        // Verification log'u güncelle
        await supabase
            .from('email_verification_logs')
            .update({
                status: 'verified',
                verified_at: new Date().toISOString()
            })
            .eq('verification_token', token);

        // Admin log ekle
        try {
            await supabase.from('admin_logs').insert([{
                admin_id: null,
                admin_email: 'system',
                action: 'email_verified',
                target_type: 'user',
                target_id: verificationLog.user_id,
                details: { 
                    email: verificationLog.email,
                    verification_token: token,
                    ip_address: clientIP
                },
                ip_address: clientIP,
                user_agent: event.headers['user-agent'] || 'unknown'
            }]);
        } catch (logError) {
            console.error('Admin log error:', logError);
            // Log hatası ana işlemi etkilemesin
        }

        // Rate limiting kaydını temizle
        try {
            await supabase
                .from('email_verification_attempts')
                .delete()
                .eq('email', verificationLog.email);
        } catch (cleanupError) {
            console.error('Cleanup error:', cleanupError);
        }

        // Admin'lere email doğrulama bildirimi gönder
        try {
            await sendEmailVerificationNotification(application);
        } catch (emailError) {
            console.error('Admin email notification failed:', emailError);
            // Email hatası doğrulama işlemini etkilemesin
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'E-posta başarıyla doğrulandı! Başvurunuz admin onayı bekliyor.',
                email: email,
                redirectUrl: '/login?verified=true'
            })
        };

    } catch (error) {
        console.error('Email verification error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası' })
        };
    }
};

// Yardımcı fonksiyonlar

// Token validation
function isValidToken(token) {
    return token && typeof token === 'string' && token.length === 64;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Admin'lere email doğrulama bildirimi gönder
async function sendEmailVerificationNotification(application) {
    try {
        console.log('📧 Sending email verification notification to admins for:', application.email);

        // Aktif admin'leri al
        const { data: admins, error: adminError } = await supabase
            .from('admins')
            .select('email')
            .eq('is_active', true);

        if (adminError) {
            console.error('Admin fetch error:', adminError);
            return;
        }

        if (!admins || admins.length === 0) {
            console.log('No active admins found');
            return;
        }

        const adminEmails = admins.map(admin => admin.email);
        console.log('Sending notification to admins:', adminEmails);

        // Email bildirimini gönder
        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-admin-notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'email_verified',
                data: {
                    application: application,
                    adminEmails: adminEmails
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Admin notification failed:', errorData);
        } else {
            console.log('✅ Email verification notification sent to admins successfully');
        }

    } catch (error) {
        console.error('Send admin notification error:', error);
    }
}
