exports.handler = async (event, context) => {
    console.log('<PERSON><PERSON> forgot password function called');
    
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        console.log('Environment variables check:');
        console.log('SUPABASE_URL exists:', !!process.env.SUPABASE_URL);
        console.log('SUPABASE_SERVICE_ROLE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);
        console.log('RESEND_API_KEY exists:', !!process.env.RESEND_API_KEY);

        const body = JSON.parse(event.body || '{}');
        const { email } = body;

        console.log('Processing email:', email);

        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'E-posta adresi gereklidir.' })
            };
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçerli bir e-posta adresi girin.' })
            };
        }

        // Environment variables check
        if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
            console.error('Missing Supabase environment variables');
            console.error('SUPABASE_URL:', process.env.SUPABASE_URL ? 'SET' : 'NOT SET');
            console.error('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT SET');
            
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ 
                    error: 'Server configuration error',
                    details: 'Supabase environment variables missing',
                    debug: {
                        hasSupabaseUrl: !!process.env.SUPABASE_URL,
                        hasSupabaseKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
                    }
                })
            };
        }

        // Try to initialize Supabase
        try {
            const { createClient } = require('@supabase/supabase-js');
            const supabase = createClient(
                process.env.SUPABASE_URL,
                process.env.SUPABASE_SERVICE_ROLE_KEY
            );
            
            console.log('Supabase client created successfully');

            // Test database connection
            const { data, error } = await supabase
                .from('users')
                .select('id, email')
                .eq('email', email)
                .single();

            console.log('Database query result:', { found: !!data, error: error?.message });

            if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
                console.error('Database error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ 
                        error: 'Database connection error',
                        details: error.message
                    })
                };
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Test başarılı - Şifre sıfırlama sistemi çalışıyor.',
                    debug: {
                        userFound: !!data,
                        email: email,
                        timestamp: new Date().toISOString()
                    }
                })
            };

        } catch (supabaseError) {
            console.error('Supabase initialization error:', supabaseError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ 
                    error: 'Database initialization error',
                    details: supabaseError.message
                })
            };
        }

    } catch (error) {
        console.error('Forgot password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Şifre sıfırlama isteği işlenirken hata oluştu.',
                details: error.message 
            })
        };
    }
};
