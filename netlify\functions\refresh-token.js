const jwt = require('jsonwebtoken');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Authorization header kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli' })
            };
        }

        const token = authHeader.substring(7);

        // Mevcut token'ı verify et
        let userData;
        try {
            userData = jwt.verify(token, process.env.JWT_SECRET);
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token' })
            };
        }

        // Kullanıcının güncel bilgilerini al
        const { data: user, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', userData.userId)
            .single();

        if (error || !user) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı' })
            };
        }

        // Yeni JWT token oluştur (güncel bilgilerle)
        const newToken = jwt.sign(
            {
                userId: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                status: user.status,
                emailVerified: !!user.email_verified_at,
                role: 'user'
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                token: newToken,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    status: user.status,
                    emailVerified: !!user.email_verified_at
                }
            })
        };

    } catch (error) {
        console.error('Token refresh error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası' })
        };
    }
};
