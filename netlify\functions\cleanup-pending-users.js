const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    try {
        console.log('Starting cleanup of pending users...');

        // 1. Users tablosundaki pending kullanıcıları bul
        const { data: pendingUsers, error: fetchError } = await supabase
            .from('users')
            .select('*')
            .eq('status', 'pending');

        if (fetchError) {
            console.error('Error fetching pending users:', fetchError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Pending kullanıcılar getirilemedi' })
            };
        }

        console.log(`Found ${pendingUsers.length} pending users to cleanup`);

        let movedCount = 0;
        let errorCount = 0;

        // 2. Her pending kullanıcı için
        for (const user of pendingUsers) {
            try {
                console.log(`Processing user: ${user.email}`);

                // Applications tablosunda bu email ile başvuru var mı kontrol et
                const { data: existingApp, error: appCheckError } = await supabase
                    .from('applications')
                    .select('*')
                    .eq('email', user.email)
                    .single();

                if (appCheckError && appCheckError.code !== 'PGRST116') {
                    console.error(`Error checking application for ${user.email}:`, appCheckError);
                    errorCount++;
                    continue;
                }

                if (existingApp) {
                    // Zaten applications tablosunda var, sadece email_verified_at güncelle
                    await supabase
                        .from('applications')
                        .update({
                            email_verified_at: user.email_verified_at || new Date().toISOString()
                        })
                        .eq('email', user.email);
                    
                    console.log(`Updated existing application for ${user.email}`);
                } else {
                    // Applications tablosunda yok, yeni kayıt oluştur
                    const { error: insertError } = await supabase
                        .from('applications')
                        .insert([{
                            email: user.email,
                            first_name: user.first_name,
                            last_name: user.last_name,
                            phone: user.phone,
                            password_hash: user.password_hash,
                            profession: user.profession,
                            reason: 'Sistem tarafından taşındı', // Default reason
                            status: 'pending',
                            email_verified_at: user.email_verified_at || new Date().toISOString(),
                            created_at: user.created_at
                        }]);

                    if (insertError) {
                        console.error(`Error creating application for ${user.email}:`, insertError);
                        errorCount++;
                        continue;
                    }

                    console.log(`Created new application for ${user.email}`);
                }

                // Users tablosundan sil
                const { error: deleteError } = await supabase
                    .from('users')
                    .delete()
                    .eq('id', user.id);

                if (deleteError) {
                    console.error(`Error deleting user ${user.email}:`, deleteError);
                    errorCount++;
                    continue;
                }

                console.log(`Successfully moved ${user.email} from users to applications`);
                movedCount++;

            } catch (userError) {
                console.error(`Error processing user ${user.email}:`, userError);
                errorCount++;
            }
        }

        console.log(`Cleanup completed. Moved: ${movedCount}, Errors: ${errorCount}`);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: `Temizlik tamamlandı. ${movedCount} kullanıcı taşındı, ${errorCount} hata.`,
                moved: movedCount,
                errors: errorCount,
                total: pendingUsers.length
            })
        };

    } catch (error) {
        console.error('Cleanup error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Temizlik işlemi başarısız' })
        };
    }
};
