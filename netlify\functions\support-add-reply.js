// Destek <PERSON>na <PERSON>ıt Ekleme API Endpoint

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece POST metodunu kabul et
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ 
                error: 'Method not allowed',
                method: event.httpMethod,
                allowedMethods: ['POST', 'OPTIONS']
            })
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ 
                    error: 'Geçersiz token.',
                    details: jwtError.message 
                })
            };
        }

        // Request body'yi parse et
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz JSON formatı.' })
            };
        }

        const { ticketId, message, isAdminReply = false, isInternalNote = false } = requestData;

        // Input validasyonu
        if (!ticketId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Ticket ID gerekli.' })
            };
        }

        if (!message || message.trim().length < 5) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Mesaj en az 5 karakter olmalıdır.' })
            };
        }

        if (message.trim().length > 2000) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Mesaj 2000 karakterden uzun olamaz.' })
            };
        }

        let userId = null;
        let adminId = null;
        let userInfo = null;

        if (isAdminReply) {
            // Admin yanıtı - admin kontrolü (email ile)
            const { data: allAdmins, error: allError } = await supabase
                .from('admins')
                .select('id, email, full_name, is_active')
                .eq('email', decoded.email);

            // Aktif admin'i bul
            const activeAdmin = allAdmins?.find(a => a.is_active === true);

            if (!activeAdmin) {
                return {
                    statusCode: 403,
                    headers,
                    body: JSON.stringify({ 
                        error: 'Admin yetkisi gerekli.',
                        debug: {
                            tokenEmail: decoded.email,
                            allAdminsCount: allAdmins?.length || 0,
                            allAdmins: allAdmins,
                            allError: allError?.message,
                            activeAdminFound: !!activeAdmin
                        }
                    })
                };
            }

            adminId = activeAdmin.id;
            userInfo = activeAdmin;
        } else {
            // Kullanıcı yanıtı - kullanıcı kontrolü
            const { data: user, error: userError } = await supabase
                .from('users')
                .select('id, email, first_name, last_name, status')
                .eq('id', decoded.userId)
                .single();

            if (userError || !user) {
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
                };
            }

            if (user.status !== 'approved') {
                return {
                    statusCode: 403,
                    headers,
                    body: JSON.stringify({ 
                        error: 'Yanıt eklemek için hesabınızın onaylanmış olması gerekir.' 
                    })
                };
            }

            userId = user.id;
            userInfo = user;
        }

        // Ticket'ın varlığını ve erişim yetkisini kontrol et (tüm gerekli bilgilerle)
        let ticketQuery = supabase
            .from('support_tickets')
            .select('id, user_id, status, subject, ticket_number, description, priority, created_at')
            .eq('id', ticketId);

        if (!isAdminReply) {
            // Kullanıcı sadece kendi ticketına yanıt verebilir
            ticketQuery = ticketQuery.eq('user_id', userId);
        }

        const { data: ticket, error: ticketError } = await ticketQuery.single();

        if (ticketError || !ticket) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'Ticket bulunamadı veya erişim yetkiniz yok.' })
            };
        }

        // Kapalı ticket'a yanıt verilemez (admin hariç)
        if (ticket.status === 'closed' && !isAdminReply) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Kapalı ticket\'a yanıt verilemez.' })
            };
        }

        // Kullanıcı yanıt kısıtlaması kontrolü
        if (!isAdminReply) {
            const replyLimitCheck = await checkUserReplyLimit(ticketId, userId);
            if (!replyLimitCheck.allowed) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({
                        error: replyLimitCheck.error,
                        details: replyLimitCheck.details
                    })
                };
            }
        }

        // IP adresini parse et (virgülle ayrılmış IP'lerden ilkini al)
        let ipAddress = event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown';
        if (ipAddress && ipAddress !== 'unknown') {
            // Virgülle ayrılmış IP'lerden ilkini al ve temizle
            ipAddress = ipAddress.split(',')[0].trim();
        }

        // Yanıt ekle
        const replyData = {
            ticket_id: ticketId,
            user_id: userId,
            admin_id: adminId,
            message: message.trim(),
            is_admin_reply: isAdminReply,
            is_internal_note: isInternalNote && isAdminReply,
            ip_address: ipAddress,
            user_agent: event.headers['user-agent'] || 'unknown'
        };

        console.log('Inserting reply data:', replyData);

        const { data: reply, error: replyError } = await supabase
            .from('support_ticket_replies')
            .insert([replyData])
            .select()
            .single();

        if (replyError) {
            console.error('Reply creation error:', replyError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Yanıt eklenemedi.',
                    details: replyError.message,
                    code: replyError.code,
                    hint: replyError.hint,
                    replyData: replyData
                })
            };
        }

        // Ticket durumunu güncelle
        const ticketUpdateData = {
            last_reply_at: new Date().toISOString(),
            last_reply_by: isAdminReply ? 'admin' : 'user',
            is_read_by_admin: isAdminReply ? true : false,
            is_read_by_user: isAdminReply ? false : true,
            updated_at: new Date().toISOString()
        };

        // Eğer ticket waiting durumundaysa ve admin yanıt verdiyse, in_progress yap
        if (ticket.status === 'waiting' && isAdminReply) {
            ticketUpdateData.status = 'in_progress';
        }

        await supabase
            .from('support_tickets')
            .update(ticketUpdateData)
            .eq('id', ticketId);

        // E-posta bildirimi gönder (async, hata durumunda devam et)
        try {
            // Admin bilgilerini de gönder
            const adminInfo = isAdminReply ? userInfo : null;
            await sendReplyNotification(ticket, reply, userInfo, isAdminReply, adminInfo);
        } catch (emailError) {
            console.error('Email notification error:', emailError);
            // E-posta hatası yanıt eklenmesini engellemez
        }

        // Telegram bildirimi gönder (async, hata durumunda devam et)
        try {
            const adminInfo = isAdminReply ? userInfo : null;
            await sendTelegramReplyNotification(ticket, reply, userInfo, isAdminReply, adminInfo);
        } catch (telegramError) {
            console.error('Telegram notification error:', telegramError);
            // Telegram hatası yanıt eklenmesini engellemez
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Yanıt başarıyla eklendi.',
                reply: {
                    id: reply.id,
                    message: reply.message,
                    is_admin_reply: reply.is_admin_reply,
                    created_at: reply.created_at,
                    author_name: isAdminReply
                        ? userInfo.full_name || 'Destek Ekibi'
                        : `${userInfo.first_name} ${userInfo.last_name}`.trim()
                }
            })
        };

    } catch (error) {
        console.error('Support add reply error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası.',
                details: error.message,
                stack: error.stack
            })
        };
    }
}

// E-posta bildirimi gönder
async function sendReplyNotification(ticket, reply, userInfo, isAdminReply, adminInfo = null) {
    try {
        let recipientEmail = null;

        if (isAdminReply) {
            // Admin yanıt verdi, kullanıcıya bildir
            // Ticket sahibinin e-postasını al
            const { data: ticketOwner, error: ownerError } = await supabase
                .from('users')
                .select('email, first_name, last_name')
                .eq('id', ticket.user_id)
                .single();

            if (ownerError || !ticketOwner) {
                console.error('Ticket owner not found:', ownerError);
                return;
            }

            recipientEmail = ticketOwner.email;
        } else {
            // Kullanıcı yanıt verdi, admin'lere bildir
            const adminEmails = await getAdminEmails();
            if (adminEmails.length === 0) {
                console.log('No admin emails found for notification');
                return;
            }
            recipientEmail = adminEmails; // Array olarak gönder
        }

        if (!recipientEmail) {
            console.log('No recipient email found for notification');
            return;
        }

        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/support-send-notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'ticket_reply',
                data: {
                    ticket: ticket,
                    reply: reply,
                    recipientEmail: recipientEmail,
                    isAdminReply: isAdminReply,
                    adminInfo: adminInfo // Admin bilgilerini ekle
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Reply email notification failed:', errorData);
        } else {
            console.log('Reply email notification sent successfully');
        }

    } catch (error) {
        console.error('Send reply notification error:', error);
    }
}

// Telegram yanıt bildirimi gönder
async function sendTelegramReplyNotification(ticket, reply, userInfo, isAdminReply, adminInfo = null) {
    try {
        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-telegram-notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'support_ticket_reply',
                ticketInfo: {
                    ticket_number: ticket.ticket_number,
                    subject: ticket.subject
                },
                replyInfo: {
                    message: reply.message,
                    is_admin_reply: isAdminReply
                },
                userInfo: isAdminReply ? adminInfo : {
                    first_name: userInfo.first_name,
                    last_name: userInfo.last_name,
                    email: userInfo.email,
                    full_name: adminInfo?.full_name || adminInfo?.fullName
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Telegram reply notification failed:', errorData);
        } else {
            console.log('Telegram reply notification sent successfully');
        }

    } catch (error) {
        console.error('Send telegram reply notification error:', error);
    }
}

// Admin e-postalarını al
async function getAdminEmails() {
    try {
        const { data: admins, error } = await supabase
            .from('admins')
            .select('email')
            .eq('is_active', true);

        if (error) {
            console.error('Admin emails fetch error:', error);
            return [];
        }

        return admins.map(admin => admin.email);
    } catch (error) {
        console.error('Get admin emails error:', error);
        return [];
    }
}

// Kullanıcı yanıt limiti kontrolü
async function checkUserReplyLimit(ticketId, userId) {
    try {
        // Ticket'taki tüm yanıtları kronolojik sırayla al
        const { data: replies, error } = await supabase
            .from('support_ticket_replies')
            .select('id, is_admin_reply, created_at, user_id, admin_id')
            .eq('ticket_id', ticketId)
            .order('created_at', { ascending: true });

        if (error) {
            console.error('Replies fetch error for limit check:', error);
            return { allowed: false, error: 'Yanıt geçmişi kontrol edilemedi.' };
        }

        if (!replies || replies.length === 0) {
            // İlk yanıt, izin ver
            return { allowed: true };
        }

        // Kullanıcının yanıtlarını filtrele
        const userReplies = replies.filter(reply =>
            !reply.is_admin_reply && reply.user_id === userId
        );

        // Admin yanıtlarını filtrele
        const adminReplies = replies.filter(reply => reply.is_admin_reply);

        // Son yanıtın kim tarafından verildiğini kontrol et
        const lastReply = replies[replies.length - 1];
        const lastReplyIsAdmin = lastReply.is_admin_reply;

        console.log('Reply limit check:', {
            ticketId,
            userId,
            totalReplies: replies.length,
            userRepliesCount: userReplies.length,
            adminRepliesCount: adminReplies.length,
            lastReplyIsAdmin
        });

        // Kural: Kullanıcı admin cevap vermeden sadece 1 cevap verebilir
        // Admin cevap verince kullanıcı tekrar 1 cevap verebilir

        if (lastReplyIsAdmin) {
            // Son yanıt admin'den geldi, kullanıcı 1 yanıt verebilir
            // Son admin yanıtından sonra kullanıcı yanıtı var mı kontrol et
            const lastAdminReplyIndex = replies.length - 1;

            // Son admin yanıtından sonra kullanıcı yanıtı var mı?
            const userRepliesAfterLastAdmin = replies.slice(lastAdminReplyIndex + 1)
                .filter(reply => !reply.is_admin_reply && reply.user_id === userId);

            if (userRepliesAfterLastAdmin.length > 0) {
                return {
                    allowed: false,
                    error: 'Admin cevap verdikten sonra sadece 1 yanıt verebilirsiniz. Admin\'in yeni cevabını bekleyin.',
                    details: {
                        userRepliesAfterLastAdmin: userRepliesAfterLastAdmin.length,
                        lastAdminReplyAt: lastReply.created_at
                    }
                };
            }

            return { allowed: true };
        } else {
            // Son yanıt kullanıcıdan geldi
            // Kullanıcı admin cevap vermeden sadece 1 yanıt verebilir

            // Son admin yanıtından sonra kaç kullanıcı yanıtı var?
            let lastAdminReplyIndex = -1;
            for (let i = replies.length - 1; i >= 0; i--) {
                if (replies[i].is_admin_reply) {
                    lastAdminReplyIndex = i;
                    break;
                }
            }

            let userRepliesAfterLastAdmin;
            if (lastAdminReplyIndex === -1) {
                // Hiç admin yanıtı yok, tüm kullanıcı yanıtlarını say
                userRepliesAfterLastAdmin = userReplies;
            } else {
                // Son admin yanıtından sonraki kullanıcı yanıtlarını say
                userRepliesAfterLastAdmin = replies.slice(lastAdminReplyIndex + 1)
                    .filter(reply => !reply.is_admin_reply && reply.user_id === userId);
            }

            if (userRepliesAfterLastAdmin.length >= 1) {
                return {
                    allowed: false,
                    error: 'Admin cevap vermeden sadece 1 yanıt verebilirsiniz. Admin\'in cevabını bekleyin.',
                    details: {
                        userRepliesAfterLastAdmin: userRepliesAfterLastAdmin.length,
                        lastAdminReplyIndex,
                        hasAdminReplied: lastAdminReplyIndex !== -1
                    }
                };
            }

            return { allowed: true };
        }

    } catch (error) {
        console.error('User reply limit check error:', error);
        return { allowed: false, error: 'Yanıt limiti kontrol edilemedi.' };
    }
}
