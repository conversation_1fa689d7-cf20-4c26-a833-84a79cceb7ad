// Kullanıcı giriş function'ı - Supabase entegrasyonu
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { getClientIP, checkRateLimit } = require('./security-middleware');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Content-Type': 'application/json; charset=utf-8'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Rate limiting kontrolü
        const clientIP = getClientIP(event);
        const rateLimit = checkRateLimit(clientIP, 'user-login');

        if (!rateLimit.allowed) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({
                    error: 'Çok fazla giriş denemesi. Lütfen daha sonra tekrar deneyin.',
                    retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
                })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { email, password } = body;

        if (!email || !password) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'E-posta ve şifre gereklidir.' })
            };
        }

        // Kullanıcıyı Supabase'den getir (tüm statuslar dahil)
        console.log('🔍 Searching for user in users table:', email);
        const { data: user, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .single();

        console.log('👤 Users table result:', {
            found: !!user,
            error: error?.message,
            user_id: user?.id,
            email_verified_at: user?.email_verified_at
        });

        if (error || !user) {
            console.log('❌ User not found in users table, checking applications...');
            // Users tablosunda yoksa, applications tablosunu kontrol et
            const { data: application, error: appError } = await supabase
                .from('applications')
                .select('email, status, email_verified_at, created_at')
                .eq('email', email)
                .single();

            if (appError || !application) {
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Geçersiz e-posta veya şifre.'
                    })
                };
            }

            // Application var ama henüz users tablosuna taşınmamış
            console.log('Application email verification check:', {
                email: application.email,
                email_verified_at: application.email_verified_at,
                status: application.status,
                hasVerification: !!application.email_verified_at
            });

            if (!application.email_verified_at) {
                console.log('🚨 Application email not verified, returning verification required error');
                const errorResponse = {
                    success: false,
                    error: 'Halen email doğrulaması yapmadınız. Başvuru yaptıktan sonra size gönderilen doğrulama e-postasındaki linke tıklayın.',
                    type: 'email_verification_required',
                    email: email
                };
                console.log('📤 Sending error response:', errorResponse);
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify(errorResponse)
                };
            } else if (application.status === 'pending') {
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Başvurunuz henüz onaylanmamış. Admin onayı bekleniyor.',
                        type: 'admin_approval_required',
                        email: email
                    })
                };
            } else if (application.status === 'rejected') {
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Başvurunuz reddedilmiş. Yeni bir başvuru yapabilirsiniz.',
                        type: 'application_rejected',
                        email: email
                    })
                };
            } else {
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({
                        success: false,
                        error: 'Hesap durumunuz belirsiz. Lütfen destek ile iletişime geçin.',
                        type: 'unknown_status',
                        email: email
                    })
                };
            }
        }

        // Email doğrulama kontrolü (users tablosundaki kullanıcılar için)
        console.log('User email verification check:', {
            email: user.email,
            email_verified_at: user.email_verified_at,
            hasVerification: !!user.email_verified_at
        });

        if (!user.email_verified_at) {
            console.log('🚨 User email not verified, returning verification required error');
            const errorResponse = {
                success: false,
                error: 'Halen email doğrulaması yapmadınız. Lütfen e-posta kutunuzu kontrol edin ve doğrulama linkine tıklayın.',
                type: 'email_verification_required',
                email: email
            };
            console.log('📤 Sending error response:', errorResponse);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify(errorResponse)
            };
        }

        // Şifre kontrolü
        const passwordMatch = await bcrypt.compare(password, user.password_hash);

        if (!passwordMatch) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Geçersiz e-posta veya şifre.'
                })
            };
        }

        // Son giriş zamanını güncelle
        await supabase
            .from('users')
            .update({ last_login: new Date().toISOString() })
            .eq('id', user.id);

        // Debug: Kullanıcı adını kontrol et
        console.log('User data from database:', {
            firstName: user.first_name,
            lastName: user.last_name,
            firstNameCharCodes: user.first_name ? user.first_name.split('').map(c => c.charCodeAt(0)) : null,
            lastNameCharCodes: user.last_name ? user.last_name.split('').map(c => c.charCodeAt(0)) : null
        });

        // JWT token oluştur (kullanıcı durumu bilgileriyle)
        const token = jwt.sign(
            {
                userId: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                status: user.status,
                emailVerified: !!user.email_verified_at,
                role: 'user'
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                token,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    status: user.status,
                    emailVerified: !!user.email_verified_at,
                    emailVerifiedAt: user.email_verified_at
                }
            })
        };
        
    } catch (error) {
        console.error('User login error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Giriş işlenirken bir hata oluştu.' })
        };
    }
};
