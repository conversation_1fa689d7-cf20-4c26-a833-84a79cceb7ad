const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
const { sendEmail } = require('./utils/email-service');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Rate limiting constants
const MAX_ATTEMPTS_PER_EMAIL = 3; // 3 deneme (daha sıkı)
const MAX_ATTEMPTS_PER_IP = 5; // 5 deneme (daha sıkı)
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 dakika (daha sıkı)
const BLOCK_DURATION = 60 * 60 * 1000; // 1 saat (daha kısa)

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const { email } = JSON.parse(event.body);
        
        if (!email || !isValidEmail(email)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçerli bir email adresi gerekli' })
            };
        }

        // IP adresini al
        const clientIP = event.headers['x-forwarded-for'] || 
                        event.headers['x-real-ip'] || 
                        event.requestContext?.identity?.sourceIp || 
                        'unknown';

        // Rate limiting kontrolü
        const rateLimitCheck = await checkRateLimit(email, clientIP);
        if (!rateLimitCheck.allowed) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({ 
                    error: rateLimitCheck.message,
                    blockedUntil: rateLimitCheck.blockedUntil
                })
            };
        }

        // Kullanıcının zaten doğrulanmış olup olmadığını kontrol et
        const { data: existingUser, error: userError } = await supabase
            .from('users')
            .select('id, email, email_verified_at, status')
            .eq('email', email)
            .single();

        if (userError && userError.code !== 'PGRST116') {
            console.error('User check error:', userError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı kontrolü yapılamadı' })
            };
        }

        if (existingUser && existingUser.email_verified_at) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Bu email adresi zaten doğrulanmış' })
            };
        }

        // Verification token oluştur
        const verificationToken = crypto.randomBytes(32).toString('hex');
        const verificationUrl = `${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/verify-email?token=${verificationToken}`;

        // NOT: Authentication tablosunu kullanmıyoruz, sadece applications ve users tabloları kullanılıyor
        // Bu nedenle auth.admin.createUser çağrısını kaldırıyoruz
        console.log('Skipping auth user creation - using custom user management');

        // Verification log kaydet
        await logVerificationAttempt(
            existingUser?.id || null, // Auth user kullanmıyoruz
            email,
            verificationToken,
            clientIP,
            event.headers['user-agent'] || 'unknown'
        );

        // Rate limiting kaydını güncelle
        await updateRateLimitAttempt(email, clientIP);

        // Email gönder (dinamik email service kullan)
        console.log('Sending email via dynamic email service...');
        const emailResult = await sendVerificationEmailWithDynamicService(email, verificationUrl, verificationToken);

        if (!emailResult.success) {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: emailResult.error || 'Email gönderilemedi' })
            };
        }

        console.log('Email sent successfully via', emailResult.provider);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Doğrulama emaili gönderildi',
                email: email
            })
        };

    } catch (error) {
        console.error('Send verification email error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası' })
        };
    }
};

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Rate limiting kontrolü
async function checkRateLimit(email, ipAddress) {
    try {
        const now = new Date();
        const windowStart = new Date(now.getTime() - RATE_LIMIT_WINDOW);

        // Email bazlı kontrol
        const { data: emailAttempts, error: emailError } = await supabase
            .from('email_verification_attempts')
            .select('*')
            .eq('email', email)
            .gte('last_attempt_at', windowStart.toISOString())
            .single();

        if (emailAttempts && emailAttempts.attempt_count >= MAX_ATTEMPTS_PER_EMAIL) {
            if (emailAttempts.blocked_until && new Date(emailAttempts.blocked_until) > now) {
                return {
                    allowed: false,
                    message: 'Bu email adresi için çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.',
                    blockedUntil: emailAttempts.blocked_until
                };
            }
        }

        // IP bazlı kontrol
        const { data: ipAttempts, error: ipError } = await supabase
            .from('email_verification_attempts')
            .select('*')
            .eq('ip_address', ipAddress)
            .gte('last_attempt_at', windowStart.toISOString());

        if (ipAttempts && ipAttempts.length >= MAX_ATTEMPTS_PER_IP) {
            return {
                allowed: false,
                message: 'Bu IP adresinden çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.',
                blockedUntil: new Date(now.getTime() + BLOCK_DURATION).toISOString()
            };
        }

        return { allowed: true };

    } catch (error) {
        console.error('Rate limit check error:', error);
        return { allowed: true }; // Hata durumunda izin ver
    }
}

// Rate limiting kaydını güncelle
async function updateRateLimitAttempt(email, ipAddress) {
    try {
        const now = new Date();
        
        // Email için kayıt
        const { data: existing, error: fetchError } = await supabase
            .from('email_verification_attempts')
            .select('*')
            .eq('email', email)
            .single();

        if (existing) {
            const newCount = existing.attempt_count + 1;
            const blockedUntil = newCount >= MAX_ATTEMPTS_PER_EMAIL ? 
                new Date(now.getTime() + BLOCK_DURATION).toISOString() : null;

            await supabase
                .from('email_verification_attempts')
                .update({
                    attempt_count: newCount,
                    last_attempt_at: now.toISOString(),
                    blocked_until: blockedUntil
                })
                .eq('email', email);
        } else {
            await supabase
                .from('email_verification_attempts')
                .insert([{
                    email: email,
                    ip_address: ipAddress,
                    attempt_count: 1,
                    last_attempt_at: now.toISOString()
                }]);
        }

    } catch (error) {
        console.error('Update rate limit attempt error:', error);
    }
}

// Resend ile e-posta gönderme (ana yöntem)
async function sendVerificationEmailWithResend(email, verificationUrl, token) {
    try {
        // Resend API key kontrolü
        if (!process.env.RESEND_API_KEY) {
            console.log('Resend API key not configured:', !!process.env.RESEND_API_KEY);
            console.log('Available env vars:', Object.keys(process.env).filter(k => k.includes('RESEND')));
            return false;
        }

        console.log('Resend API key found, initializing...');
        const { Resend } = require('resend');
        const resend = new Resend(process.env.RESEND_API_KEY);

        console.log('Sending email to:', email);

        // Production modu: Verified domain kullan
        const testMode = !process.env.RESEND_DOMAIN_VERIFIED;
        const targetEmail = testMode ? '<EMAIL>' : email;

        console.log('Test mode:', testMode, 'Target email:', targetEmail);

        const { data, error } = await resend.emails.send({
            from: 'LegalAI <<EMAIL>>', // Verified domain
            to: [targetEmail],
            subject: `E-posta Adresinizi Doğrulayın - LegalAI ${testMode ? '(Test Mode)' : ''}`,
            html: `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>E-posta Doğrulama</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; }
                        .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
                        .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>⚖️ LegalAI</h1>
                        <h2>E-posta Doğrulama</h2>
                    </div>
                    <div class="content">
                        <p>Merhaba,</p>
                        ${testMode ? `<p><strong>Test Modu:</strong> Bu e-posta ${email} adresi için gönderildi.</p>` : ''}
                        <p>LegalAI hesabınızı oluşturduğunuz için teşekkürler. E-posta adresinizi doğrulamak için aşağıdaki butona tıklayın:</p>

                        <div style="text-align: center;">
                            <a href="${verificationUrl}" class="button">
                                ✅ E-posta Adresimi Doğrula
                            </a>
                        </div>

                        <p>Eğer buton çalışmıyorsa, aşağıdaki linki kopyalayıp tarayıcınıza yapıştırın:</p>
                        <p style="word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px;">
                            ${verificationUrl}
                        </p>

                        <p><strong>Önemli:</strong></p>
                        <ul>
                            <li>Bu link 24 saat geçerlidir</li>
                            <li>E-posta doğrulandıktan sonra hesabınız admin onayı bekleyecektir</li>
                            <li>Onay durumu e-posta ile bildirilecektir</li>
                        </ul>

                        <p>Eğer bu işlemi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.</p>
                    </div>
                    <div class="footer">
                        <p>LegalAI - Hukuki Belge Özetleme Sistemi</p>
                        <p>Bu e-posta otomatik olarak gönderilmiştir.</p>
                    </div>
                </body>
                </html>
            `
        });

        if (error) {
            console.error('Resend email error:', error);
            return false;
        }

        console.log('Resend email sent successfully:', data);
        return true;

    } catch (error) {
        console.error('Resend email send error:', error);
        return false;
    }
}

// Dinamik email service ile e-posta gönderme
async function sendVerificationEmailWithDynamicService(email, verificationUrl, token) {
    try {
        console.log('Sending verification email to:', email);

        // Email ayarlarını Supabase'den al
        console.log('🔧 Fetching email settings from Supabase...');
        const { data: emailSettings, error: settingsError } = await supabase
            .from('system_settings')
            .select('setting_key, setting_value, setting_type')
            .in('setting_key', [
                'email_enabled',
                'email_service_provider',
                'email_test_mode',
                'email_test_address',
                'brevo_api_key',
                'brevo_sender_email',
                'brevo_sender_name',
                'smtp_host',
                'smtp_port',
                'smtp_username',
                'smtp_password',
                'smtp_secure',
                'smtp_sender_email',
                'smtp_sender_name'
            ])
            .eq('is_active', true);

        if (settingsError) {
            console.error('❌ Failed to fetch email settings:', settingsError);
            return { success: false, error: 'Email ayarları alınamadı: ' + settingsError.message };
        }

        console.log('✅ Email settings fetched:', emailSettings?.length, 'settings');

        const emailHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>E-posta Doğrulama</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; }
                    .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                    .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
                    .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>⚖️ LegalAI</h1>
                    <h2>E-posta Doğrulama</h2>
                </div>
                <div class="content">
                    <p>Merhaba,</p>
                    <p>LegalAI hesabınızı oluşturduğunuz için teşekkürler. E-posta adresinizi doğrulamak için aşağıdaki butona tıklayın:</p>

                    <div style="text-align: center;">
                        <a href="${verificationUrl}" class="button">
                            ✅ E-posta Adresimi Doğrula
                        </a>
                    </div>

                    <p>Eğer buton çalışmıyorsa, aşağıdaki linki kopyalayıp tarayıcınıza yapıştırın:</p>
                    <p style="word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px;">
                        ${verificationUrl}
                    </p>

                    <p><strong>Önemli:</strong></p>
                    <ul>
                        <li>Bu link 24 saat geçerlidir</li>
                        <li>E-posta doğrulandıktan sonra hesabınız admin onayı bekleyecektir</li>
                        <li>Onay durumu e-posta ile bildirilecektir</li>
                    </ul>

                    <p>Eğer bu işlemi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.</p>
                </div>
                <div class="footer">
                    <p>LegalAI - Hukuki Belge Özetleme Sistemi</p>
                    <p>Bu e-posta otomatik olarak gönderilmiştir.</p>
                </div>
            </body>
            </html>
        `;

        const result = await sendEmail({
            to: email,
            subject: 'E-posta Adresinizi Doğrulayın - LegalAI',
            html: emailHtml,
            text: `LegalAI E-posta Doğrulama\n\nMerhaba!\n\nE-posta adresinizi doğrulamak için aşağıdaki linke tıklayın:\n${verificationUrl}\n\nBu link 24 saat geçerlidir.\n\nLegalAI Ekibi`
        }, emailSettings);

        console.log('Email send result:', result);
        return result;

    } catch (error) {
        console.error('Verification email send error:', error);
        return { success: false, error: error.message };
    }
}

// Verification token güncelle
async function updateVerificationToken(email, token, url) {
    try {
        // Auth user'ı bul ve metadata'sını güncelle
        const { data: users, error: listError } = await supabase.auth.admin.listUsers();
        
        if (users) {
            const user = users.users.find(u => u.email === email);
            if (user) {
                await supabase.auth.admin.updateUserById(user.id, {
                    user_metadata: {
                        ...user.user_metadata,
                        verification_token: token,
                        verification_url: url
                    }
                });
            }
        }
    } catch (error) {
        console.error('Update verification token error:', error);
    }
}

// Verification attempt log
async function logVerificationAttempt(userId, email, token, ipAddress, userAgent) {
    try {
        await supabase
            .from('email_verification_logs')
            .insert([{
                user_id: userId,
                email: email,
                verification_token: token,
                ip_address: ipAddress,
                user_agent: userAgent,
                status: 'sent'
            }]);
    } catch (error) {
        console.error('Log verification attempt error:', error);
    }
}

// Email gönderme (Supabase Auth kullanarak)
async function sendVerificationEmail(email, verificationUrl, token) {
    try {
        console.log('Sending verification email to:', email);
        console.log('Verification URL:', verificationUrl);

        // Önce kullanıcıyı bul
        const { data: users, error: listError } = await supabase.auth.admin.listUsers();
        let user = null;

        if (users && users.users) {
            user = users.users.find(u => u.email === email);
        }

        if (!user) {
            console.error('User not found for email verification');
            return false;
        }

        // Test sonuçlarına göre invite yöntemi çalışıyor, onu kullan
        console.log('Using invite method for email verification');

        // Kullanıcıyı sil ve yeniden invite et (daha temiz)
        try {
            await supabase.auth.admin.deleteUser(user.id);
            console.log('Existing user deleted for re-invitation');
        } catch (deleteError) {
            console.log('User deletion failed, continuing with invite:', deleteError.message);
        }

        // Invite link gönder
        const { data: inviteData, error: inviteError } = await supabase.auth.admin.inviteUserByEmail(email, {
            redirectTo: verificationUrl,
            data: {
                verification_token: token,
                verification_url: verificationUrl
            }
        });

        if (inviteError) {
            console.error('Invite user error:', inviteError);
            return false;
        }

        console.log('Invite email sent successfully:', inviteData);
        return true;

    } catch (error) {
        console.error('Send verification email error:', error);
        return false;
    }
}
