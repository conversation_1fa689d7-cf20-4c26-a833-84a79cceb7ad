<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limit Test - Hu<PERSON><PERSON>tle<PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            cursor: pointer;
            margin: 0.25rem;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 0.25rem;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Günlük Limit Sistemi Test Sayfası</h1>
        
        <div class="test-section">
            <h3>1. Kullanıcı Profili Test</h3>
            <button class="test-button" onclick="testUserProfile()">Profil Bilgilerini Al</button>
            <div id="profile-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>2. Günlük Limit Kontrolü Test</h3>
            <button class="test-button" onclick="testLimitCheck()">Limit Kontrolü Yap</button>
            <div id="limit-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>3. Sayaç Artırma Test</h3>
            <button class="test-button" onclick="testIncrementCount()">Sayacı Artır</button>
            <div id="increment-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>4. Token Bilgileri</h3>
            <button class="test-button" onclick="showTokenInfo()">Token Bilgilerini Göster</button>
            <div id="token-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>5. Veritabanı Bağlantı Test</h3>
            <button class="test-button" onclick="testDatabaseConnection()">Veritabanı Test</button>
            <div id="db-result" class="test-result"></div>
        </div>
    </div>

    <script>
        async function testUserProfile() {
            const resultDiv = document.getElementById('profile-result');
            const token = localStorage.getItem('userToken');
            
            if (!token) {
                resultDiv.textContent = 'HATA: Token bulunamadı. Lütfen giriş yapın.';
                resultDiv.className = 'test-result error';
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/user-profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\n\nResponse:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.ok ? 'test-result success' : 'test-result error';
            } catch (error) {
                resultDiv.textContent = `HATA: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        async function testLimitCheck() {
            const resultDiv = document.getElementById('limit-result');
            const token = localStorage.getItem('userToken');
            
            if (!token) {
                resultDiv.textContent = 'HATA: Token bulunamadı. Lütfen giriş yapın.';
                resultDiv.className = 'test-result error';
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/check-summary-limit', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\n\nResponse:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.ok ? 'test-result success' : 'test-result error';
            } catch (error) {
                resultDiv.textContent = `HATA: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        async function testIncrementCount() {
            const resultDiv = document.getElementById('increment-result');
            const token = localStorage.getItem('userToken');
            
            if (!token) {
                resultDiv.textContent = 'HATA: Token bulunamadı. Lütfen giriş yapın.';
                resultDiv.className = 'test-result error';
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/check-summary-limit', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\n\nResponse:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.ok ? 'test-result success' : 'test-result error';
            } catch (error) {
                resultDiv.textContent = `HATA: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        function showTokenInfo() {
            const resultDiv = document.getElementById('token-result');
            const token = localStorage.getItem('userToken');
            const userData = localStorage.getItem('userData');
            
            if (!token) {
                resultDiv.textContent = 'Token bulunamadı. Lütfen giriş yapın.';
                resultDiv.className = 'test-result error';
                return;
            }

            try {
                // JWT token'ı decode et
                const tokenParts = token.split('.');
                if (tokenParts.length === 3) {
                    let payload = tokenParts[1];
                    while (payload.length % 4) {
                        payload += '=';
                    }
                    const tokenData = JSON.parse(atob(payload));
                    
                    const info = {
                        tokenLength: token.length,
                        tokenPreview: token.substring(0, 50) + '...',
                        decodedToken: tokenData,
                        userData: userData ? JSON.parse(userData) : null
                    };
                    
                    resultDiv.textContent = JSON.stringify(info, null, 2);
                    resultDiv.className = 'test-result success';
                } else {
                    resultDiv.textContent = 'Geçersiz JWT token formatı';
                    resultDiv.className = 'test-result error';
                }
            } catch (error) {
                resultDiv.textContent = `Token decode hatası: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        async function testDatabaseConnection() {
            const resultDiv = document.getElementById('db-result');
            
            try {
                // Basit bir API çağrısı yaparak veritabanı bağlantısını test et
                const response = await fetch('/.netlify/functions/admin-stats', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                resultDiv.textContent = `Status: ${response.status}\n\nResponse:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.status === 401 ? 'test-result success' : 'test-result error';
                
                if (response.status === 401) {
                    resultDiv.textContent += '\n\nNOT: 401 hatası beklenen bir durumdur (admin token gerekli). Veritabanı bağlantısı çalışıyor.';
                }
            } catch (error) {
                resultDiv.textContent = `HATA: ${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
    </script>
</body>
</html>
