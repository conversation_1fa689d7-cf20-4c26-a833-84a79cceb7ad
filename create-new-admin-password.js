// Yeni Admin Şifresi Oluştur
const bcrypt = require('bcryptjs');

async function createNewPasswordHash(newPassword) {
    try {
        console.log('🔐 Yeni şifre hash\'i oluşturuluyor...\n');
        
        const saltRounds = 10;
        const hash = await bcrypt.hash(newPassword, saltRounds);
        
        console.log('✅ Hash başarıyla oluşturuldu!');
        console.log('Yeni şifre:', newPassword);
        console.log('Hash:', hash);
        console.log('\n📋 Supabase SQL Update komutu:');
        console.log('='.repeat(80));
        console.log(`UPDATE "public"."admins" 
SET "password_hash" = '${hash}', 
    "updated_at" = NOW() 
WHERE "email" = '<EMAIL>';`);
        console.log('='.repeat(80));
        
        // Test et
        console.log('\n🧪 Hash testi yapılıyor...');
        const testResult = await bcrypt.compare(newPassword, hash);
        console.log('Test sonucu:', testResult ? '✅ Başarılı' : '❌ Başarısız');
        
        return hash;
    } catch (error) {
        console.error('❌ Hash oluşturma hatası:', error);
        return null;
    }
}

// Yeni şifre: 8454854
createNewPasswordHash('8454854').catch(console.error);
