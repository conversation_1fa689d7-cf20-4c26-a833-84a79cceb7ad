// Admin Token Doğrulama Function'ı
const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// Supabase client (Service Key ile)
const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

// Basit token doğrulama fonksiyonu
async function verifyAdminToken(token) {
    try {
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error('JWT_SECRET not configured');
        }

        // JWT token'ı doğrula
        const decoded = jwt.verify(token, jwtSecret);

        // Admin kullanıcısını kontrol et
        const { data: admin, error: adminError } = await supabase
            .from('admins')
            .select('*')
            .eq('id', decoded.adminId)
            .eq('is_active', true)
            .single();

        if (adminError || !admin) {
            throw new Error('Admin not found or inactive');
        }

        return {
            valid: true,
            admin: {
                id: admin.id,
                email: admin.email,
                fullName: admin.full_name,
                role: admin.role
            }
        };

    } catch (error) {
        return {
            valid: false,
            error: error.message
        };
    }
}

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const verification = await verifyAdminToken(token);

        if (!verification.valid) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ 
                    error: 'Geçersiz veya süresi dolmuş token.',
                    details: verification.error
                })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                valid: true,
                admin: verification.admin
            })
        };

    } catch (error) {
        console.error('Token verification error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Token doğrulama hatası.' })
        };
    }
};

// Export verification function for use in other functions
exports.verifyAdminToken = verifyAdminToken;
