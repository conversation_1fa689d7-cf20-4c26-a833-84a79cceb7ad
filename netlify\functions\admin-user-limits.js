const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Admin token doğrulama (basitleştirilmiş)
async function verifyAdminToken(token) {
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // adminId varsa admin kabul et (admin-users.js'deki gibi)
        if (decoded.adminId) {
            return decoded;
        }

        return null;
    } catch (e) {
        return null;
    }
}

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow GET and PUT requests
    if (!['GET', 'PUT'].includes(event.httpMethod)) {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Admin token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Admin authorization required' })
            };
        }

        const token = authHeader.substring(7);
        const adminAuth = await verifyAdminToken(token);

        if (!adminAuth) {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({ error: 'Admin access denied' })
            };
        }

        if (event.httpMethod === 'GET') {
            // Kullanıcıların limit bilgilerini getir (tüm statuslar dahil)
            const { data: users, error } = await supabase
                .from('users')
                .select('id, first_name, last_name, email, phone, profession, daily_summary_limit, daily_summary_count, last_summary_date, status, created_at, last_login, email_verified_at')
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Users fetch error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcı bilgileri getirilemedi.' })
                };
            }

            // Günlük istatistikleri de al
            const { data: stats, error: statsError } = await supabase
                .from('daily_summary_stats')
                .select('*')
                .single();

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    users: users || [],
                    stats: stats || {}
                })
            };
        }

        if (event.httpMethod === 'PUT') {
            // Kullanıcının limit değerini güncelle
            const body = JSON.parse(event.body || '{}');
            const { userId, newLimit } = body;

            if (!userId || newLimit === undefined || newLimit === null) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcı ID ve yeni limit değeri gerekli.' })
                };
            }

            // Limit değeri kontrolü
            if (newLimit < 0 || newLimit > 100) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Limit değeri 0-100 arasında olmalıdır.' })
                };
            }

            // Basit UPDATE ile limit güncelle
            const { error } = await supabase
                .from('users')
                .update({ daily_summary_limit: parseInt(newLimit) })
                .eq('id', userId);

            if (error) {
                console.error('Limit update error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Limit güncellenemedi.' })
                };
            }

            // Admin log ekle
            try {
                const clientIP = event.headers['x-forwarded-for'] || event.headers['x-real-ip'] || 'unknown';
                const userAgent = event.headers['user-agent'] || 'unknown';

                await supabase.from('admin_logs').insert([{
                    admin_username: adminAuth.email,
                    admin_email: adminAuth.email,
                    action: 'update_user_limit',
                    target_id: userId,
                    target_type: 'user',
                    details: { 
                        newLimit: newLimit,
                        ip: clientIP 
                    },
                    ip_address: clientIP,
                    user_agent: userAgent
                }]);
            } catch (logError) {
                console.error('Log insert error:', logError);
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Kullanıcı limiti başarıyla güncellendi.'
                })
            };
        }

    } catch (error) {
        console.error('Admin user limits error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
};
