# Environment Variables Örnek Dosyası
# Bu dosya sadece örnek amaçlıdır. Gerçek değerleri environment variables olarak ayarlayın.

# 🔑 API Keys (Failover sistemi için 20 taneye kadar)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_API_KEY2=your_second_gemini_api_key_here
GEMINI_API_KEY3=your_third_gemini_api_key_here
GEMINI_API_KEY4=your_fourth_gemini_api_key_here
# ... GEMINI_API_KEY20'ye kadar ekleyebilirsiniz

# 🗄️ Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# 🔐 JWT Secret (32 karakter minimum - generate-jwt-secret.js ile oluşturun)
JWT_SECRET=K8mN9pQ2rS5tU7vW0xY3zA6bC9dE2fG5

# 🌐 Site URL (Email verification için)
SITE_URL=https://hukukibelgeozetleme.netlify.app

# 📧 Email Service (Brevo - tüm e-posta ayarları Supabase'de)
# E-posta ayarları artık Supabase system_settings tablosunda saklanıyor
# Admin panelinden yapılandırılabilir

# 📱 Telegram Bot Configuration (Üyelik bildirimleri için)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# 🔗 Webhook Secret (32 karakter minimum - webhook güvenliği için)
WEBHOOK_SECRET=X9mP2qR5sT8uV1wY4zA7bC0dF3gH6jK9

# 🔗 Kalıcı Webhook Key (32 karakter minimum - kalıcı webhook için)
PERMANENT_WEBHOOK_KEY=PermanentWebhook2024SecureKey789

# 👤 Admin Security Settings
ADMIN_SESSION_DURATION=86400000
ADMIN_MAX_LOGIN_ATTEMPTS=5
ADMIN_LOCKOUT_DURATION=1800000

# 🚦 Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_ATTEMPTS=10

# 📝 Açıklamalar:
# ADMIN_SESSION_DURATION: Admin session süresi (24 saat = 86400000 ms)
# ADMIN_MAX_LOGIN_ATTEMPTS: Maksimum başarısız giriş denemesi (5)
# ADMIN_LOCKOUT_DURATION: Hesap kilit süresi (30 dakika = 1800000 ms)
# RATE_LIMIT_WINDOW: Rate limit penceresi (15 dakika = 900000 ms)
# RATE_LIMIT_MAX_ATTEMPTS: Rate limit max deneme (10)

# 🚨 Güvenlik Uyarıları:
# 1. Bu dosyayı asla git'e commit etmeyin
# 2. Production'da güçlü ve benzersiz değerler kullanın
# 3. JWT_SECRET'i düzenli olarak değiştirin
# 4. Netlify Dashboard > Environment Variables'dan import edin
