const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');
const { apiKeyManager } = require('./api-key-manager');
const { getCurrentGeminiModel } = require('./api-key-manager');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing environment variables:', {
        SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
        SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY
    });
}

const supabase = createClient(supabaseUrl, supabaseKey);

// API key'leri kontrol et ve listele
function getAvailableApiKeys() {
    const apiKeys = [];
    
    for (let i = 1; i <= 20; i++) {
        const keyName = i === 1 ? 'GEMINI_API_KEY' : `GEMINI_API_KEY${i}`;
        const keyValue = process.env[keyName];
        
        if (keyValue) {
            apiKeys.push({
                name: keyName,
                preview: keyValue.substring(0, 8) + '...' + keyValue.substring(keyValue.length - 4),
                exists: true,
                isActive: false // Bu bilgi ayrı bir fonksiyondan gelecek
            });
        } else {
            apiKeys.push({
                name: keyName,
                preview: 'Tanımlanmamış',
                exists: false,
                isActive: false
            });
        }
    }
    
    return apiKeys;
}

// Aktif API key'i belirle
async function getCurrentActiveApiKey() {
    try {
        // Önce ApiKeyManager'dan gerçek durumu al
        const keyStatus = apiKeyManager.getKeyStatus();

        // Manuel seçim varsa onu kullan
        if (keyStatus.manuallySelectedKey) {
            return keyStatus.manuallySelectedKey;
        }

        // Yoksa mevcut aktif key'i kullan
        if (keyStatus.currentKeyName && keyStatus.currentKeyName !== 'None') {
            return keyStatus.currentKeyName;
        }

        // Son başarılı kullanılan API key'i bul (fallback)
        const { data: lastSuccessLog } = await supabase
            .from('gemini_api_logs')
            .select('api_key_name')
            .eq('status', 'success')
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

        if (lastSuccessLog) {
            return lastSuccessLog.api_key_name;
        }
    } catch (error) {
        console.log('Error getting active API key:', error);
    }

    // Varsayılan olarak GEMINI_API_KEY
    return 'GEMINI_API_KEY';
}

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Yetkilendirme gerekli' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token doğrulama
        let adminData;
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                adminId: decoded.adminId,
                email: decoded.email,
                role: decoded.role
            };
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token' })
            };
        }

        if (event.httpMethod === 'GET') {
            // API key'leri ve logları getir
            const apiKeys = getAvailableApiKeys();
            const currentActiveKey = await getCurrentActiveApiKey();

            // Aktif key'i işaretle
            apiKeys.forEach(key => {
                key.isActive = key.name === currentActiveKey;
            });

            // Son API loglarını getir
            const { data: apiLogs, error: logsError } = await supabase
                .from('gemini_api_logs')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(50);

            if (logsError) {
                console.error('API logs fetch error:', logsError);
            }

            // API key istatistikleri
            const { data: keyStats, error: statsError } = await supabase
                .from('gemini_api_logs')
                .select('api_key_name, status')
                .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Son 24 saat

            const stats = {};
            if (keyStats) {
                keyStats.forEach(log => {
                    if (!stats[log.api_key_name]) {
                        stats[log.api_key_name] = { success: 0, error: 0, total: 0 };
                    }
                    stats[log.api_key_name][log.status]++;
                    stats[log.api_key_name].total++;
                });
            }

            // Aktif model bilgisini al
            const modelInfo = await getCurrentGeminiModel();

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    apiKeys: apiKeys,
                    currentActiveKey: currentActiveKey,
                    apiLogs: apiLogs || [],
                    keyStats: stats,
                    modelInfo: {
                        name: modelInfo.model,
                        displayName: modelInfo.displayName,
                        description: modelInfo.description
                    }
                })
            };
        }

        if (event.httpMethod === 'POST') {
            const requestBody = JSON.parse(event.body);
            const { action, apiKeyName } = requestBody;

            if (action === 'test_key') {
                // API key test et
                const keyValue = process.env[apiKeyName];
                
                if (!keyValue) {
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'API key bulunamadı' })
                    };
                }

                // Test isteği gönder (basit bir test)
                try {
                    const testResult = await testGeminiApiKey(keyValue, apiKeyName);
                    
                    return {
                        statusCode: 200,
                        headers,
                        body: JSON.stringify({
                            success: true,
                            message: 'API key test edildi',
                            result: testResult
                        })
                    };
                } catch (error) {
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'API key test başarısız',
                            details: error.message
                        })
                    };
                }
            }

            if (action === 'set_active') {
                console.log(`🔧 Admin setting API key as active: ${apiKeyName}`);

                // API key'in var olup olmadığını kontrol et
                const keyValue = process.env[apiKeyName];
                console.log(`🔧 Environment variable exists: ${!!keyValue}`);

                if (!keyValue) {
                    console.log(`❌ API key ${apiKeyName} not found in environment variables`);
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'API key bulunamadı' })
                    };
                }

                // ApiKeyManager'da manuel seçimi ayarla
                console.log(`🔧 Calling apiKeyManager.setManualApiKey(${apiKeyName})`);
                const success = await apiKeyManager.setManualApiKey(apiKeyName);
                console.log(`🔧 setManualApiKey result: ${success}`);

                if (success) {
                    // Log olarak kaydet
                    await logApiKeyEvent(apiKeyName, 'manual_activation', 200, 'Admin tarafından aktif olarak ayarlandı', adminData);

                    return {
                        statusCode: 200,
                        headers,
                        body: JSON.stringify({
                            success: true,
                            message: `${apiKeyName} aktif olarak ayarlandı`
                        })
                    };
                } else {
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'API key ayarlanamadı' })
                    };
                }
            }

            if (action === 'clear_manual_selection') {
                // Manuel seçimi temizle
                await apiKeyManager.clearManualSelection();

                // Log olarak kaydet
                await logApiKeyEvent('SYSTEM', 'manual_selection_cleared', 200, 'Admin tarafından manuel seçim temizlendi', adminData);

                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify({
                        success: true,
                        message: 'Manuel API key seçimi temizlendi, otomatik failover aktif'
                    })
                };
            }
        }

        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };

    } catch (error) {
        console.error('API management error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası',
                details: error.message
            })
        };
    }
};

// API key test fonksiyonu
async function testGeminiApiKey(apiKey, keyName) {
    const startTime = Date.now();

    try {
        // Aktif modeli al
        const modelInfo = await getCurrentGeminiModel();

        // Basit bir test isteği
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelInfo.model}:generateContent?key=` + apiKey, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: "Test mesajı"
                    }]
                }],
                generationConfig: {
                    temperature: 0.2,
                    topK: 20,
                    topP: 0.8,
                    maxOutputTokens: 100,
                },
                safetySettings: [
                    {
                        category: "HARM_CATEGORY_HARASSMENT",
                        threshold: "BLOCK_NONE",
                    },
                    {
                        category: "HARM_CATEGORY_HATE_SPEECH",
                        threshold: "BLOCK_NONE",
                    },
                    {
                        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        threshold: "BLOCK_NONE",
                    },
                    {
                        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                        threshold: "BLOCK_NONE",
                    }
                ]
            })
        });

        const responseTime = Date.now() - startTime;
        const result = await response.json();

        if (response.ok) {
            await logApiKeyEvent(keyName, 'test_request', response.status, 'Test başarılı', null, responseTime);
            return { status: 'success', responseTime, message: 'API key çalışıyor' };
        } else {
            await logApiKeyEvent(keyName, 'test_request', response.status, result.error?.message || 'Test başarısız', null, responseTime);
            throw new Error(result.error?.message || 'API key test başarısız');
        }
    } catch (error) {
        const responseTime = Date.now() - startTime;
        await logApiKeyEvent(keyName, 'test_request', 0, error.message, null, responseTime);
        throw error;
    }
}

// API key event log fonksiyonu
async function logApiKeyEvent(apiKeyName, requestType, status, message, adminData = null, responseTime = null) {
    try {
        const keyValue = process.env[apiKeyName];
        const keyPreview = keyValue ? keyValue.substring(0, 8) + '...' + keyValue.substring(keyValue.length - 4) : 'Tanımlanmamış';
        
        await supabase.from('gemini_api_logs').insert([{
            api_key_name: apiKeyName,
            api_key_preview: keyPreview,
            status: status >= 200 && status < 300 ? 'success' : 'error',
            request_type: requestType,
            response_status: status,
            error_message: message,
            response_time_ms: responseTime,
            usage_info: adminData ? { triggered_by: adminData.email } : null
        }]);
    } catch (error) {
        console.error('API log error:', error);
    }
}
