// Global değişkenler
let currentFile = null;
let isProcessing = false;

// UTF-8 string düzeltme fonksiyonu
function fixUTF8String(str) {
    if (!str) return str;

    try {
        // Eğer string zaten doğru UTF-8 ise, olduğu gibi döndür
        if (str === decodeURIComponent(encodeURIComponent(str))) {
            return str;
        }

        // UTF-8 düzeltme dene
        return decodeURIComponent(escape(str));
    } catch (e) {
        // Hata durumunda orijinal string'i döndür
        console.warn('UTF-8 fix failed for string:', str, e);
        return str;
    }
}

// Karakter sınırları
const MAX_CHARACTERS = 25000;
const CHARACTER_WARNING_THRESHOLD = 20000;

// DOM elementleri - Lazy loading ile
let tabButtons, tabContents, pdfUploadArea, pdfFileInput, pdfFileDetails, pdfFileName, pdfFileSize, pdfRemoveFile, pdfSubmitBtn, textInput, charCount, textSubmitBtn, resultSection, resultContent, copyResultBtn, errorMessage, errorText, successMessage, successText;

function initializeDOMElements() {
    console.log('Initializing DOM elements...');

    tabButtons = document.querySelectorAll('.tab-btn');
    tabContents = document.querySelectorAll('.tab-content');
    pdfUploadArea = document.getElementById('pdf-upload-area');
    pdfFileInput = document.getElementById('pdf-file-input');
    pdfFileDetails = document.getElementById('pdf-file-details');
    pdfFileName = document.getElementById('pdf-file-name');
    pdfFileSize = document.getElementById('pdf-file-size');
    pdfRemoveFile = document.getElementById('pdf-remove-file');
    pdfSubmitBtn = document.getElementById('pdf-submit-btn');
    textInput = document.getElementById('text-input');
    charCount = document.getElementById('char-count');
    textSubmitBtn = document.getElementById('text-submit-btn');
    resultSection = document.getElementById('result-section');
    resultContent = document.getElementById('result-content');
    copyResultBtn = document.getElementById('copy-result-btn');
    errorMessage = document.getElementById('error-message');
    errorText = document.getElementById('error-text');
    successMessage = document.getElementById('success-message');
    successText = document.getElementById('success-text');

    // Debug bilgisi
    console.log('DOM Elements Status:', {
        tabButtons: tabButtons.length,
        tabContents: tabContents.length,
        pdfUploadArea: !!pdfUploadArea,
        pdfFileInput: !!pdfFileInput,
        pdfSubmitBtn: !!pdfSubmitBtn,
        textInput: !!textInput,
        textSubmitBtn: !!textSubmitBtn
    });

    // Eksik elementleri logla
    const missingElements = [];
    if (tabButtons.length === 0) missingElements.push('tabButtons');
    if (!pdfUploadArea) missingElements.push('pdfUploadArea');
    if (!pdfFileInput) missingElements.push('pdfFileInput');
    if (!pdfSubmitBtn) missingElements.push('pdfSubmitBtn');
    if (!textInput) missingElements.push('textInput');
    if (!textSubmitBtn) missingElements.push('textSubmitBtn');

    if (missingElements.length > 0) {
        console.error('Missing DOM elements:', missingElements);
    } else {
        console.log('✅ All required DOM elements found');
    }

    return missingElements.length === 0;
}

// Event Listeners
// İlk DOMContentLoaded event listener kaldırıldı - aşağıdaki daha kapsamlı olan kullanılıyor

// FontAwesome fallback sistemi kaldırıldı - Modern CSS ile iconlar çalışıyor

// FontAwesome fallback fonksiyonları kaldırıldı - Modern CSS ile iconlar çalışıyor

// Kullanıcı kimlik doğrulama kontrolü
function checkUserAuth() {
    const token = localStorage.getItem('userToken');
    const trialLimit = document.getElementById('trial-limit');
    const mainContent = document.getElementById('main-content');
    const userMenuBtn = document.getElementById('user-menu-btn');
    const loginLink = document.getElementById('login-link');
    const registerLink = document.getElementById('register-link');
    const userName = document.getElementById('user-name');

    if (!token) {
        // Kullanıcı giriş yapmamış - deneme modunda
        trialLimit.style.display = 'none';
        mainContent.style.display = 'block';
        userMenuBtn.style.display = 'none';
        loginLink.style.display = 'block';
        registerLink.style.display = 'block';
        return;
    }

    try {
        // JWT token'ı decode et (payload kısmı)
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3) {
            throw new Error('Invalid JWT format');
        }

        let payload = tokenParts[1];
        // Base64 padding ekle
        while (payload.length % 4) {
            payload += '=';
        }

        // Base64 URL decode
        const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
        const userData = JSON.parse(atob(base64));

        // Token süresi kontrol et (JWT exp saniye cinsinden) - 5 dakika tolerans
        if (userData.exp && userData.exp < (Math.floor(Date.now() / 1000) - 300)) {
            console.log('Token expired, clearing auth data');
            localStorage.removeItem('userToken');
            localStorage.removeItem('userData');
            trialLimit.style.display = 'none';
            mainContent.style.display = 'block';
            userMenuBtn.style.display = 'none';
            loginLink.style.display = 'block';
            registerLink.style.display = 'block';
            return;
        }

        // Kullanıcı giriş yapmış
        // JWT'den user data'yı al
        const user = {
            firstName: userData.firstName || 'Kullanıcı',
            lastName: userData.lastName || '',
            first_name: userData.firstName || 'Kullanıcı', // Mobile menu compatibility
            last_name: userData.lastName || '', // Mobile menu compatibility
            email: userData.email
        };

        // localStorage'a da kaydet (eski uyumluluk için)
        localStorage.setItem('userData', JSON.stringify(user));

        trialLimit.style.display = 'none';
        mainContent.style.display = 'block';
        userMenuBtn.style.display = 'flex';
        loginLink.style.display = 'none';
        registerLink.style.display = 'none';
        const firstName = fixUTF8String(user.firstName);
        const lastName = fixUTF8String(user.lastName);
        const fullName = `${firstName} ${lastName}`.trim();
        console.log('Setting user name in script.js:', {
            originalFirstName: user.firstName,
            originalLastName: user.lastName,
            fixedFirstName: firstName,
            fixedLastName: lastName,
            fullName: fullName,
            charCodes: fullName.split('').map(c => c.charCodeAt(0))
        });
        userName.textContent = fullName;

        // Kullanıcı menü olayları
        setupUserMenu();

        // Mobil menü durumunu güncelle
        if (window.mobileMenuInstance) {
            window.mobileMenuInstance.updateAuthButtons();
        }

    } catch (error) {
        console.error('Token decode error:', error);
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        trialLimit.style.display = 'none';
        mainContent.style.display = 'block';
        userMenuBtn.style.display = 'none';
        loginLink.style.display = 'block';
        registerLink.style.display = 'block';

        // Mobil menü durumunu güncelle
        updateMobileAuthStatus();
    }
}

function setupUserMenu() {
    const userMenuBtn = document.getElementById('user-menu-btn');

    // User menu dropdown HTML'ini sayfaya ekle
    if (!document.getElementById('user-menu-dropdown')) {
        const menuHTML = `
            <div class="user-menu-dropdown" id="user-menu-dropdown" style="display: none;">
                <div class="user-menu-item" onclick="goToProfile()">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </div>
                <div class="user-menu-divider"></div>
                <div class="user-menu-item" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Çıkış Yap</span>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', menuHTML);
    }

    const userMenuDropdown = document.getElementById('user-menu-dropdown');

    if (userMenuBtn && userMenuDropdown) {
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleUserMenu();
        });

        // Dışarı tıklandığında menüyü kapat
        document.addEventListener('click', function(e) {
            if (!userMenuBtn.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                userMenuDropdown.style.display = 'none';
            }
        });
    }
}

function toggleUserMenu() {
    const userMenuDropdown = document.getElementById('user-menu-dropdown');
    const userMenuBtn = document.getElementById('user-menu-btn');

    if (userMenuDropdown.style.display === 'none' || !userMenuDropdown.style.display) {
        // Menüyü göster
        const rect = userMenuBtn.getBoundingClientRect();
        userMenuDropdown.style.display = 'block';
        userMenuDropdown.style.top = (rect.bottom + 5) + 'px';
        userMenuDropdown.style.right = (window.innerWidth - rect.right) + 'px';
    } else {
        // Menüyü gizle
        userMenuDropdown.style.display = 'none';
    }
}

function goToProfile() {
    window.location.href = '/profile';
}

function logout() {
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    window.location.reload();
}

// Deneme limiti kontrolü (Supabase ile)
async function checkTrialLimit() {
    const token = localStorage.getItem('userToken');

    // Kullanıcı giriş yapmışsa limit yok
    if (token) {
        try {
            // JWT token decode
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
                let payload = tokenParts[1];
                while (payload.length % 4) {
                    payload += '=';
                }
                const userData = JSON.parse(atob(payload));

                // Token süresi kontrol et (saniye cinsinden)
                if (userData.exp && userData.exp > Math.floor(Date.now() / 1000)) {
                    return { allowed: true, reason: 'authenticated' };
                }
            }
        } catch (e) {
            console.warn('Token decode error in trial check:', e.message);
        }
    }

    try {
        // Supabase ile IP tabanlı kontrol
        const response = await fetch('/.netlify/functions/check-trial', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                userAgent: navigator.userAgent,
                fingerprint: generateFingerprint()
            })
        });

        const data = await response.json();

        if (data.success && data.trial) {
            return {
                allowed: data.trial.allowed,
                reason: data.trial.message,
                usageCount: data.trial.usage_count
            };
        }

        // API hatası durumunda localStorage fallback
        return checkLocalTrialLimit();

    } catch (error) {
        console.error('Trial check error:', error);
        // Network hatası durumunda localStorage fallback
        return checkLocalTrialLimit();
    }
}

// Fallback: LocalStorage tabanlı kontrol
function checkLocalTrialLimit() {
    const trialData = getTrialData();

    if (trialData.used) {
        return { allowed: false, reason: 'local_limit_exceeded' };
    }

    return { allowed: true, reason: 'local_first_use' };
}

function getTrialData() {
    const stored = localStorage.getItem('trialData');
    if (stored) {
        try {
            return JSON.parse(stored);
        } catch (e) {
            // Geçersiz data
        }
    }

    return {
        used: false,
        timestamp: null,
        fingerprint: generateFingerprint()
    };
}

function generateFingerprint() {
    // Basit browser fingerprint oluştur
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Browser fingerprint', 2, 2);

    const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL()
    ].join('|');

    // Basit hash oluştur
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 32bit integer'a çevir
    }

    return Math.abs(hash).toString(36);
}

async function recordTrialUsage() {
    const token = localStorage.getItem('userToken');

    // Kullanıcı giriş yapmışsa kayıt gereksiz
    if (token) {
        try {
            // JWT token decode
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
                let payload = tokenParts[1];
                while (payload.length % 4) {
                    payload += '=';
                }
                const userData = JSON.parse(atob(payload));

                // Token süresi kontrol et (saniye cinsinden)
                if (userData.exp && userData.exp > Math.floor(Date.now() / 1000)) {
                    return; // Giriş yapmış kullanıcı
                }
            }
        } catch (e) {
            console.warn('Token decode error in trial record:', e.message);
        }
    }

    try {
        // Supabase ile kaydet
        const response = await fetch('/.netlify/functions/record-trial', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                userAgent: navigator.userAgent,
                fingerprint: generateFingerprint(),
                documentType: 'summary'
            })
        });

        const data = await response.json();

        if (data.success) {
            console.log('Trial usage recorded:', data.result);
        }

    } catch (error) {
        console.error('Trial record error:', error);
    }

    // LocalStorage'a da kaydet (fallback)
    const trialData = {
        used: true,
        timestamp: new Date().toISOString(),
        fingerprint: generateFingerprint()
    };

    localStorage.setItem('trialData', JSON.stringify(trialData));
}

// Otomatik günlük sıfırlama kontrolü - DEVRE DIŞI BIRAKILDI
async function checkAndResetDailyQuota() {
    // Otomatik sıfırlama devre dışı bırakıldı
    // Sadece webhook ile manuel sıfırlama kullanılacak
    console.log('Otomatik quota sıfırlama devre dışı');
    return;
}

// Günlük limit kontrolü (kayıtlı kullanıcılar için)
async function checkDailyLimit() {
    // Otomatik sıfırlama kaldırıldı - sadece webhook ile sıfırlama

    // Token'ı yenile (güncel status için)
    await refreshTokenIfNeeded();

    const token = localStorage.getItem('userToken');

    if (!token) {
        return { allowed: false, reason: 'not_authenticated' };
    }

    try {
        const response = await fetch('/.netlify/functions/check-summary-limit', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success && data.limit) {
            return {
                allowed: data.limit.allowed,
                remaining: data.limit.remaining,
                limit: data.limit.limit_value,
                used: data.limit.used_today,
                reason: data.limit.allowed ? 'allowed' : 'limit_exceeded'
            };
        }

        // Admin onayı kontrolü
        if (!data.success && data.status && data.status !== 'approved') {
            console.log('Admin approval required - Current status:', data.status);
            return {
                allowed: false,
                reason: 'admin_approval_required',
                status: data.status,
                emailVerified: data.emailVerified,
                error: data.error
            };
        }

        // API hatası durumunda fallback - basit kontrol
        console.warn('Daily limit check failed, using fallback');
        return await checkDailyLimitFallback();

    } catch (error) {
        console.error('Daily limit check error:', error);
        // Hata durumunda fallback kontrol
        return await checkDailyLimitFallback();
    }
}

// Fallback günlük limit kontrolü (API çalışmadığında)
async function checkDailyLimitFallback() {
    const token = localStorage.getItem('userToken');

    if (!token) {
        return { allowed: false, reason: 'not_authenticated' };
    }

    try {
        // Kullanıcı profilinden limit bilgilerini al
        const response = await fetch('/.netlify/functions/user-profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const profileData = await response.json();

            if (profileData.success && profileData.user) {
                const user = profileData.user;
                const limit = user.daily_summary_limit || 5;
                const used = user.daily_summary_count || 0;
                const remaining = Math.max(0, limit - used);

                return {
                    allowed: used < limit,
                    remaining: remaining,
                    limit: limit,
                    used: used,
                    reason: used < limit ? 'allowed' : 'limit_exceeded'
                };
            }
        }

        // Profil alınamazsa varsayılan olarak izin ver
        console.warn('Could not get user profile for limit check, allowing usage');
        return { allowed: true, reason: 'profile_unavailable' };

    } catch (error) {
        console.error('Fallback limit check error:', error);
        // Son çare olarak izin ver
        return { allowed: true, reason: 'fallback_error' };
    }
}

// Günlük kullanım kaydı (kayıtlı kullanıcılar için)
async function recordDailyUsage() {
    const token = localStorage.getItem('userToken');

    if (!token) {
        return;
    }

    try {
        const response = await fetch('/.netlify/functions/check-summary-limit', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            console.log('Daily usage recorded:', data.limit);
            // Profil sayfası açıksa limit bilgilerini güncelle
            updateLimitDisplay(data.limit);
        } else if (data.limit_exceeded) {
            console.warn('Daily limit exceeded after usage');
        }

    } catch (error) {
        console.error('Daily usage record error:', error);
        // Hata durumunda basit kayıt yap
        await recordDailyUsageFallback();
    }
}

// Fallback günlük kullanım kaydı
async function recordDailyUsageFallback() {
    console.log('Using fallback daily usage recording');
    // Bu durumda sadece log tutuyoruz, gerçek sayaç güncellemesi yapamıyoruz
    // Veritabanı düzeltildikten sonra bu fonksiyon kaldırılabilir
}

// Limit bilgilerini güncelle (profil sayfasında)
function updateLimitDisplay(limitInfo) {
    // Sadece profil sayfasındaysak güncelle
    if (window.location.pathname.includes('profile')) {
        const dailyLimit = document.getElementById('daily-limit');
        const usedToday = document.getElementById('used-today');
        const remainingToday = document.getElementById('remaining-today');
        const usagePercentage = document.getElementById('usage-percentage');
        const progressFill = document.getElementById('usage-progress');

        if (dailyLimit && limitInfo) {
            dailyLimit.textContent = limitInfo.limit_value || 5;
            usedToday.textContent = limitInfo.used_today || 0;
            remainingToday.textContent = limitInfo.remaining || 0;

            const percentage = limitInfo.limit_value > 0 ?
                Math.round((limitInfo.used_today / limitInfo.limit_value) * 100) : 0;

            usagePercentage.textContent = percentage;
            progressFill.style.width = `${percentage}%`;

            // Renk ayarla
            if (percentage >= 100) {
                progressFill.style.backgroundColor = 'var(--danger)';
            } else if (percentage >= 80) {
                progressFill.style.backgroundColor = 'var(--warning)';
            } else {
                progressFill.style.backgroundColor = 'var(--success)';
            }
        }
    }
}

// Günlük limit aşıldığında gösterilecek güzel modal
function showDailyLimitExceeded(limitInfo) {
    let modalHTML = '';

    // Admin onayı gerekli durumu
    if (limitInfo.reason === 'admin_approval_required') {
        modalHTML = `
            <div id="limitModal" class="limit-modal-overlay">
                <div class="limit-modal">
                    <div class="limit-modal-header">
                        <i class="fas fa-user-check limit-modal-icon"></i>
                        <h2>Admin Onayı Gerekli</h2>
                    </div>

                    <div class="limit-modal-content">
                        <p class="limit-modal-text">
                            Özet yapabilmek için hesabınızın admin tarafından onaylanması gerekmektedir.
                        </p>

                        <div class="status-info-grid">
                            <div class="status-info-card">
                                <div class="status-label">Hesap Durumu</div>
                                <div class="status-value ${limitInfo.status}">${getStatusText(limitInfo.status)}</div>
                            </div>
                            <div class="status-info-card">
                                <div class="status-label">E-posta Durumu</div>
                                <div class="status-value ${limitInfo.emailVerified ? 'verified' : 'not-verified'}">
                                    ${limitInfo.emailVerified ? '✅ Doğrulandı' : '❌ Doğrulanmamış'}
                                </div>
                            </div>
                        </div>

                        <p class="limit-modal-footer-text">
                            <i class="fas fa-info-circle"></i>
                            Profilinizi kontrol ederek hesap durumunuzu görebilirsiniz.
                        </p>
                    </div>

                    <div class="limit-modal-actions">
                        <button class="btn btn-primary" onclick="window.location.href='/profile'">
                            <i class="fas fa-user"></i> Profile Git
                        </button>
                        <button class="btn btn-secondary" onclick="closeLimitModal()">
                            <i class="fas fa-times"></i> Kapat
                        </button>
                    </div>
                </div>
            </div>
        `;
    } else {
        // Normal limit aşıldı durumu
        modalHTML = `
        <div id="limitModal" class="limit-modal-overlay">
            <div class="limit-modal">
                <div class="limit-modal-header">
                    <i class="fas fa-exclamation-triangle limit-modal-icon"></i>
                    <h2>Günlük Özet Limitiniz Doldu</h2>
                </div>

                <div class="limit-modal-content">
                    <p class="limit-modal-text">
                        Bugün <strong class="text-danger">${limitInfo.used || 0}</strong> adet özet yaptınız ve
                        günlük <strong class="text-success">${limitInfo.limit || 5}</strong> özet limitinize ulaştınız.
                    </p>

                    <div class="limit-stats-grid">
                        <div class="limit-stat-card">
                            <div class="stat-number text-success">${limitInfo.limit || 5}</div>
                            <div class="stat-label">Günlük Limit</div>
                        </div>
                        <div class="limit-stat-card">
                            <div class="stat-number text-danger">${limitInfo.used || 0}</div>
                            <div class="stat-label">Kullanılan</div>
                        </div>
                        <div class="limit-stat-card">
                            <div class="stat-number text-muted">${limitInfo.remaining || 0}</div>
                            <div class="stat-label">Kalan</div>
                        </div>
                    </div>

                    <p class="limit-modal-footer-text">
                        Yarın tekrar özet yapabilirsiniz.
                    </p>
                </div>

                <div class="limit-modal-actions">
                    <button class="btn btn-secondary" onclick="closeLimitModal()">
                        <i class="fas fa-check"></i> Tamam
                    </button>
                    <button class="btn btn-primary" onclick="goToSupport()">
                        <i class="fas fa-headset"></i> Daha Fazla Özet Hakkı İçin Destek Talebi Oluştur
                    </button>
                </div>
            </div>
        </div>
        `;
    }

    // Modal'ı sayfaya ekle
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Modal'ı göster
    setTimeout(() => {
        document.getElementById('limitModal').classList.add('show');
    }, 10);
}

// Status text helper
function getStatusText(status) {
    const statusTexts = {
        'pending': '⏳ Admin Onayı Bekliyor',
        'approved': '✅ Aktif',
        'suspended': '🚫 Askıda'
    };
    return statusTexts[status] || status;
}

// Token'ı gerekirse yenile
async function refreshTokenIfNeeded() {
    const token = localStorage.getItem('userToken');
    if (!token) return;

    try {
        // Token'ın yaşını kontrol et (1 saatten eski ise yenile)
        const payload = JSON.parse(atob(token.split('.')[1]));
        const tokenAge = Date.now() / 1000 - payload.iat;

        // 1 saatten eski token'ları yenile
        if (tokenAge > 3600) {
            console.log('Token is old, refreshing...');

            const response = await fetch('/.netlify/functions/refresh-token', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.token) {
                    localStorage.setItem('userToken', data.token);
                    console.log('Token refreshed successfully');
                }
            }
        }
    } catch (error) {
        console.warn('Token refresh failed:', error);
    }
}

// Modal'ı kapat
function closeLimitModal() {
    const modal = document.getElementById('limitModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Destek sayfasına yönlendir (sadece giriş yapmış kullanıcılar için)
function goToSupport() {
    const token = localStorage.getItem('userToken');

    if (!token) {
        // Giriş yapmamış kullanıcılar için uyarı göster
        alert('Destek talebi oluşturmak için önce giriş yapmanız gerekiyor.');
        return;
    }

    // Modal'ı kapat
    closeLimitModal();

    // Destek sayfasına yönlendir
    window.location.href = '/support';
}

function showTrialLimit() {
    const trialLimit = document.getElementById('trial-limit');
    const mainContent = document.getElementById('main-content');

    trialLimit.style.display = 'block';
    mainContent.style.display = 'none';

    // Sayfayı yukarı kaydır
    trialLimit.scrollIntoView({ behavior: 'smooth' });
}

// Sekme değiştirme
function setupTabSwitching() {
    console.log('Setting up tab switching...');
    console.log('Tab buttons found:', tabButtons.length);

    tabButtons.forEach((button, index) => {
        console.log(`Tab button ${index}:`, button.dataset.tab);
        button.addEventListener('click', (e) => {
            console.log('Tab clicked:', button.dataset.tab);
            const tabId = button.dataset.tab;
            switchTab(tabId);
        });
    });
}

function switchTab(tabId) {
    console.log('Switching to tab:', tabId);

    // Aktif sekmeyi kaldır
    tabButtons.forEach(btn => btn.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    // Yeni sekmeyi aktif et
    const tabButton = document.querySelector(`[data-tab="${tabId}"]`);
    const tabContent = document.getElementById(`${tabId}-tab`);

    if (tabButton && tabContent) {
        tabButton.classList.add('active');
        tabContent.classList.add('active');
        console.log('Tab switched successfully to:', tabId);
    } else {
        console.error('Tab elements not found:', { tabButton, tabContent, tabId });
    }

    // Mesajları temizle
    hideMessages();
    hideResult();
}

// PDF Upload işlemleri
function setupPDFUpload() {
    console.log('Setting up PDF upload...');
    console.log('PDF elements:', {
        pdfUploadArea: !!pdfUploadArea,
        pdfFileInput: !!pdfFileInput,
        pdfRemoveFile: !!pdfRemoveFile
    });

    if (!pdfUploadArea || !pdfFileInput) {
        console.error('PDF upload elements not found!');
        return;
    }

    // Click ile dosya seçme
    pdfUploadArea.addEventListener('click', () => {
        console.log('PDF upload area clicked');
        if (!isProcessing) {
            pdfFileInput.click();
        } else {
            console.log('Processing in progress, ignoring click');
        }
    });

    // Dosya seçildiğinde
    pdfFileInput.addEventListener('change', handleFileSelect);

    // Drag & Drop
    pdfUploadArea.addEventListener('dragover', handleDragOver);
    pdfUploadArea.addEventListener('dragleave', handleDragLeave);
    pdfUploadArea.addEventListener('drop', handleFileDrop);

    // Dosya kaldırma
    if (pdfRemoveFile) {
        pdfRemoveFile.addEventListener('click', removeFile);
    }

    console.log('PDF upload setup completed');
}

function handleDragOver(e) {
    e.preventDefault();
    pdfUploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    pdfUploadArea.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    pdfUploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    // Dosya türü kontrolü
    if (file.type !== 'application/pdf') {
        showError('Lütfen sadece PDF dosyası yükleyin.');
        return;
    }
    
    // Dosya boyutu kontrolü (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showError('Dosya boyutu 10MB\'dan büyük olamaz.');
        return;
    }
    
    currentFile = file;
    displayFileInfo(file);
    pdfSubmitBtn.disabled = false;
    hideMessages();
}

function displayFileInfo(file) {
    pdfFileName.textContent = file.name;
    pdfFileSize.textContent = formatFileSize(file.size);
    pdfUploadArea.style.display = 'none';
    pdfFileDetails.style.display = 'block';
}

function removeFile() {
    currentFile = null;
    pdfFileInput.value = '';
    pdfUploadArea.style.display = 'block';
    pdfFileDetails.style.display = 'none';
    pdfSubmitBtn.disabled = true;
    hideMessages();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Metin girişi işlemleri
function setupTextInput() {
    console.log('Setting up text input...');
    console.log('Text input element:', !!textInput);

    if (!textInput) {
        console.error('Text input element not found!');
        return;
    }

    textInput.addEventListener('input', handleTextInput);
    console.log('Text input setup completed');
}

function handleTextInput() {
    const text = textInput.value.trim();
    const length = text.length;

    // Karakter sayısını güncelle ve renklendirme yap
    updateCharacterCount(length);

    // Buton durumunu güncelle
    updateTextSubmitButton(text, length);

    hideMessages();
}

function updateCharacterCount(length) {
    const charCountElement = charCount;
    charCountElement.textContent = `${length.toLocaleString()} / ${MAX_CHARACTERS.toLocaleString()} karakter`;

    // Renklendirme
    if (length > MAX_CHARACTERS) {
        charCountElement.className = 'char-count error';
        charCountElement.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${length.toLocaleString()} / ${MAX_CHARACTERS.toLocaleString()} karakter
            <span class="char-limit-warning">Karakter sınırı aşıldı!</span>
        `;
    } else if (length > CHARACTER_WARNING_THRESHOLD) {
        charCountElement.className = 'char-count warning';
        charCountElement.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            ${length.toLocaleString()} / ${MAX_CHARACTERS.toLocaleString()} karakter
            <span class="char-limit-warning">Sınıra yaklaşıyorsunuz</span>
        `;
    } else {
        charCountElement.className = 'char-count';
        charCountElement.textContent = `${length.toLocaleString()} / ${MAX_CHARACTERS.toLocaleString()} karakter`;
    }
}

function updateTextSubmitButton(text, length) {
    if (length < 50) {
        // Özel durum: disabled buton için farklı ikon
        const btnText = textSubmitBtn.querySelector('.btn-text');
        if (btnText) {
            btnText.innerHTML = '<i class="fas fa-exclamation-circle"></i> En az 50 karakter gerekli';
        }
        textSubmitBtn.disabled = true;
        charCount.style.color = '#dc3545';
    } else if (length > MAX_CHARACTERS) {
        // Özel durum: disabled buton için farklı ikon
        const btnText = textSubmitBtn.querySelector('.btn-text');
        if (btnText) {
            btnText.innerHTML = '<i class="fas fa-ban"></i> Karakter sınırı aşıldı';
        }
        textSubmitBtn.disabled = true;
        charCount.style.color = '#dc3545';
    } else {
        setButtonState(textSubmitBtn, 'normal', 'Özet Oluştur');
        charCount.style.color = length > CHARACTER_WARNING_THRESHOLD ? '#f59e0b' : '#28a745';
    }
}

// Form gönderimi
function setupFormSubmissions() {
    console.log('Setting up form submissions...');
    console.log('Submit buttons:', {
        pdfSubmitBtn: !!pdfSubmitBtn,
        textSubmitBtn: !!textSubmitBtn
    });

    if (!pdfSubmitBtn || !textSubmitBtn) {
        console.error('Submit buttons not found!');
        return;
    }

    pdfSubmitBtn.addEventListener('click', (e) => {
        console.log('PDF submit button clicked');
        handlePDFSubmit();
    });

    textSubmitBtn.addEventListener('click', (e) => {
        console.log('Text submit button clicked');
        handleTextSubmit();
    });

    console.log('Form submissions setup completed');
}

async function handlePDFSubmit() {
    if (!currentFile || isProcessing) return;

    // PDF.js yüklenmesini bekle
    const pdfJSReady = await waitForPDFJS();
    if (!pdfJSReady) {
        showError('PDF.js kütüphanesi yüklenemedi. Lütfen sayfayı yenileyin.');
        return;
    }

    // Kullanıcı giriş yapmışsa günlük limit kontrolü
    const token = localStorage.getItem('userToken');
    if (token) {
        const limitCheck = await checkDailyLimit();
        if (!limitCheck.allowed) {
            showDailyLimitExceeded(limitCheck);
            return;
        }
    } else {
        // Deneme limiti kontrolü (giriş yapmamış kullanıcılar için)
        const trialCheck = await checkTrialLimit();
        if (!trialCheck.allowed) {
            showTrialLimit();
            return;
        }
    }

    try {
        isProcessing = true;
        hideMessages();

        // İlk aşama: PDF yükleme ve okuma
        showProcessingStep('PDF dosyası okunuyor...', 1, 3);
        setButtonState(pdfSubmitBtn, 'processing', 'PDF Okunuyor...');

        // PDF'den metin çıkar
        const text = await extractTextFromPDF(currentFile);

        if (!text.trim()) {
            throw new Error('PDF dosyasından metin çıkarılamadı. Lütfen metin içeren bir PDF dosyası yükleyin.');
        }

        // Karakter sınırı kontrolü
        if (text.length > MAX_CHARACTERS) {
            throw new Error(`PDF dosyası çok uzun! Maksimum ${MAX_CHARACTERS.toLocaleString()} karakter desteklenmektedir. Bu dosya ${text.length.toLocaleString()} karakter içeriyor.`);
        }

        // İkinci aşama: Metin analizi
        showProcessingStep('Metin analiz ediliyor...', 2, 3);
        setButtonState(pdfSubmitBtn, 'processing', 'Analiz Ediliyor...');

        // Kısa bir bekleme (kullanıcının aşamayı görmesi için)
        await new Promise(resolve => setTimeout(resolve, 500));

        // Üçüncü aşama: Özet oluşturma
        showProcessingStep('Özet oluşturuluyor...', 3, 3);
        setButtonState(pdfSubmitBtn, 'processing', 'Özet Oluşturuluyor...');

        // Özet oluştur
        const summary = await generateSummary(text);

        // İşlem tamamlandı
        hideProcessingStep();
        showResult(summary);

        // Kullanım kaydı (sadece anonim kullanıcılar için - kayıtlı kullanıcılar için generate-summary.js'de yapılıyor)
        if (!token) {
            await recordTrialUsage();
        }

    } catch (error) {
        console.error('PDF işleme hatası:', error);
        hideProcessingStep();
        showError(error.message || 'PDF işlenirken bir hata oluştu.');
    } finally {
        setButtonState(pdfSubmitBtn, 'normal', 'Özet Oluştur');
        isProcessing = false;
    }
}

async function handleTextSubmit() {
    const text = textInput.value.trim();

    if (text.length < 50 || text.length > MAX_CHARACTERS || isProcessing) return;

    // Kullanıcı giriş yapmışsa günlük limit kontrolü
    const token = localStorage.getItem('userToken');
    if (token) {
        const limitCheck = await checkDailyLimit();
        if (!limitCheck.allowed) {
            showDailyLimitExceeded(limitCheck);
            return;
        }
    } else {
        // Deneme limiti kontrolü (giriş yapmamış kullanıcılar için)
        const trialCheck = await checkTrialLimit();
        if (!trialCheck.allowed) {
            showTrialLimit();
            return;
        }
    }

    try {
        isProcessing = true;
        hideMessages();

        // Metin için tek aşamalı işlem
        showProcessingStep('Özet oluşturuluyor...', 1, 1);
        setButtonState(textSubmitBtn, 'processing', 'Özet Oluşturuluyor...');

        // Özet oluştur
        const summary = await generateSummary(text);

        // İşlem tamamlandı
        hideProcessingStep();
        showResult(summary);

        // Kullanım kaydı (sadece anonim kullanıcılar için - kayıtlı kullanıcılar için generate-summary.js'de yapılıyor)
        if (!token) {
            await recordTrialUsage();
        }

    } catch (error) {
        console.error('Metin işleme hatası:', error);
        hideProcessingStep();
        showError(error.message || 'Metin işlenirken bir hata oluştu.');
    } finally {
        setButtonState(textSubmitBtn, 'normal', 'Özet Oluştur');
        isProcessing = false;
    }
}

// PDF'den metin çıkarma - CSP uyumlu
async function extractTextFromPDF(file) {
    return new Promise((resolve, reject) => {
        // PDF.js yüklü mü kontrol et
        if (typeof pdfjsLib === 'undefined') {
            reject(new Error('PDF.js kütüphanesi yüklenemedi. Lütfen sayfayı yenileyin.'));
            return;
        }

        const fileReader = new FileReader();

        fileReader.onload = async function() {
            try {
                const typedarray = new Uint8Array(this.result);

                // PDF.js konfigürasyonu - Worker'sız CSP uyumlu
                const loadingTask = pdfjsLib.getDocument({
                    data: typedarray,
                    // Worker'ı tamamen devre dışı bırak
                    disableWorker: true,
                    useWorkerFetch: false,
                    isEvalSupported: false,
                    useSystemFonts: true,
                    // CSP uyumlu ayarlar
                    cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
                    cMapPacked: true
                });

                const pdf = await loadingTask.promise;
                let fullText = '';

                for (let i = 1; i <= pdf.numPages; i++) {
                    const page = await pdf.getPage(i);
                    const textContent = await page.getTextContent();
                    const pageText = textContent.items.map(item => item.str).join(' ');
                    fullText += pageText + '\n';
                }

                resolve(fullText);
            } catch (error) {
                console.error('PDF processing error:', error);
                reject(new Error('PDF dosyası okunamadı: ' + error.message));
            }
        };

        fileReader.onerror = () => {
            reject(new Error('Dosya okunamadı.'));
        };

        fileReader.readAsArrayBuffer(file);
    });
}

// API çağrısı - Özet oluşturma
async function generateSummary(text) {
    try {
        const token = localStorage.getItem('userToken');
        const headers = {
            'Content-Type': 'application/json',
        };

        // Token varsa Authorization header'ı ekle
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        // Timeout ile fetch
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 saniye timeout

        const response = await fetch('/.netlify/functions/generate-summary', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({ text: text }),
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.summary) {
            throw new Error('Özet oluşturulamadı.');
        }
        
        return data.summary;
        
    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('İşlem zaman aşımına uğradı. Lütfen daha kısa bir metin deneyin.');
        } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.');
        }
        throw error;
    }
}

// Yardımcı fonksiyonlar
function setLoading(button, loading) {
    const btnText = button.querySelector('.btn-text');
    const spinner = button.querySelector('.loading-spinner');

    if (loading) {
        btnText.style.display = 'none';
        spinner.style.display = 'block';
        button.disabled = true;
    } else {
        btnText.style.display = 'block';
        spinner.style.display = 'none';
        button.disabled = false;
    }
}

// Gelişmiş buton durumu kontrolü
function setButtonState(button, state, text) {
    if (!button) {
        console.warn('setButtonState: button is null');
        return;
    }

    const btnText = button.querySelector('.btn-text');
    const spinner = button.querySelector('.loading-spinner');

    if (!btnText) {
        console.warn('setButtonState: .btn-text not found in button');
        return;
    }

    switch (state) {
        case 'normal':
            btnText.innerHTML = '<i class="fas fa-magic"></i> ' + text;
            btnText.style.display = 'block';
            if (spinner) spinner.style.display = 'none';
            button.disabled = false;
            button.style.cursor = 'pointer';
            break;
        case 'processing':
            btnText.innerHTML = '<i class="fas fa-cog fa-spin"></i> ' + text;
            btnText.style.display = 'block';
            if (spinner) spinner.style.display = 'none';
            button.disabled = true;
            button.style.cursor = 'not-allowed';
            break;
        case 'disabled':
            btnText.innerHTML = '<i class="fas fa-magic"></i> ' + text;
            btnText.style.display = 'block';
            if (spinner) spinner.style.display = 'none';
            button.disabled = true;
            button.style.cursor = 'not-allowed';
            break;
    }
}

// İşlem aşaması gösterimi
function showProcessingStep(message, currentStep, totalSteps) {
    // Mevcut progress container'ı kontrol et
    let progressContainer = document.getElementById('processing-progress');

    if (!progressContainer) {
        // Progress container oluştur
        progressContainer = document.createElement('div');
        progressContainer.id = 'processing-progress';
        progressContainer.innerHTML = `
            <div class="processing-overlay">
                <div class="processing-card">
                    <div class="processing-header">
                        <i class="fas fa-cog fa-spin processing-icon"></i>
                        <h3>İşlem Devam Ediyor</h3>
                    </div>
                    <div class="processing-message" id="processing-message"></div>
                    <div class="processing-progress-bar">
                        <div class="progress-track">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-text" id="progress-text"></div>
                    </div>
                    <div class="processing-steps">
                        <div class="step-indicator">
                            <div class="step" id="step-1"><span>1</span><label>PDF Okuma</label></div>
                            <div class="step" id="step-2"><span>2</span><label>Analiz</label></div>
                            <div class="step" id="step-3"><span>3</span><label>Özet</label></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // CSS stilleri ekle
        const style = document.createElement('style');
        style.textContent = `
            .processing-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(4px);
            }

            .processing-card {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                text-align: center;
            }

            .processing-header {
                margin-bottom: 1.5rem;
            }

            .processing-icon {
                font-size: 2.5rem;
                color: var(--primary-color, #4f46e5);
                margin-bottom: 1rem;
            }

            .processing-header h3 {
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--gray-900, #111827);
                margin: 0;
            }

            .processing-message {
                font-size: 1rem;
                color: var(--gray-600, #4b5563);
                margin-bottom: 1.5rem;
                font-weight: 500;
            }

            .processing-progress-bar {
                margin-bottom: 1.5rem;
            }

            .progress-track {
                width: 100%;
                height: 8px;
                background: var(--gray-200, #e5e7eb);
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 0.5rem;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, var(--primary-color, #4f46e5), var(--secondary-color, #8b5cf6));
                border-radius: 4px;
                transition: width 0.5s ease;
                width: 0%;
            }

            .progress-text {
                font-size: 0.875rem;
                color: var(--gray-600, #4b5563);
                font-weight: 500;
            }

            .step-indicator {
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;
            }

            .step-indicator::before {
                content: '';
                position: absolute;
                top: 15px;
                left: 15px;
                right: 15px;
                height: 2px;
                background: var(--gray-200, #e5e7eb);
                z-index: 1;
            }

            .step {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                z-index: 2;
            }

            .step span {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: var(--gray-200, #e5e7eb);
                color: var(--gray-500, #6b7280);
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 0.875rem;
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;
            }

            .step.active span {
                background: var(--primary-color, #4f46e5);
                color: white;
            }

            .step.completed span {
                background: var(--success-color, #10b981);
                color: white;
            }

            .step label {
                font-size: 0.75rem;
                color: var(--gray-600, #4b5563);
                font-weight: 500;
                text-align: center;
            }
        `;

        if (!document.getElementById('processing-styles')) {
            style.id = 'processing-styles';
            document.head.appendChild(style);
        }

        document.body.appendChild(progressContainer);
    }

    // Mesajı güncelle
    document.getElementById('processing-message').textContent = message;

    // Progress bar'ı güncelle
    const progressPercentage = (currentStep / totalSteps) * 100;
    document.getElementById('progress-fill').style.width = progressPercentage + '%';
    document.getElementById('progress-text').textContent = `Adım ${currentStep} / ${totalSteps}`;

    // Step indicator'ları güncelle
    for (let i = 1; i <= totalSteps; i++) {
        const stepElement = document.getElementById(`step-${i}`);
        if (stepElement) {
            stepElement.classList.remove('active', 'completed');
            if (i < currentStep) {
                stepElement.classList.add('completed');
            } else if (i === currentStep) {
                stepElement.classList.add('active');
            }
        }
    }
}

// İşlem aşaması gizleme
function hideProcessingStep() {
    const progressContainer = document.getElementById('processing-progress');
    if (progressContainer) {
        progressContainer.remove();
    }
}

function showResult(summary) {
    resultContent.textContent = summary;
    resultSection.style.display = 'block';
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

function hideResult() {
    resultSection.style.display = 'none';
}

function showError(message) {
    errorText.textContent = message;
    errorMessage.style.display = 'flex';
    hideSuccess();
    errorMessage.scrollIntoView({ behavior: 'smooth' });
}

function showSuccess(message) {
    successText.textContent = message;
    successMessage.style.display = 'flex';
    hideError();
}

function hideError() {
    errorMessage.style.display = 'none';
}

function hideSuccess() {
    successMessage.style.display = 'none';
}

function hideMessages() {
    hideError();
    hideSuccess();
}

// Kopyalama işlemi
function setupCopyButton() {
    copyResultBtn.addEventListener('click', copyResult);
}

async function copyResult() {
    try {
        await navigator.clipboard.writeText(resultContent.textContent);
        showSuccess('Özet başarıyla kopyalandı!');
        
        // 3 saniye sonra mesajı gizle
        setTimeout(hideSuccess, 3000);
    } catch (error) {
        // Fallback: manuel seçim
        const range = document.createRange();
        range.selectNode(resultContent);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        
        try {
            document.execCommand('copy');
            showSuccess('Özet başarıyla kopyalandı!');
            setTimeout(hideSuccess, 3000);
        } catch (fallbackError) {
            showError('Kopyalama işlemi başarısız oldu.');
        }
        
        window.getSelection().removeAllRanges();
    }
}

// Mobil menü kontrolü
function setupMobileMenu() {
    console.log('Setting up mobile menu...');

    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    console.log('Mobile menu elements:', {
        toggle: !!mobileMenuToggle,
        menu: !!mobileMenu
    });

    if (mobileMenuToggle && mobileMenu) {
        console.log('Mobile menu elements found, adding event listeners');

        // Mobil menü toggle
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Mobile menu toggle clicked');

            mobileMenu.classList.toggle('active');

            // İkon değiştir
            const icon = mobileMenuToggle.querySelector('i');
            if (mobileMenu.classList.contains('active')) {
                icon.className = 'fas fa-times';
                console.log('Menu opened');
            } else {
                icon.className = 'fas fa-bars';
                console.log('Menu closed');
            }
        });

        // Menü dışına tıklandığında kapat
        document.addEventListener('click', function(event) {
            if (!mobileMenuToggle.contains(event.target) && !mobileMenu.contains(event.target)) {
                if (mobileMenu.classList.contains('active')) {
                    mobileMenu.classList.remove('active');
                    mobileMenuToggle.querySelector('i').className = 'fas fa-bars';
                    console.log('Menu closed by outside click');
                }
            }
        });

        // Mobil logout butonu
        const mobileLogoutBtn = document.getElementById('mobile-logout-btn');
        if (mobileLogoutBtn) {
            mobileLogoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                logout();
                mobileMenu.classList.remove('active');
                mobileMenuToggle.querySelector('i').className = 'fas fa-bars';
            });
        }

        // Mobil menü linklerine tıklandığında menüyü kapat
        const mobileNavLinks = mobileMenu.querySelectorAll('.mobile-nav-link, .mobile-nav-btn');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Sadece gerçek linkler için menüyü kapat (logout butonu hariç)
                if (!this.id || this.id !== 'mobile-logout-btn') {
                    mobileMenu.classList.remove('active');
                    mobileMenuToggle.querySelector('i').className = 'fas fa-bars';
                }
            });
        });

    } else {
        console.error('Mobile menu elements not found!');
        console.log('Available elements:', {
            'mobile-menu-toggle': document.getElementById('mobile-menu-toggle'),
            'mobile-menu': document.getElementById('mobile-menu')
        });
    }
}

// Eski mobile auth status fonksiyonu kaldırıldı - artık mobile-menu.js kullanılıyor

// Global mobil menü toggle fonksiyonu
function toggleMobileMenu() {
    console.log('toggleMobileMenu called');

    const menu = document.getElementById('mobile-menu');
    const toggle = document.getElementById('mobile-menu-toggle');

    if (menu && toggle) {
        menu.classList.toggle('active');

        // İkon değiştir
        const icon = toggle.querySelector('i');
        if (menu.classList.contains('active')) {
            icon.className = 'fas fa-times';
            console.log('Menu opened');
        } else {
            icon.className = 'fas fa-bars';
            console.log('Menu closed');
        }
    } else {
        console.error('Mobile menu elements not found');
    }
}

// Mobil logout fonksiyonu
function mobileLogout() {
    console.log('Mobile logout called');
    logout();

    // Menüyü kapat
    const menu = document.getElementById('mobile-menu');
    const toggle = document.getElementById('mobile-menu-toggle');
    if (menu && toggle) {
        menu.classList.remove('active');
        toggle.querySelector('i').className = 'fas fa-bars';
    }
}

// Test fonksiyonu - mobil menü için
function testMobileMenu() {
    console.log('Testing mobile menu...');
    const toggle = document.getElementById('mobile-menu-toggle');
    const menu = document.getElementById('mobile-menu');

    if (toggle && menu) {
        console.log('Elements found, testing toggle...');
        menu.classList.toggle('active');
        console.log('Menu active:', menu.classList.contains('active'));
    } else {
        console.error('Elements not found:', { toggle: !!toggle, menu: !!menu });
    }
}

// PDF.js yükleme durumunu kontrol et
function checkPDFJSStatus() {
    if (typeof pdfjsLib !== 'undefined') {
        console.log('✅ PDF.js loaded successfully');
        try {
            pdfjsLib.GlobalWorkerOptions.workerSrc = false;
            console.log('✅ PDF.js worker disabled for CSP compliance');
            return true;
        } catch (error) {
            console.error('❌ PDF.js configuration error:', error);
            return false;
        }
    } else {
        console.error('❌ PDF.js not loaded');
        return false;
    }
}

// PDF.js yüklenmesini bekle
async function waitForPDFJS(maxWait = 5000) {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
        if (typeof pdfjsLib !== 'undefined') {
            return checkPDFJSStatus();
        }
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.error('❌ PDF.js loading timeout');
    return false;
}

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // DOM elementlerini initialize et
    const domReady = initializeDOMElements();
    if (!domReady) {
        console.error('❌ Critical DOM elements missing, retrying in 500ms...');
        setTimeout(() => {
            const retryDomReady = initializeDOMElements();
            if (retryDomReady) {
                setupAllFunctions();
            } else {
                console.error('❌ DOM elements still missing after retry');
            }
        }, 500);
        return;
    }

    setupAllFunctions();
});

function setupAllFunctions() {
    console.log('Setting up all functions...');

    // PDF.js durumunu kontrol et
    setTimeout(() => {
        checkPDFJSStatus();
    }, 200);

    // Temel setup fonksiyonları
    setupTabSwitching();
    setupPDFUpload();
    setupTextInput();
    setupFormSubmissions();
    setupCopyButton();

    // Auth kontrolü
    checkUserAuth();

    // Sayfa yüklendiğinde otomatik quota kontrolü
    const token = localStorage.getItem('userToken');
    if (token) {
        checkAndResetDailyQuota();
    }

    // Mobil menüyü biraz gecikmeyle kur
    setTimeout(function() {
        setupMobileMenu();
    }, 100);

    // İlk karakter sayısını ayarla
    if (charCount) {
        updateCharacterCount(0);
    }

    // Test fonksiyonunu global scope'a ekle
    window.testMobileMenu = testMobileMenu;
    window.toggleMobileMenu = toggleMobileMenu;
    window.mobileLogout = mobileLogout;
    window.checkPDFJSStatus = checkPDFJSStatus; // Debug için
    window.initializeDOMElements = initializeDOMElements; // Debug için

    // Menü dışına tıklama event'i
    document.addEventListener('click', function(event) {
        const menu = document.getElementById('mobile-menu');
        const toggle = document.getElementById('mobile-menu-toggle');

        if (menu && toggle && menu.classList.contains('active')) {
            if (!toggle.contains(event.target) && !menu.contains(event.target)) {
                menu.classList.remove('active');
                toggle.querySelector('i').className = 'fas fa-bars';
                console.log('Menu closed by outside click');
            }
        }
    });

    console.log('✅ All functions setup completed');
}

// Window load event'inde de kontrol et
window.addEventListener('load', function() {
    setTimeout(() => {
        console.log('Window loaded, checking PDF.js again...');
        checkPDFJSStatus();

        // DOM elementleri tekrar kontrol et
        if (!tabButtons || tabButtons.length === 0) {
            console.log('Retrying DOM initialization on window load...');
            initializeDOMElements();
        }
    }, 300);
});
