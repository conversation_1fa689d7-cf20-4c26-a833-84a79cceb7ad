// Supabase Configuration
import { createClient } from '@supabase/supabase-js';

// Supabase URL ve Keys (Environment variables'dan alınac<PERSON>)
const supabaseUrl = process.env.SUPABASE_URL || 'YOUR_SUPABASE_URL';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY';
// Service role key - hem eski hem yeni isimleri dene
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || 'YOUR_SERVICE_KEY';

// Supabase client oluştur (service key ile admin işlemleri için)
export const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Anon client (public işlemler için)
export const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

// Database tabloları
export const TABLES = {
    USERS: 'users',
    APPLICATIONS: 'applications',
    SUMMARIES: 'summaries',
    ADMIN_LOGS: 'admin_logs',
    SUPPORT_TICKETS: 'support_tickets',
    SUPPORT_TICKET_REPLIES: 'support_ticket_replies',
    SUPPORT_CATEGORIES: 'support_categories',
    SUPPORT_TICKET_HISTORY: 'support_ticket_history'
};

// Kullanıcı durumları
export const USER_STATUS = {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    SUSPENDED: 'suspended'
};

// Destek ticket durumları
export const TICKET_STATUS = {
    OPEN: 'open',
    IN_PROGRESS: 'in_progress',
    WAITING: 'waiting',
    RESOLVED: 'resolved',
    CLOSED: 'closed'
};

// Destek ticket öncelikleri
export const TICKET_PRIORITY = {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
};

// Başvuru durumları
export const APPLICATION_STATUS = {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected'
};

// Supabase helper functions
export const supabaseHelpers = {
    // Kullanıcı oluştur
    async createUser(userData) {
        const { data, error } = await supabase
            .from(TABLES.USERS)
            .insert([userData])
            .select();
        
        if (error) throw error;
        return data[0];
    },

    // Kullanıcı getir
    async getUser(email) {
        const { data, error } = await supabase
            .from(TABLES.USERS)
            .select('*')
            .eq('email', email)
            .single();
        
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },

    // Başvuru oluştur
    async createApplication(applicationData) {
        const { data, error } = await supabase
            .from(TABLES.APPLICATIONS)
            .insert([applicationData])
            .select();
        
        if (error) throw error;
        return data[0];
    },

    // Bekleyen başvuruları getir
    async getPendingApplications() {
        const { data, error } = await supabase
            .from(TABLES.APPLICATIONS)
            .select('*')
            .eq('status', APPLICATION_STATUS.PENDING)
            .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data;
    },

    // Başvuruyu onayla
    async approveApplication(applicationId, adminId) {
        const { data, error } = await supabase
            .from(TABLES.APPLICATIONS)
            .update({ 
                status: APPLICATION_STATUS.APPROVED,
                approved_at: new Date().toISOString(),
                approved_by: adminId
            })
            .eq('id', applicationId)
            .select();
        
        if (error) throw error;
        return data[0];
    },

    // Özet kaydet
    async saveSummary(summaryData) {
        const { data, error } = await supabase
            .from(TABLES.SUMMARIES)
            .insert([summaryData])
            .select();
        
        if (error) throw error;
        return data[0];
    },

    // İstatistikleri getir
    async getStats() {
        const [usersResult, summariesResult, todaySummariesResult] = await Promise.all([
            supabase.from(TABLES.USERS).select('id', { count: 'exact' }),
            supabase.from(TABLES.SUMMARIES).select('id', { count: 'exact' }),
            supabase.from(TABLES.SUMMARIES)
                .select('id', { count: 'exact' })
                .gte('created_at', new Date().toISOString().split('T')[0])
        ]);

        return {
            totalUsers: usersResult.count || 0,
            totalSummaries: summariesResult.count || 0,
            todaySummaries: todaySummariesResult.count || 0
        };
    },

    // Destek ticket oluştur
    async createSupportTicket(ticketData) {
        const { data, error } = await supabase
            .from(TABLES.SUPPORT_TICKETS)
            .insert([ticketData])
            .select();

        if (error) throw error;
        return data[0];
    },

    // Kullanıcının ticketlarını getir
    async getUserTickets(userId, status = null) {
        let query = supabase
            .from(TABLES.SUPPORT_TICKETS)
            .select(`
                *,
                support_categories(name, color, icon),
                support_ticket_replies(count)
            `)
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

        if (status) {
            query = query.eq('status', status);
        }

        const { data, error } = await query;
        if (error) throw error;
        return data;
    },

    // Ticket yanıtı ekle
    async addTicketReply(replyData) {
        const { data, error } = await supabase
            .from(TABLES.SUPPORT_TICKET_REPLIES)
            .insert([replyData])
            .select();

        if (error) throw error;
        return data[0];
    },

    // Destek kategorilerini getir
    async getSupportCategories() {
        const { data, error } = await supabase
            .from(TABLES.SUPPORT_CATEGORIES)
            .select('*')
            .eq('is_active', true)
            .order('sort_order');

        if (error) throw error;
        return data;
    },

    // Ticket detayını getir (yanıtlarla birlikte)
    async getTicketDetails(ticketId, userId = null) {
        let query = supabase
            .from(TABLES.SUPPORT_TICKETS)
            .select(`
                *,
                support_categories(name, color, icon),
                support_ticket_replies(
                    id,
                    message,
                    is_admin_reply,
                    is_internal_note,
                    created_at,
                    user_id,
                    admin_id
                )
            `)
            .eq('id', ticketId);

        if (userId) {
            query = query.eq('user_id', userId);
        }

        const { data, error } = await query.single();
        if (error) throw error;
        return data;
    }
};
