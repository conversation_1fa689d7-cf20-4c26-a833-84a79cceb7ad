<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Prompt Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f9fafb;
        }
        
        .test-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .test-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .test-button:hover {
            background: #4338ca;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .result-box {
            background: #f3f4f6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .loading {
            color: #4f46e5;
        }
        
        .info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #dbeafe;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <h1>🔧 System Prompt Test Sayfası</h1>
    
    <div class="info">
        <strong>Bu sayfa SYSTEM_PROMPT environment variable'ının doğru çalışıp çalışmadığını test eder.</strong><br>
        Netlify Functions'da environment variable'ların algılanıp algılanmadığını kontrol edebilirsiniz.
    </div>

    <div class="test-card">
        <div class="test-title">1. Environment Variable Test</div>
        <p>SYSTEM_PROMPT environment variable'ının varlığını ve içeriğini kontrol eder.</p>
        <button class="test-button" onclick="testEnvironmentVariable()">Environment Variable Test</button>
        <div id="env-result" class="result-box" style="display: none;"></div>
    </div>

    <div class="test-card">
        <div class="test-title">2. Basit Özet Test</div>
        <p>Kısa bir metin ile özet oluşturma fonksiyonunu test eder.</p>
        <button class="test-button" onclick="testSimpleSummary()">Basit Özet Test</button>
        <div id="summary-result" class="result-box" style="display: none;"></div>
    </div>

    <div class="test-card">
        <div class="test-title">3. Detaylı Function Test</div>
        <p>Generate-summary function'ının tüm parametrelerini test eder.</p>
        <button class="test-button" onclick="testDetailedFunction()">Detaylı Function Test</button>
        <div id="detailed-result" class="result-box" style="display: none;"></div>
    </div>

    <div class="test-card">
        <div class="test-title">4. Hata Durumu Test</div>
        <p>Hatalı durumları test eder ve error handling'i kontrol eder.</p>
        <button class="test-button" onclick="testErrorHandling()">Hata Durumu Test</button>
        <div id="error-result" class="result-box" style="display: none;"></div>
    </div>

    <script>
        // Test 1: Environment Variable Test
        async function testEnvironmentVariable() {
            const resultDiv = document.getElementById('env-result');
            const button = event.target;
            
            button.disabled = true;
            button.textContent = 'Test Ediliyor...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result-box loading';
            resultDiv.textContent = 'Environment variable test ediliyor...';

            try {
                const response = await fetch('/.netlify/functions/test-env', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result-box success';
                    resultDiv.textContent = `✅ Test Başarılı!\n\nResponse: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result-box error';
                    resultDiv.textContent = `❌ Test Başarısız!\n\nStatus: ${response.status}\nError: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result-box error';
                resultDiv.textContent = `❌ Network Hatası!\n\nError: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Environment Variable Test';
            }
        }

        // Test 2: Basit Özet Test
        async function testSimpleSummary() {
            const resultDiv = document.getElementById('summary-result');
            const button = event.target;
            
            button.disabled = true;
            button.textContent = 'Test Ediliyor...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result-box loading';
            resultDiv.textContent = 'Basit özet test ediliyor...';

            const testText = "Bu bir test metnidir. Davacı vekili dava dilekçesinde, davalının sözleşmeyi ihlal ettiğini ve bunun sonucunda maddi zarar gördüğünü belirtmiştir. Davacı, davalıdan 50.000 TL tazminat talep etmektedir.";

            try {
                const response = await fetch('/.netlify/functions/generate-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: testText })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result-box success';
                    resultDiv.textContent = `✅ Özet Başarılı!\n\nTest Metni:\n${testText}\n\nÖzet:\n${data.summary}`;
                } else {
                    resultDiv.className = 'result-box error';
                    resultDiv.textContent = `❌ Özet Başarısız!\n\nStatus: ${response.status}\nError: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result-box error';
                resultDiv.textContent = `❌ Network Hatası!\n\nError: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Basit Özet Test';
            }
        }

        // Test 3: Detaylı Function Test
        async function testDetailedFunction() {
            const resultDiv = document.getElementById('detailed-result');
            const button = event.target;
            
            button.disabled = true;
            button.textContent = 'Test Ediliyor...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result-box loading';
            resultDiv.textContent = 'Detaylı function test ediliyor...';

            try {
                // Önce function'ın mevcut olup olmadığını kontrol et
                const healthResponse = await fetch('/.netlify/functions/generate-summary', {
                    method: 'OPTIONS'
                });

                let healthStatus = `Function Health Check: ${healthResponse.status}\n`;
                
                // Şimdi gerçek test
                const testText = "Davacı vekili Av. Mehmet Yılmaz, dava dilekçesinde müvekkilinin davalı şirket tarafından haksız yere işten çıkarıldığını, bu durumun iş kanununa aykırı olduğunu ve müvekkilinin maddi ve manevi zarar gördüğünü belirtmiştir.";
                
                const response = await fetch('/.netlify/functions/generate-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        text: testText,
                        debug: true 
                    })
                });

                const responseText = await response.text();
                let data;
                
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    data = { rawResponse: responseText, parseError: parseError.message };
                }
                
                resultDiv.className = response.ok ? 'result-box success' : 'result-box error';
                resultDiv.textContent = `${healthStatus}\nStatus: ${response.status}\nHeaders: ${JSON.stringify(Object.fromEntries(response.headers), null, 2)}\n\nResponse:\n${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result-box error';
                resultDiv.textContent = `❌ Detaylı Test Hatası!\n\nError: ${error.message}\nStack: ${error.stack}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Detaylı Function Test';
            }
        }

        // Test 4: Hata Durumu Test
        async function testErrorHandling() {
            const resultDiv = document.getElementById('error-result');
            const button = event.target;
            
            button.disabled = true;
            button.textContent = 'Test Ediliyor...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result-box loading';
            resultDiv.textContent = 'Hata durumu test ediliyor...';

            try {
                // Boş metin ile test
                const response1 = await fetch('/.netlify/functions/generate-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: "" })
                });

                const data1 = await response1.json();
                
                // Çok uzun metin ile test
                const longText = "a".repeat(30000);
                const response2 = await fetch('/.netlify/functions/generate-summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: longText })
                });

                const data2 = await response2.json();
                
                resultDiv.className = 'result-box';
                resultDiv.textContent = `Hata Durumu Test Sonuçları:\n\n1. Boş Metin Test:\nStatus: ${response1.status}\nResponse: ${JSON.stringify(data1, null, 2)}\n\n2. Uzun Metin Test:\nStatus: ${response2.status}\nResponse: ${JSON.stringify(data2, null, 2)}`;
                
            } catch (error) {
                resultDiv.className = 'result-box error';
                resultDiv.textContent = `❌ Hata Test Hatası!\n\nError: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Hata Durumu Test';
            }
        }
    </script>
</body>
</html>
