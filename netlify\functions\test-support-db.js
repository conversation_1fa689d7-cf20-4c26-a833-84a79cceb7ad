// Test Support Database Tables

import { supabase } from '../../supabase-config.js';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Test 1: Check if support_categories table exists
        const { data: categories, error: categoriesError } = await supabase
            .from('support_categories')
            .select('*')
            .limit(1);

        // Test 2: Check if support_tickets table exists
        const { data: tickets, error: ticketsError } = await supabase
            .from('support_tickets')
            .select('*')
            .limit(1);

        // Test 3: Check if support_ticket_replies table exists
        const { data: replies, error: repliesError } = await supabase
            .from('support_ticket_replies')
            .select('*')
            .limit(1);

        // Test 4: Check admin table
        const { data: admins, error: adminsError } = await supabase
            .from('admins')
            .select('id, email, role, is_active')
            .limit(1);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                tests: {
                    support_categories: {
                        exists: !categoriesError,
                        error: categoriesError?.message,
                        count: categories?.length || 0
                    },
                    support_tickets: {
                        exists: !ticketsError,
                        error: ticketsError?.message,
                        count: tickets?.length || 0
                    },
                    support_ticket_replies: {
                        exists: !repliesError,
                        error: repliesError?.message,
                        count: replies?.length || 0
                    },
                    admins: {
                        exists: !adminsError,
                        error: adminsError?.message,
                        count: admins?.length || 0
                    }
                }
            })
        };

    } catch (error) {
        console.error('Test support db error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Test failed',
                details: error.message 
            })
        };
    }
}
