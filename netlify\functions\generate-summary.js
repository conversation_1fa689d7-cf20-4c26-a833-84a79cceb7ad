const { GoogleGenerativeAI } = require('@google/generative-ai');
const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');
const { callGeminiWithFailover } = require('./api-key-manager');
const { getClientIP, checkRateLimit, getSecurityHeaders } = require('./security-middleware');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);
 
// Sistem mesajını environment variable'dan al - Güvenlik için
const SYSTEM_MESSAGE = process.env.SYSTEM_PROMPT || `Lütfen aşağıdaki hukuki belgeyi özetleyin. Özet net, anlaşılır ve önemli noktaları içermelidir.`;

exports.handler = async (event, context) => {
    console.log('Function called:', event.httpMethod);

    // Debug: Environment variables kontrolü
    console.log('Environment variables check:');
    console.log('SYSTEM_PROMPT exists:', !!process.env.SYSTEM_PROMPT);
    console.log('SYSTEM_PROMPT length:', process.env.SYSTEM_PROMPT ? process.env.SYSTEM_PROMPT.length : 0);
    console.log('SYSTEM_PROMPT preview:', process.env.SYSTEM_PROMPT ? process.env.SYSTEM_PROMPT.substring(0, 50) + '...' : 'NOT_FOUND');
    console.log('GEMINI_API_KEY exists:', !!process.env.GEMINI_API_KEY);
    console.log('Current SYSTEM_MESSAGE:', SYSTEM_MESSAGE.substring(0, 100) + '...');

    // 🛡️ GÜVENLİK: Rate limiting kontrolü
    const clientIP = getClientIP(event);
    const rateLimit = checkRateLimit(clientIP, 'generate-summary');

    // CORS headers
    const headers = getSecurityHeaders();

    if (!rateLimit.allowed) {
        return {
            statusCode: 429,
            headers,
            body: JSON.stringify({
                error: 'Çok fazla istek gönderdiniz. Lütfen 15 dakika bekleyin.',
                retryAfter: Math.ceil((15 * 60 * 1000 - (Date.now() - rateLimit.resetTime)) / 1000)
            })
        };
    }

    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    // Sadece POST kabul et
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // API key kontrolü
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            console.error('GEMINI_API_KEY environment variable bulunamadı');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Sunucu konfigürasyon hatası.',
                    debug: {
                        systemPromptExists: !!process.env.SYSTEM_PROMPT,
                        geminiKeyExists: !!process.env.GEMINI_API_KEY,
                        allEnvKeys: Object.keys(process.env).filter(key => !key.includes('KEY') && !key.includes('SECRET'))
                    }
                })
            };
        }

        // Request body parse et
        let body;
        try {
            body = JSON.parse(event.body || '{}');
        } catch (e) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Invalid JSON' })
            };
        }

        // 🛡️ GÜVENLİK: Gelişmiş input validation
        const { text } = body;

        // Temel kontroller
        if (!text || typeof text !== 'string' || text.trim().length === 0) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Metin alanı boş olamaz.' })
            };
        }

        const cleanText = text.trim();

        // Uzunluk kontrolleri
        if (cleanText.length < 50) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Metin en az 50 karakter olmalıdır.' })
            };
        }

        if (cleanText.length > 25000) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    error: `Metin çok uzun! Maksimum 25.000 karakter desteklenmektedir. Gönderilen metin ${cleanText.length.toLocaleString()} karakter içeriyor.`
                })
            };
        }

        // 🛡️ GÜVENLİK: Şüpheli içerik kontrolü
        const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /data:text\/html/i,
            /vbscript:/i,
            /<iframe/i,
            /<object/i,
            /<embed/i
        ];

        if (suspiciousPatterns.some(pattern => pattern.test(cleanText))) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz içerik tespit edildi.' })
            };
        }

        // Kullanıcı token'ından bilgi al
        let userEmail = 'anonymous';
        let userId = null;
        let userData = null;
        let isAuthenticated = false;
        const authHeader = event.headers.authorization;

        if (authHeader && authHeader.startsWith('Bearer ')) {
            try {
                const token = authHeader.substring(7);
                console.log('Token received for summary:', token.substring(0, 50) + '...');

                // JWT token'ı verify et
                userData = jwt.verify(token, process.env.JWT_SECRET);
                userEmail = userData.email;
                userId = userData.userId;
                isAuthenticated = true;

                // Kullanıcının güncel durumunu veritabanından kontrol et
                const { data: currentUser, error: userError } = await supabase
                    .from('users')
                    .select('status, email_verified_at')
                    .eq('id', userData.userId)
                    .single();

                if (userError || !currentUser) {
                    return {
                        statusCode: 404,
                        headers,
                        body: JSON.stringify({
                            error: 'Kullanıcı bulunamadı.'
                        })
                    };
                }

                // Admin onayı kontrolü (güncel status ile)
                if (currentUser.status !== 'approved') {
                    return {
                        statusCode: 403,
                        headers,
                        body: JSON.stringify({
                            error: 'Özet yapabilmek için admin onayı gereklidir.',
                            status: currentUser.status,
                            emailVerified: !!currentUser.email_verified_at
                        })
                    };
                }
            } catch (e) {
                // Token parse edilemezse anonymous olarak devam et
                console.log('Token parse failed, continuing as anonymous');
                isAuthenticated = false;
            }
        }

        // 🛡️ GÜVENLİK: ZORUNLU KONTROL - Tüm istekler için authentication veya trial kontrolü
        if (!isAuthenticated) {
            const clientIP = getClientIP(event);
            console.log('🔍 DEBUG: Unauthenticated request - Client IP detected:', clientIP);

            if (!clientIP) {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'IP adresi alınamadı.' })
                };
            }

            // 🚨 ÖNCE BASIT IP KONTROLÜ - İşlem öncesi hızlı kontrol
            console.log('🔒 SECURITY: Pre-processing IP trial check for:', clientIP);

            try {
                const { data: existingRecord, error: checkError } = await supabase
                    .from('trial_usage')
                    .select('usage_count')
                    .eq('ip_address', clientIP)
                    .single();

                // Eğer kayıt varsa ve limit aşılmışsa direkt reddet
                if (!checkError && existingRecord && (existingRecord.usage_count || 0) >= 1) {
                    console.log('🚫 SECURITY: IP already used trial - blocking immediately');
                    return {
                        statusCode: 429,
                        headers,
                        body: JSON.stringify({
                            error: 'Deneme süresi doldu. Lütfen üye olun.',
                            debug: {
                                ip: clientIP,
                                reason: 'trial_already_used',
                                usage_count: existingRecord.usage_count
                            }
                        })
                    };
                }

                console.log('✅ SECURITY: Pre-check passed - proceeding with atomic trial record');

                // 🛡️ ATOMİK İŞLEM: İlk kullanım için kayıt oluştur
                const { error: insertError } = await supabase
                    .from('trial_usage')
                    .insert([{
                        ip_address: clientIP,
                        user_agent: event.headers['user-agent'] || null,
                        fingerprint: null,
                        usage_count: 1,
                        first_used_at: new Date().toISOString(),
                        last_used_at: new Date().toISOString(),
                        created_at: new Date().toISOString()
                    }]);

                if (insertError) {
                    // Eğer kayıt zaten varsa (race condition), bu IP zaten kullanmış demektir
                    if (insertError.code === '23505') { // Unique constraint violation
                        console.log('🚫 SECURITY: Race condition detected - IP already used trial');
                        return {
                            statusCode: 429,
                            headers,
                            body: JSON.stringify({
                                error: 'Deneme süresi doldu. Lütfen üye olun.',
                                debug: {
                                    ip: clientIP,
                                    reason: 'race_condition_detected'
                                }
                            })
                        };
                    }

                    // Diğer veritabanı hataları
                    console.error('🚫 SECURITY: Database error during trial record:', insertError);
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'Güvenlik kontrolü yapılamadı. Lütfen tekrar deneyin.',
                            debug: {
                                ip: clientIP,
                                reason: 'database_error'
                            }
                        })
                    };
                }

                console.log('✅ SECURITY: Trial usage recorded successfully - allowing request');

            } catch (trialError) {
                console.error('🚫 SECURITY: Trial check error - blocking for security:', trialError);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        error: 'Güvenlik kontrolü yapılamadı. Lütfen tekrar deneyin.',
                        debug: {
                            ip: clientIP,
                            reason: 'security_check_failed'
                        }
                    })
                };
            }
        } else {
            // 🛡️ AUTHENTICATED USER - Günlük limit kontrolü
            console.log('🔒 SECURITY: Authenticated user - checking daily limits');

            // Authenticated kullanıcılar için günlük limit kontrolü
            const canSummarize = await checkDailyLimit(userId);
            if (!canSummarize) {
                return {
                    statusCode: 429,
                    headers,
                    body: JSON.stringify({
                        error: 'Günlük özet hakkınız dolmuştur.',
                        authenticated: true
                    })
                };
            }
        }

        // 🛡️ GÜVENLİK: Bu noktaya sadece yetkilendirilmiş istekler gelebilir
        console.log('🔒 SECURITY: Authorization passed - proceeding with summary generation');

        // Gemini AI ile özet oluştur (yeni failover sistemi)
        const startTime = Date.now();
        const prompt = `${SYSTEM_MESSAGE}\n\nLütfen aşağıdaki hukuki belgeyi yukarıdaki kurallara göre özetleyin:\n\n${cleanText}`;
        const summary = await callGeminiWithFailover(prompt);
        const processingTime = Date.now() - startTime;

        // Özeti Supabase'e kaydet
        try {
            await supabase.from('summaries').insert([{
                user_id: userId,
                user_email: userEmail,
                original_text: cleanText.substring(0, 1000), // İlk 1000 karakter
                summary_text: summary,
                document_type: detectDocumentType(cleanText),
                processing_time_ms: processingTime
            }]);
        } catch (logError) {
            console.error('Summary log error:', logError);
            // Log hatası özet oluşturmayı engellemez
        }

        // 🛡️ GÜVENLİK: Kullanım logu (trial kaydı artık atomik işlemde yapılıyor)
        if (!isAuthenticated) {
            console.log(`🔒 SECURITY: Anonymous summary generated - IP: ${getClientIP(event)}, Length: ${cleanText.length}, Time: ${processingTime}ms`);
        } else {
            console.log(`� SECURITY: Authenticated summary generated - User: ${userEmail}, Length: ${cleanText.length}, Time: ${processingTime}ms`);
        }

        // Kayıtlı kullanıcı için günlük hak sayısını artır
        if (userId && userData) {
            try {
                console.log('Incrementing summary count for user:', userId);
                const { data: incrementData, error: incrementError } = await supabase.rpc('increment_summary_count', {
                    user_id: userId
                });

                if (incrementError) {
                    console.error('RPC increment failed, using fallback:', incrementError);
                    // RPC fonksiyonu yoksa fallback kullan
                    await incrementSummaryCountFallback(userId);
                } else {
                    console.log('Summary count incremented successfully:', incrementData);
                }
            } catch (incrementError) {
                console.error('Error incrementing summary count, using fallback:', incrementError);
                // Hata durumunda fallback kullan
                await incrementSummaryCountFallback(userId);
            }
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                summary: summary,
                success: true
            })
        };

    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Özet oluşturulurken bir hata oluştu.' })
        };
    }
};

// 🛡️ GÜVENLİK: ATOMİK TRIAL KONTROLÜ VE KAYDI - Race condition'ı önler
async function checkAndRecordTrialUsage(clientIP, userAgent) {
    try {
        console.log('🔒 SECURITY: Atomic trial check and record for IP:', clientIP);

        // 🚨 ATOMİK İŞLEM: Önce mevcut kaydı kontrol et ve aynı işlemde güncelle
        const { data: existingRecord, error: selectError } = await supabase
            .from('trial_usage')
            .select('*')
            .eq('ip_address', clientIP)
            .single();

        if (selectError && selectError.code !== 'PGRST116') {
            console.error('🚫 SECURITY: Error checking trial record:', selectError);
            throw selectError;
        }

        const maxTrialUsage = 1; // Sadece 1 deneme hakkı

        if (!existingRecord) {
            // İlk kullanım - kayıt oluştur ve izin ver
            console.log('🔒 SECURITY: First usage - creating record and allowing');

            const { error: insertError } = await supabase
                .from('trial_usage')
                .insert([{
                    ip_address: clientIP,
                    user_agent: userAgent || null,
                    fingerprint: null,
                    usage_count: 1, // İlk kullanım için 1
                    first_used_at: new Date().toISOString(),
                    last_used_at: new Date().toISOString(),
                    created_at: new Date().toISOString()
                }]);

            if (insertError) {
                console.error('🚫 SECURITY: Failed to create trial record:', insertError);
                throw insertError;
            }

            return {
                allowed: true,
                reason: 'first_use_recorded',
                usage_count: 1,
                max_usage: maxTrialUsage,
                remaining: 0 // İlk kullanım sonrası hak kalmaz
            };
        } else {
            // Mevcut kayıt var - limit kontrolü
            const currentUsage = existingRecord.usage_count || 0;
            console.log(`🔒 SECURITY: Existing record found - usage: ${currentUsage}, limit: ${maxTrialUsage}`);

            if (currentUsage >= maxTrialUsage) {
                console.log('🚫 SECURITY: Trial limit already exceeded');
                return {
                    allowed: false,
                    reason: 'trial_limit_exceeded',
                    usage_count: currentUsage,
                    max_usage: maxTrialUsage,
                    remaining: 0
                };
            }

            // Limit aşılmamış - sayacı artır
            const newUsageCount = currentUsage + 1;
            console.log(`🔒 SECURITY: Incrementing usage count: ${currentUsage} -> ${newUsageCount}`);

            const { error: updateError } = await supabase
                .from('trial_usage')
                .update({
                    usage_count: newUsageCount,
                    last_used_at: new Date().toISOString(),
                    user_agent: userAgent || existingRecord.user_agent
                })
                .eq('ip_address', clientIP);

            if (updateError) {
                console.error('🚫 SECURITY: Failed to update trial record:', updateError);
                throw updateError;
            }

            const isAllowed = newUsageCount <= maxTrialUsage;
            console.log(`🔒 SECURITY: Usage updated - allowed: ${isAllowed}`);

            return {
                allowed: isAllowed,
                reason: isAllowed ? 'trial_usage_recorded' : 'trial_limit_exceeded',
                usage_count: newUsageCount,
                max_usage: maxTrialUsage,
                remaining: Math.max(0, maxTrialUsage - newUsageCount)
            };
        }

    } catch (error) {
        console.error('🚫 SECURITY: Atomic trial check/record error:', error);
        throw error; // Hata durumunda üst seviyeye fırlat
    }
}

// 🛡️ GÜVENLİK: Trial limit fallback kontrolü (RPC fonksiyonu çalışmadığında) - ARTIK KULLANILMIYOR
async function checkTrialLimitFallback(clientIP) {
    try {
        // Basit IP tabanlı kontrol - bir kez kullanıldı mı?
        console.log('Checking trial limit for IP:', clientIP);

        const { data: usageRecord, error } = await supabase
            .from('trial_usage')
            .select('*')
            .eq('ip_address', clientIP)
            .single();

        console.log('Trial usage query result:', { usageRecord, error });

        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
            console.error('Fallback trial check error:', error);
            console.error('Error details:', JSON.stringify(error, null, 2));

            // Eğer kayıt yoksa (ilk kullanım), izin ver
            if (error.code === 'PGRST116' || error.message?.includes('No rows found')) {
                console.log('No existing trial record found - allowing first use');
                return { allowed: true, reason: 'first_use' };
            }

            // Diğer veritabanı hataları için de ilk kullanım kabul et
            console.log('Database error - allowing as first use');
            return { allowed: true, reason: 'db_error_allow_first_use' };
        }

        const maxTrialUsage = 1; // Sadece 1 deneme hakkı

        // Kayıt yoksa ilk kullanım - izin ver
        if (!usageRecord) {
            console.log('No usage record found - allowing first use');
            return {
                allowed: true,
                reason: 'first_use',
                usage_count: 0,
                max_usage: maxTrialUsage
            };
        }

        const usageCount = usageRecord.usage_count || 0;
        console.log('Usage count found:', usageCount);

        return {
            allowed: usageCount < maxTrialUsage,
            reason: usageCount >= maxTrialUsage ? 'trial_limit_exceeded' : 'trial_allowed',
            usage_count: usageCount,
            max_usage: maxTrialUsage
        };

    } catch (error) {
        console.error('Fallback trial check general error:', error);
        // İlk kullanım olabilir - izin ver ama logla
        console.log('General error in trial check - allowing as potential first use');
        return {
            allowed: true,
            reason: 'general_error_allow_first_use',
            error_details: error.message
        };
    }
}

// 🛡️ GÜVENLİK: Trial kullanım kayıt fallback (RPC fonksiyonu çalışmadığında)
async function recordTrialUsageFallback(clientIP, userAgent) {
    try {
        console.log('🔒 SECURITY: Recording trial usage for IP:', clientIP);

        // Önce mevcut kaydı kontrol et
        const { data: existingRecord, error: selectError } = await supabase
            .from('trial_usage')
            .select('*')
            .eq('ip_address', clientIP)
            .single();

        if (selectError && selectError.code !== 'PGRST116') {
            console.error('Error checking existing trial record:', selectError);
            throw selectError;
        }

        if (!existingRecord) {
            // Yeni kayıt oluştur
            console.log('🔒 SECURITY: Creating new trial usage record');
            const { error: insertError } = await supabase
                .from('trial_usage')
                .insert([{
                    ip_address: clientIP,
                    user_agent: userAgent || null,
                    fingerprint: null,
                    usage_count: 1,
                    first_used_at: new Date().toISOString(),
                    last_used_at: new Date().toISOString(),
                    created_at: new Date().toISOString()
                }]);

            if (insertError) {
                console.error('🚫 SECURITY: Failed to create trial record:', insertError);
                throw insertError;
            }

            console.log('✅ SECURITY: Trial usage recorded (new record)');
        } else {
            // Mevcut kaydı güncelle
            console.log('🔒 SECURITY: Updating existing trial usage record');
            const newUsageCount = (existingRecord.usage_count || 0) + 1;

            const { error: updateError } = await supabase
                .from('trial_usage')
                .update({
                    usage_count: newUsageCount,
                    last_used_at: new Date().toISOString(),
                    user_agent: userAgent || existingRecord.user_agent
                })
                .eq('ip_address', clientIP);

            if (updateError) {
                console.error('🚫 SECURITY: Failed to update trial record:', updateError);
                throw updateError;
            }

            console.log(`✅ SECURITY: Trial usage updated (${existingRecord.usage_count} -> ${newUsageCount})`);
        }

    } catch (error) {
        console.error('🚫 SECURITY: Fallback trial record general error:', error);
        // Trial kayıt hatası kritik - özet oluşturmayı engellemez ama loglanır
    }
}

// Bu fonksiyon artık kullanılmıyor - yeni failover sistemi api-key-manager.js'de

// 🛡️ GÜVENLİK: Günlük limit kontrolü (authenticated kullanıcılar için)
async function checkDailyLimit(userId) {
    try {
        console.log('🔒 SECURITY: Checking daily limit for user:', userId);

        // Kullanıcının güncel bilgilerini al
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('daily_summary_limit, daily_summary_count, status')
            .eq('id', userId)
            .single();

        if (userError || !user) {
            console.error('🚫 SECURITY: User not found for daily limit check:', userError);
            return false;
        }

        // Status kontrolü (ekstra güvenlik)
        if (user.status !== 'approved') {
            console.log('🚫 SECURITY: User not approved for daily limit check');
            return false;
        }

        const limit = user.daily_summary_limit || 5;
        const currentCount = user.daily_summary_count || 0;

        console.log(`🔒 SECURITY: Daily limit check - User: ${userId}, Used: ${currentCount}, Limit: ${limit}`);

        return currentCount < limit;

    } catch (error) {
        console.error('🚫 SECURITY: Daily limit check error:', error);
        return false; // Hata durumunda güvenli taraf - izin verme
    }
}

// Fallback fonksiyonu - RPC increment_summary_count yoksa kullanılır
async function incrementSummaryCountFallback(userId) {
    try {
        console.log('Using fallback increment for user:', userId);

        // Kullanıcının güncel bilgilerini al
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('daily_summary_limit, daily_summary_count, last_summary_date')
            .eq('id', userId)
            .single();

        if (userError || !user) {
            console.error('User not found for increment fallback:', userError);
            return false;
        }

        const limit = user.daily_summary_limit || 5;
        const currentCount = user.daily_summary_count || 0;

        // Otomatik günlük sıfırlama kaldırıldı - sadece webhook ile sıfırlama

        // Limit kontrolü
        if (currentCount >= limit) {
            console.log('Daily limit exceeded for user:', userId);
            return false;
        }

        // Sayacı artır (tarih güncelleme kaldırıldı)
        const newCount = currentCount + 1;
        const { error: updateError } = await supabase
            .from('users')
            .update({
                daily_summary_count: newCount
            })
            .eq('id', userId);

        if (updateError) {
            console.error('Failed to update summary count:', updateError);
            return false;
        }

        console.log(`Summary count updated: ${currentCount} -> ${newCount} for user ${userId}`);
        return true;

    } catch (error) {
        console.error('Increment fallback error:', error);
        return false;
    }
}

// Belge türü tespit fonksiyonu
function detectDocumentType(text) {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('dava dilekçesi') || lowerText.includes('davacı')) {
        return 'dava_dilekçesi';
    } else if (lowerText.includes('cevap dilekçesi') || lowerText.includes('davalı')) {
        return 'cevap_dilekçesi';
    } else if (lowerText.includes('bilirkişi') || lowerText.includes('rapor')) {
        return 'bilirkişi_raporu';
    } else if (lowerText.includes('talep dilekçesi') || lowerText.includes('talep')) {
        return 'talep_dilekçesi';
    } else {
        return 'diğer';
    }
}
