// Basit Admin Authentication Function
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Supabase client
const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        const body = JSON.parse(event.body || '{}');
        const { email, password } = body;

        if (!email || !password) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'E-posta ve şifre gereklidir.' })
            };
        }

        // Admin kullanıcısını Supabase'den getir
        console.log('Login attempt:', { email: email.toLowerCase() });

        const { data: admin, error: fetchError } = await supabase
            .from('admins')
            .select('*')
            .eq('email', email.toLowerCase())
            .eq('is_active', true)
            .single();

        console.log('Supabase result:', { admin: !!admin, error: fetchError });

        if (fetchError || !admin) {
            console.log('User not found or error:', fetchError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({
                    error: 'Geçersiz e-posta veya şifre.',
                    debug: `User found: ${!!admin}, Error: ${fetchError?.message || 'none'}`
                })
            };
        }

        // Hesap kilitli mi kontrol et
        if (admin.locked_until && new Date() < new Date(admin.locked_until)) {
            return {
                statusCode: 423,
                headers,
                body: JSON.stringify({
                    error: 'Hesap geçici olarak kilitlenmiştir. Lütfen daha sonra tekrar deneyin.'
                })
            };
        }

        // Şifre kontrolü
        console.log('Password check:', {
            inputPassword: password,
            storedHash: admin.password_hash.substring(0, 20) + '...',
            hashLength: admin.password_hash.length
        });

        const passwordMatch = await bcrypt.compare(password, admin.password_hash);
        console.log('Password match result:', passwordMatch);

        if (!passwordMatch) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({
                    error: 'Geçersiz e-posta veya şifre.'
                })
            };
        }

        // Başarılı giriş - kilidi temizle
        await supabase
            .from('admins')
            .update({
                failed_login_attempts: 0,
                locked_until: null,
                last_login: new Date().toISOString()
            })
            .eq('id', admin.id);

        // JWT token oluştur
        const jwtSecret = process.env.JWT_SECRET;
        const sessionDuration = parseInt(process.env.ADMIN_SESSION_DURATION) || 86400000;

        const tokenPayload = {
            adminId: admin.id,
            email: admin.email,
            role: admin.role,
            fullName: admin.full_name,
            exp: Math.floor((Date.now() + sessionDuration) / 1000)
        };

        const token = jwt.sign(tokenPayload, jwtSecret);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                token: token,
                admin: {
                    id: admin.id,
                    email: admin.email,
                    fullName: admin.full_name,
                    role: admin.role
                }
            })
        };

    } catch (error) {
        console.error('Admin auth error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası oluştu.',
                details: error.message
            })
        };
    }
};
