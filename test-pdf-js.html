<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #file-input {
            margin: 10px 0;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>PDF.js Test Sayfası</h1>
    
    <div id="status-container">
        <div class="status info">Test başlatılıyor...</div>
    </div>

    <h2>PDF.js Yükleme Testi</h2>
    <button onclick="testPDFJS()">PDF.js Durumunu Kontrol Et</button>
    
    <h2>PDF Dosyası Test</h2>
    <input type="file" id="file-input" accept=".pdf">
    <button onclick="testPDFFile()" id="test-btn" disabled>PDF'i Test Et</button>
    
    <div id="result"></div>

    <!-- PDF.js kütüphanesi -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" crossorigin="anonymous"></script>
    
    <script>
        let statusContainer = document.getElementById('status-container');
        let fileInput = document.getElementById('file-input');
        let testBtn = document.getElementById('test-btn');
        let result = document.getElementById('result');

        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            statusContainer.appendChild(div);
            console.log(message);
        }

        function testPDFJS() {
            addStatus('PDF.js test başlatılıyor...');
            
            if (typeof pdfjsLib === 'undefined') {
                addStatus('❌ PDF.js kütüphanesi yüklenmemiş!', 'error');
                return false;
            }
            
            addStatus('✅ PDF.js kütüphanesi yüklü', 'success');
            
            try {
                // Worker'ı devre dışı bırak
                pdfjsLib.GlobalWorkerOptions.workerSrc = false;
                addStatus('✅ PDF.js worker devre dışı bırakıldı', 'success');
                
                // Version bilgisi
                if (pdfjsLib.version) {
                    addStatus(`📋 PDF.js version: ${pdfjsLib.version}`, 'info');
                }
                
                return true;
            } catch (error) {
                addStatus(`❌ PDF.js konfigürasyon hatası: ${error.message}`, 'error');
                return false;
            }
        }

        async function testPDFFile() {
            const file = fileInput.files[0];
            if (!file) {
                addStatus('❌ Lütfen bir PDF dosyası seçin', 'error');
                return;
            }

            if (!testPDFJS()) {
                return;
            }

            addStatus(`📄 PDF dosyası test ediliyor: ${file.name} (${formatFileSize(file.size)})`);
            
            try {
                const text = await extractTextFromPDF(file);
                addStatus(`✅ PDF başarıyla işlendi! ${text.length} karakter çıkarıldı`, 'success');
                result.textContent = text.substring(0, 1000) + (text.length > 1000 ? '\n\n... (ilk 1000 karakter gösteriliyor)' : '');
            } catch (error) {
                addStatus(`❌ PDF işleme hatası: ${error.message}`, 'error');
                result.textContent = `Hata: ${error.message}`;
            }
        }

        async function extractTextFromPDF(file) {
            return new Promise((resolve, reject) => {
                const fileReader = new FileReader();

                fileReader.onload = async function() {
                    try {
                        const typedarray = new Uint8Array(this.result);

                        const loadingTask = pdfjsLib.getDocument({
                            data: typedarray,
                            disableWorker: true,
                            useWorkerFetch: false,
                            isEvalSupported: false,
                            useSystemFonts: true,
                            cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
                            cMapPacked: true
                        });

                        const pdf = await loadingTask.promise;
                        let fullText = '';

                        for (let i = 1; i <= pdf.numPages; i++) {
                            const page = await pdf.getPage(i);
                            const textContent = await page.getTextContent();
                            const pageText = textContent.items.map(item => item.str).join(' ');
                            fullText += pageText + '\n';
                        }

                        resolve(fullText);
                    } catch (error) {
                        reject(error);
                    }
                };
                
                fileReader.onerror = () => {
                    reject(new Error('Dosya okunamadı'));
                };
                
                fileReader.readAsArrayBuffer(file);
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Dosya seçildiğinde test butonunu aktif et
        fileInput.addEventListener('change', function() {
            testBtn.disabled = !this.files[0];
        });

        // Sayfa yüklendiğinde otomatik test
        window.addEventListener('load', function() {
            setTimeout(() => {
                addStatus('Sayfa yüklendi, PDF.js test ediliyor...');
                testPDFJS();
            }, 100);
        });

        // DOMContentLoaded'da da test
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addStatus('DOM yüklendi, PDF.js test ediliyor...');
                testPDFJS();
            }, 100);
        });
    </script>
</body>
</html>
