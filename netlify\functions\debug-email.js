const { createClient } = require('@supabase/supabase-js');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        const testEmail = event.queryStringParameters?.email || '<EMAIL>';
        const results = {
            timestamp: new Date().toISOString(),
            testEmail: testEmail,
            tests: []
        };

        // Test 1: Environment Variables Check
        results.tests.push({
            name: 'Environment Variables',
            status: 'PASS',
            details: {
                RESEND_API_KEY: !!process.env.RESEND_API_KEY,
                SITE_URL: !!process.env.SITE_URL,
                SUPABASE_URL: !!process.env.SUPABASE_URL,
                resendKeyLength: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.length : 0
            }
        });

        // Test 2: Resend Email Test
        if (process.env.RESEND_API_KEY) {
            try {
                console.log('Testing Resend with API key length:', process.env.RESEND_API_KEY.length);

                const { Resend } = require('resend');
                const resend = new Resend(process.env.RESEND_API_KEY);

                // Production modu: Verified domain kullan
                const testMode = !process.env.RESEND_DOMAIN_VERIFIED;
                const targetEmail = testMode ? '<EMAIL>' : testEmail;

                const { data: resendData, error: resendError } = await resend.emails.send({
                    from: 'LegalAI <<EMAIL>>', // Verified domain
                    to: [targetEmail],
                    subject: `Test Email - Debug ${testMode ? '(Test Mode)' : ''}`,
                    html: `<h1>Test Email</h1><p>This is a test email from debug function.</p>${testMode ? `<p><strong>Original email:</strong> ${testEmail}</p>` : ''}`
                });

                results.tests.push({
                    name: 'Resend Email',
                    status: resendError ? 'FAIL' : 'PASS',
                    details: {
                        error: resendError?.message,
                        success: !!resendData?.id,
                        emailId: resendData?.id
                    }
                });

            } catch (error) {
                results.tests.push({
                    name: 'Resend Email',
                    status: 'FAIL',
                    details: { error: error.message }
                });
            }
        } else {
            results.tests.push({
                name: 'Resend Email',
                status: 'SKIP',
                details: { reason: 'RESEND_API_KEY not configured' }
            });
        }

        // Test 3: Email verification logs kontrol
        try {
            const { data: logs, error: logsError } = await supabase
                .from('email_verification_logs')
                .select('*')
                .eq('email', testEmail)
                .order('created_at', { ascending: false })
                .limit(5);

            results.tests.push({
                name: 'Email Verification Logs',
                status: logsError ? 'FAIL' : 'PASS',
                details: {
                    error: logsError?.message,
                    logCount: logs?.length || 0,
                    recentLogs: logs || []
                }
            });

        } catch (error) {
            results.tests.push({
                name: 'Email Verification Logs',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Test 4: Supabase Auth users kontrol
        try {
            const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
            
            const testUsers = users?.users?.filter(u => u.email?.includes('test')) || [];

            results.tests.push({
                name: 'Auth Users Check',
                status: usersError ? 'FAIL' : 'PASS',
                details: {
                    error: usersError?.message,
                    totalUsers: users?.users?.length || 0,
                    testUsers: testUsers.length,
                    testUserEmails: testUsers.map(u => u.email)
                }
            });

        } catch (error) {
            results.tests.push({
                name: 'Auth Users Check',
                status: 'FAIL',
                details: { error: error.message }
            });
        }

        // Overall status
        const failedTests = results.tests.filter(test => test.status === 'FAIL');
        const overallStatus = failedTests.length === 0 ? 'ALL_PASS' : 'SOME_FAIL';

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                overallStatus: overallStatus,
                summary: {
                    total: results.tests.length,
                    passed: results.tests.filter(test => test.status === 'PASS').length,
                    failed: failedTests.length,
                    skipped: results.tests.filter(test => test.status === 'SKIP').length
                },
                results: results,
                recommendations: getDebugRecommendations(results.tests)
            })
        };

    } catch (error) {
        console.error('Debug email error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Debug failed',
                details: error.message
            })
        };
    }
};

function getDebugRecommendations(tests) {
    const recommendations = [];
    
    const supabaseTest = tests.find(t => t.name === 'Supabase Auth Invite');
    const resendTest = tests.find(t => t.name === 'Resend Email');
    
    if (supabaseTest?.status === 'PASS') {
        recommendations.push('✅ Supabase Auth invite working - check spam folder');
        recommendations.push('📧 Check your email (including spam) for Supabase invite');
    } else if (supabaseTest?.status === 'FAIL') {
        recommendations.push('❌ Supabase Auth invite failed - check SMTP settings');
        recommendations.push('🔧 Configure custom SMTP in Supabase Dashboard');
    }
    
    if (resendTest?.status === 'PASS') {
        recommendations.push('✅ Resend working as fallback');
    } else if (resendTest?.status === 'FAIL') {
        recommendations.push('❌ Resend failed - check API key');
    } else if (resendTest?.status === 'SKIP') {
        recommendations.push('⚠️ Resend not configured - add RESEND_API_KEY for fallback');
    }
    
    if (recommendations.length === 0) {
        recommendations.push('🔍 All systems operational - check email delivery');
    }
    
    return recommendations;
}
