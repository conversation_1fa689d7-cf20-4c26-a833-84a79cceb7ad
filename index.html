<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="google-site-verification" content="_8lpKBqYPT6WhMrxeXMmBgVyV3OH_g-w_n4sd7jAnFs" />

    <!-- SEO Meta Tags -->
    <title>Hukuki Belge Özetleme Sistemi | AI Destekli Dava Dilekçesi Analizi</title>
    <meta name="description" content="Yapay zeka destekli hukuki belge özetleme platformu. Dava dilekçesi, cevap dilek<PERSON>esi, bilirkişi raporu özetleme. Otomatik tür tespiti ile anında profesyonel analiz.">
    <meta name="keywords" content="hukuki belge özetleme, dava dilekçesi analizi, ya<PERSON><PERSON> zeka hukuk, AI hukuki analiz, bilirkişi raporu özetleme, ceva<PERSON> dile<PERSON>, huku<PERSON> AI, a<PERSON><PERSON> yard<PERSON>, hukuk teknolojisi">
    <meta name="author" content="Hukuki Belge Özetleme Sistemi">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="language" content="Turkish">
    <meta name="geo.region" content="TR">
    <meta name="geo.country" content="Turkey">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Hukuki Belge Özetleme Sistemi | AI Destekli Dava Dilekçesi Analizi">
    <meta property="og:description" content="Yapay zeka destekli hukuki belge özetleme platformu. Dava dilekçesi, cevap dilekçesi, bilirkişi raporu özetleme. Otomatik tür tespiti ile anında profesyonel analiz.">
    <meta property="og:url" content="https://hukukibelgeozetleme.netlify.app">
    <meta property="og:site_name" content="LegalAI - Hukuki Belge Özetleme">
    <meta property="og:locale" content="tr_TR">
    <meta property="og:image" content="https://hukukibelgeozetleme.netlify.app/assets/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Hukuki Belge Özetleme Sistemi - AI Destekli Hukuki Analiz">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Hukuki Belge Özetleme Sistemi | AI Destekli Dava Dilekçesi Analizi">
    <meta name="twitter:description" content="Yapay zeka destekli hukuki belge özetleme platformu. Dava dilekçesi, cevap dilekçesi, bilirkişi raporu özetleme.">
    <meta name="twitter:image" content="https://hukukibelgeozetleme.netlify.app/assets/twitter-card.jpg">
    <meta name="twitter:image:alt" content="Hukuki Belge Özetleme Sistemi">

    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#2563eb">
    <meta name="msapplication-TileColor" content="#2563eb">
    <link rel="canonical" href="https://hukukibelgeozetleme.netlify.app">
    <link rel="alternate" hreflang="tr" href="https://hukukibelgeozetleme.netlify.app">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <!-- Critical CSS Preload -->
    <link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="styles.css"></noscript>

    <!-- Font Preloads -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"></noscript>

    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Critical CSS -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="whatsapp-contact.css">

    <!-- Non-critical CSS -->
    <link rel="preload" href="mobile-menu.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="mobile-menu.css"></noscript>

    <!-- Material Icons - Lazy Load -->
    <link rel="preload" href="https://fonts.googleapis.com/icon?family=Material+Icons" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons"></noscript>

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Hukuki Belge Özetleme Sistemi",
        "alternateName": "LegalAI",
        "description": "Yapay zeka destekli hukuki belge özetleme platformu. Dava dilekçesi, cevap dilekçesi, bilirkişi raporu özetleme. Otomatik tür tespiti ile anında profesyonel analiz.",
        "url": "https://hukukibelgeozetleme.netlify.app",
        "applicationCategory": "LegalTech",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "TRY",
            "availability": "https://schema.org/InStock"
        },
        "provider": {
            "@type": "Organization",
            "name": "LegalAI",
            "url": "https://hukukibelgeozetleme.netlify.app"
        },
        "featureList": [
            "Otomatik dilekçe türü tespiti",
            "Dava dilekçesi özetleme",
            "Cevap dilekçesi analizi",
            "Bilirkişi raporu özetleme",
            "AI destekli hukuki analiz",
            "PDF belge işleme",
            "Metin analizi"
        ],
        "inLanguage": "tr",
        "audience": {
            "@type": "Audience",
            "audienceType": "Lawyers, Legal Professionals, Law Students"
        }
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "LegalAI - Hukuki Belge Özetleme Sistemi",
        "url": "https://hukukibelgeozetleme.netlify.app",
        "logo": "https://hukukibelgeozetleme.netlify.app/assets/logo.png",
        "description": "Türkiye'nin önde gelen yapay zeka destekli hukuki belge özetleme platformu",
        "areaServed": {
            "@type": "Country",
            "name": "Turkey"
        },
        "serviceType": "Legal Technology Services",
        "knowsAbout": [
            "Hukuki Belge Analizi",
            "Dava Dilekçesi Özetleme",
            "Yapay Zeka Hukuk Uygulamaları",
            "Legal Tech",
            "Hukuki Doküman İşleme"
        ]
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "Hukuki Belge Özetleme Hizmeti",
        "description": "AI destekli otomatik hukuki belge analizi ve özetleme hizmeti",
        "provider": {
            "@type": "Organization",
            "name": "LegalAI"
        },
        "areaServed": {
            "@type": "Country",
            "name": "Turkey"
        },
        "serviceType": "Document Analysis",
        "category": "Legal Technology",
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Hukuki Belge Özetleme Hizmetleri",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Dava Dilekçesi Özetleme"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Cevap Dilekçesi Analizi"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Bilirkişi Raporu Özetleme"
                    }
                }
            ]
        }
    }
    </script>
    <script>
        // PDF.js dinamik yükleme
        function loadPDFJS() {
            return new Promise((resolve, reject) => {
                if (typeof pdfjsLib !== 'undefined') {
                    resolve(true);
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
                script.crossOrigin = 'anonymous';

                script.onload = function() {
                    console.log('PDF.js script loaded');
                    if (typeof pdfjsLib !== 'undefined') {
                        pdfjsLib.GlobalWorkerOptions.workerSrc = false;
                        console.log('PDF.js configured to run without worker for CSP compliance');
                        window.pdfJSLoaded = true;
                        resolve(true);
                    } else {
                        console.error('PDF.js loaded but pdfjsLib not available');
                        reject(new Error('PDF.js library not available'));
                    }
                };

                script.onerror = function() {
                    console.error('Failed to load PDF.js script');
                    reject(new Error('Failed to load PDF.js'));
                };

                document.head.appendChild(script);
            });
        }

        // Sayfa yüklendiğinde PDF.js'i yükle
        window.addEventListener('load', function() {
            loadPDFJS().catch(error => {
                console.error('PDF.js loading failed:', error);
            });
        });
    </script>
</head>
<body class="bg-gradient">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-balance-scale nav-icon"></i>
                <span class="nav-title">LegalAI</span>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link active">Ana Sayfa</a>
                <button class="nav-btn" id="user-menu-btn" style="display: none;">
                    <i class="fas fa-user"></i>
                    <span id="user-name"></span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <a href="/apply" class="nav-link" id="register-link">Üye Ol</a>
                <a href="/login" class="nav-link" id="login-link">Giriş Yap</a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle" type="button" aria-label="Menüyü aç/kapat">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobile-menu">
            <div class="mobile-menu-header">
                <div class="mobile-brand">
                    <i class="fas fa-balance-scale"></i>
                    <span>LegalAI</span>
                </div>
                <button class="mobile-menu-close" id="mobile-menu-close" type="button" aria-label="Menüyü kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mobile-menu-content">
                <div class="mobile-nav-section">
                    <a href="/" class="mobile-nav-link">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </div>

                <!-- Mobile User Menu (when logged in) -->
                <div class="mobile-nav-section" id="mobile-user-section" style="display: none;">
                    <div class="mobile-user-info">
                        <div class="mobile-user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="mobile-user-details">
                            <span class="mobile-user-name" id="mobile-user-name">Kullanıcı</span>
                            <span class="mobile-user-status">Aktif Üye</span>
                        </div>
                    </div>

                    <a href="/profile" class="mobile-nav-link">
                        <i class="fas fa-user-circle"></i>
                        <span>Profil</span>
                    </a>

                    <button class="mobile-nav-link logout-btn" id="mobile-logout-btn" type="button">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Çıkış Yap</span>
                    </button>
                </div>

                <!-- Mobile Auth Buttons (when not logged in) -->
                <div class="mobile-nav-section" id="mobile-auth-section">
                    <div class="mobile-auth-buttons">
                        <a href="/apply" class="mobile-auth-btn primary">
                            <i class="fas fa-user-plus"></i>
                            <span>Üye Ol</span>
                        </a>
                        <a href="/login" class="mobile-auth-btn secondary">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Giriş Yap</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </nav>

    <!-- Breadcrumb Navigation -->
    <nav aria-label="Breadcrumb" class="breadcrumb-nav">
        <div class="container">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/" aria-label="Ana Sayfa">
                        <i class="fas fa-home" aria-hidden="true"></i>
                        Ana Sayfa
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    Hukuki Belge Özetleme
                </li>
            </ol>
        </div>
    </nav>

    <!-- Hero Section -->
    <main>
        <section class="hero" role="banner">
            <div class="hero-container">
                <header class="hero-content">
                    <h1 class="hero-title">
                        Hukuki Belge Özetleme
                        <span class="hero-highlight">Sistemi</span>
                    </h1>
                    <p class="hero-subtitle">
                        Yapay zeka destekli profesyonel hukuki belge analizi ve özetleme platformu.
                        Otomatik dilekçe türü tespiti ile dava dilekçeleri, cevap dilekçeleri ve bilirkişi raporlarını anında özetleyin.
                    </p>
                <div class="hero-features" role="list">
                    <div class="feature-badge" role="listitem">
                        <i class="fas fa-robot" aria-hidden="true"></i>
                        <span>Otomatik Tür Tespiti</span>
                    </div>
                    <div class="feature-badge" role="listitem">
                        <i class="fas fa-language" aria-hidden="true"></i>
                        <span>Hukuki Terminoloji</span>
                    </div>
                    <div class="feature-badge" role="listitem">
                        <i class="fas fa-clock" aria-hidden="true"></i>
                        <span>Anında Sonuç</span>
                    </div>
                </div>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">5+</div>
                        <div class="stat-label">Dilekçe Türü</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">99%</div>
                        <div class="stat-label">Doğruluk Oranı</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Hizmet</div>
                    </div>
                </header>
            </div>
            <aside class="hero-visual">
                <article class="hero-card">
                    <i class="fas fa-brain hero-card-icon" aria-hidden="true"></i>
                    <h2>Akıllı Analiz</h2>
                    <p>Gelişmiş AI teknolojisi ile hukuki belgeleri otomatik olarak tanır ve özetler</p>
                </article>
            </aside>
        </div>
    </section>

    <!-- Trial Limit Message -->
    <section class="trial-limit" id="trial-limit" style="display: none;">
        <div class="container">
            <div class="trial-card">
                <div class="trial-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <h2>Deneme Süresi Doldu</h2>
                <p>Sistemi test ettiniz! Daha fazla özet oluşturmak için üye olmanız gerekmektedir.</p>
                <div class="trial-benefits">
                    <div class="benefit-item">
                        <i class="fas fa-infinity"></i>
                        <span>Sınırsız özet oluşturma</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-history"></i>
                        <span>Özet geçmişi</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-shield-check"></i>
                        <span>Güvenli hesap</span>
                    </div>
                </div>
                <div class="trial-buttons">
                    <a href="/apply" class="trial-btn primary">
                        <i class="fas fa-user-plus"></i>
                        Üye Ol
                    </a>
                    <a href="/login" class="trial-btn secondary">
                        <i class="fas fa-sign-in-alt"></i>
                        Giriş Yap
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content - Özetleme Bölümü -->
    <section class="main-section" id="main-content" role="main">
        <div class="container">
            <header class="section-header">
                <h2 class="section-title">Hukuki Belge Özetleme</h2>
                <p class="section-subtitle">PDF yükleyin veya metin girin, anında profesyonel özet alın</p>
            </header>

            <!-- Tabs -->
            <div class="tabs-container">
                <div class="tabs-header">
                    <button class="tab-btn active" data-tab="pdf">
                        <i class="fas fa-file-pdf tab-icon"></i>
                        <span>PDF Özetleme</span>
                    </button>
                    <button class="tab-btn" data-tab="text">
                        <i class="fas fa-edit tab-icon"></i>
                        <span>Metin Özetleme</span>
                    </button>
                </div>

                <!-- PDF Özetleme Sekmesi -->
                <div class="tab-content active" id="pdf-tab">
                    <div class="upload-section">
                        <div class="upload-area" id="pdf-upload-area" role="button" tabindex="0" aria-label="PDF dosyası yüklemek için tıklayın veya dosyayı buraya sürükleyin">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt" aria-hidden="true"></i>
                            </div>
                            <h3>PDF Dosyası Yükleyin</h3>
                            <p>Dosyayı buraya sürükleyip bırakın veya seçmek için tıklayın</p>
                            <p class="file-info">Maksimum dosya boyutu: 10MB</p>
                            <input type="file" id="pdf-file-input" accept=".pdf" hidden aria-label="PDF dosyası seçin">
                        </div>

                        <div class="file-details" id="pdf-file-details" style="display: none;">
                            <div class="file-info-card">
                                <div class="file-icon">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="file-meta">
                                    <div class="file-name" id="pdf-file-name"></div>
                                    <div class="file-size" id="pdf-file-size"></div>
                                </div>
                                <button class="remove-file" id="pdf-remove-file">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <button class="submit-btn" id="pdf-submit-btn" disabled>
                        <span class="btn-text">
                            <i class="fas fa-magic"></i>
                            Özet Oluştur
                        </span>
                        <div class="loading-spinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                </div>

                <!-- Metin Özetleme Sekmesi -->
                <div class="tab-content" id="text-tab">
                    <div class="text-input-section">
                        <label for="text-input" class="input-label">
                            <i class="fas fa-edit"></i>
                            Özetlenecek Metni Girin:
                        </label>
                        <textarea
                            id="text-input"
                            class="text-input"
                            placeholder="Dava dilekçesi, cevap dilekçesi, bilirkişi raporu veya diğer hukuki belge metnini buraya yapıştırın...&#10;&#10;Minimum 50 karakter gereklidir."
                            rows="12"
                        ></textarea>
                        <div class="char-counter">
                            <span id="char-count" class="char-count">0 / 25.000 karakter</span>
                        </div>
                    </div>

                    <button class="submit-btn" id="text-submit-btn" disabled>
                        <span class="btn-text">
                            <i class="fas fa-magic"></i>
                            Özet Oluştur
                        </span>
                        <div class="loading-spinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>
                </div>

                <!-- Sonuç Alanı -->
                <div class="result-section" id="result-section" style="display: none;">
                    <div class="result-header">
                        <h3>
                            <i class="fas fa-file-alt"></i>
                            Özet Sonucu
                        </h3>
                        <button class="copy-btn" id="copy-result-btn">
                            <i class="fas fa-copy"></i>
                            <span>Kopyala</span>
                        </button>
                    </div>
                    <div class="result-content" id="result-content"></div>
                </div>

                <!-- Hata Mesajı -->
                <div class="error-message" id="error-message" style="display: none;">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-text" id="error-text"></div>
                </div>

                <!-- Başarı Mesajı -->
                <div class="success-message" id="success-message" style="display: none;">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="success-text" id="success-text"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Document Types Section - Minimal -->
    <section class="document-types-minimal" role="complementary">
        <div class="container">
            <header class="section-header-minimal">
                <h2 class="section-title-small">Desteklenen Belge Türleri</h2>
            </header>
            <ul class="document-types-list" role="list">
                <li class="document-type-item">
                    <i class="fas fa-gavel" aria-hidden="true"></i>
                    <span>Dava Dilekçesi</span>
                </li>
                <li class="document-type-item">
                    <i class="fas fa-reply" aria-hidden="true"></i>
                    <span>Cevap Dilekçesi</span>
                </li>
                <li class="document-type-item">
                    <i class="fas fa-file-medical" aria-hidden="true"></i>
                    <span>Bilirkişi Raporu</span>
                </li>
                <li class="document-type-item">
                    <i class="fas fa-hand-paper" aria-hidden="true"></i>
                    <span>Talep Dilekçesi</span>
                </li>
                <li class="document-type-item">
                    <i class="fas fa-file-alt" aria-hidden="true"></i>
                    <span>Diğer Belgeler</span>
                </li>
            </ul>
        </div>
    </section>

    <!-- Features Section - Minimal -->
    <section class="features-minimal" role="complementary">
        <div class="container">
            <header class="section-header-minimal">
                <h2 class="section-title-small">Sistem Özellikleri</h2>
            </header>
            <ul class="features-list" role="list">
                <li class="feature-minimal">
                    <i class="fas fa-robot" aria-hidden="true"></i>
                    <span>Otomatik Tür Tespiti</span>
                </li>
                <li class="feature-minimal">
                    <i class="fas fa-balance-scale" aria-hidden="true"></i>
                    <span>Hukuki Terminoloji</span>
                </li>
                <li class="feature-minimal">
                    <i class="fas fa-shield-alt" aria-hidden="true"></i>
                    <span>Güvenli İşlem</span>
                </li>
                <li class="feature-minimal">
                    <i class="fas fa-bolt" aria-hidden="true"></i>
                    <span>Hızlı Sonuç</span>
                </li>
            </ul>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>LegalAI</h3>
                    <p>Türkiye'nin önde gelen yapay zeka destekli hukuki belge özetleme platformu</p>
                    <div class="footer-location">
                        <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                        <span>Türkiye</span>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Hizmetlerimiz</h4>
                    <ul>
                        <li>Dava Dilekçesi Özetleme</li>
                        <li>Cevap Dilekçesi Analizi</li>
                        <li>Bilirkişi Raporu Özetleme</li>
                        <li>Hukuki Belge Analizi</li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Destek</h4>
                    <ul>
                        <li><a href="/support">Destek Merkezi</a></li>
                        <li><a href="/support-tickets">Destek Biletleri</a></li>
                        <li><a href="mailto:<EMAIL>">E-posta Desteği</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Yasal</h4>
                    <ul>
                        <li><a href="/privacy">Gizlilik Politikası</a></li>
                        <li><a href="/terms">Kullanım Şartları</a></li>
                        <li><a href="/cookies">Çerez Politikası</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Hukuki Belge Özetleme Sistemi. Tüm hakları saklıdır.</p>
                <p class="footer-location-text">Türkiye merkezli hukuki teknoloji çözümleri</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Floating Button -->
    <div class="whatsapp-float" title="WhatsApp ile İletişim" onclick="openWhatsAppModal()">
        <i class="fab fa-whatsapp"></i>
    </div>

    <!-- Lazy load non-critical JavaScript -->
    <script>
        // Load scripts after page load for better performance
        window.addEventListener('load', function() {
            // Load main script
            const mainScript = document.createElement('script');
            mainScript.src = 'script.js';
            mainScript.defer = true;
            document.body.appendChild(mainScript);

            // Load mobile menu script
            const mobileScript = document.createElement('script');
            mobileScript.src = 'mobile-menu.js';
            mobileScript.defer = true;
            document.body.appendChild(mobileScript);

            // Load WhatsApp contact script
            const whatsappScript = document.createElement('script');
            whatsappScript.src = 'whatsapp-contact.js';
            whatsappScript.defer = true;
            document.body.appendChild(whatsappScript);
        });

        // Preload PDF.js when user interacts with PDF tab
        document.addEventListener('DOMContentLoaded', function() {
            const pdfTab = document.querySelector('[data-tab="pdf"]');
            if (pdfTab) {
                pdfTab.addEventListener('click', function() {
                    if (typeof loadPDFJS === 'function') {
                        loadPDFJS();
                    }
                }, { once: true });
            }
        });
    </script>

    <script src="script.js"></script>
    <script src="mobile-menu.js"></script>
</body>
</html>
