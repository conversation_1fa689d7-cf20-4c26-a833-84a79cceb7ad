// Test Admin Password - Mevcut şifreyi kontrol et
const bcrypt = require('bcryptjs');

// Veritabanından aldığınız hash
const storedHash = '$2a$10$0ENWa8kWYByq7vdeotwG8uTIJ8PBireQt39PyHVv/Gu9uahTE/m3i';

// Test edilecek şifreler
const passwordsToTest = [
    '8454854',
    'admin',
    'password',
    '123456',
    'admin123',
    'northlethe',
    '<EMAIL>',
    'Admin123',
    'admin8454854'
];

console.log('🔍 Admin şifre testi başlıyor...\n');
console.log('Hash:', storedHash);
console.log('Hash uzunluğu:', storedHash.length);
console.log('Hash tipi:', storedHash.startsWith('$2a$') ? 'bcrypt' : 'bilinmeyen');
console.log('\n' + '='.repeat(50) + '\n');

async function testPasswords() {
    for (const password of passwordsToTest) {
        try {
            const isMatch = await bcrypt.compare(password, storedHash);
            console.log(`Şifre: "${password}" -> ${isMatch ? '✅ DOĞRU!' : '❌ Yanlış'}`);
            
            if (isMatch) {
                console.log('\n🎉 ŞIFRE BULUNDU!');
                console.log(`Admin şifresi: ${password}`);
                return password;
            }
        } catch (error) {
            console.log(`Şifre: "${password}" -> ❌ Hata: ${error.message}`);
        }
    }
    
    console.log('\n❌ Hiçbir şifre eşleşmedi.');
    return null;
}

// Yeni şifre hash'i oluşturma fonksiyonu
async function createNewPasswordHash(newPassword) {
    try {
        const saltRounds = 10;
        const hash = await bcrypt.hash(newPassword, saltRounds);
        console.log('\n🔐 Yeni şifre hash\'i oluşturuldu:');
        console.log('Şifre:', newPassword);
        console.log('Hash:', hash);
        console.log('\nSQL Update komutu:');
        console.log(`UPDATE "public"."admins" SET "password_hash" = '${hash}', "updated_at" = NOW() WHERE "email" = '<EMAIL>';`);
        return hash;
    } catch (error) {
        console.error('Hash oluşturma hatası:', error);
        return null;
    }
}

// Ana fonksiyon
async function main() {
    const foundPassword = await testPasswords();
    
    if (!foundPassword) {
        console.log('\n🔧 Yeni şifre oluşturuluyor...');
        await createNewPasswordHash('8454854');
    }
}

main().catch(console.error);
