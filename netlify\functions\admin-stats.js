// Admin istatistikleri function'ı
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Admin token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Admin token gerekli.' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token kontrolü
        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            console.log('Token verified for admin stats:', decoded.email);
        } catch (error) {
            console.error('Token verification failed:', error.message);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // İstatistikleri getir
        const today = new Date().toISOString().split('T')[0];

        const [
            usersResult,
            applicationsResult,
            pendingApplicationsResult,
            summariesResult,
            todaySummariesResult,
            trialStatsResult
        ] = await Promise.all([
            supabase.from('users').select('id', { count: 'exact' }),
            supabase.from('applications').select('id', { count: 'exact' }),
            supabase.from('applications').select('id', { count: 'exact' }).eq('status', 'pending'),
            supabase.from('summaries').select('id', { count: 'exact' }),
            supabase.from('summaries').select('id', { count: 'exact' }).gte('created_at', today),
            supabase.rpc('get_trial_stats')
        ]);

        // Son kullanıcıları getir
        const { data: recentUsers } = await supabase
            .from('users')
            .select('first_name, last_name, email, created_at')
            .order('created_at', { ascending: false })
            .limit(5);

        // Son özetleri getir
        const { data: recentSummaries } = await supabase
            .from('summaries')
            .select('user_email, document_type, created_at')
            .order('created_at', { ascending: false })
            .limit(10);

        const stats = {
            totalUsers: usersResult.count || 0,
            totalApplications: applicationsResult.count || 0,
            pendingApplications: pendingApplicationsResult.count || 0,
            totalSummaries: summariesResult.count || 0,
            todaySummaries: todaySummariesResult.count || 0,
            recentUsers: recentUsers || [],
            recentSummaries: recentSummaries || [],
            trialStats: trialStatsResult.data || {
                total_trials: 0,
                unique_ips: 0,
                today_trials: 0
            }
        };

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ stats })
        };

    } catch (error) {
        console.error('Admin stats error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'İstatistikler getirilemedi.' })
        };
    }
};
