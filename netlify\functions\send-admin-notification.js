// Admin <PERSON>il<PERSON> - <PERSON>ail do<PERSON>a ve diğer admin bildirimleri

const { sendEmail } = require('./utils/email-service');

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece POST metodunu kabul et
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Request body'yi parse et
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz JSON formatı.' })
            };
        }

        const { type, data } = requestData;

        if (!type || !data) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Type ve data gerekli.' })
            };
        }

        let emailResult;

        switch (type) {
            case 'email_verified':
                emailResult = await sendEmailVerifiedNotification(data);
                break;
            default:
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: `Desteklenmeyen bildirim tipi: ${type}` })
                };
        }

        if (emailResult.success) {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Admin bildirimi başarıyla gönderildi.'
                })
            };
        } else {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: emailResult.error || 'Email gönderimi başarısız.'
                })
            };
        }

    } catch (error) {
        console.error('Admin notification error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Sunucu hatası.'
            })
        };
    }
};

// Email doğrulama bildirimi gönder
async function sendEmailVerifiedNotification(data) {
    try {
        const { application, adminEmails } = data;

        if (!application || !adminEmails || adminEmails.length === 0) {
            return { success: false, error: 'Eksik veri: application veya adminEmails' };
        }

        console.log('📧 Sending email verified notification:', {
            email: application.email,
            first_name: application.first_name,
            last_name: application.last_name,
            adminEmails: adminEmails
        });

        const emailHtml = getEmailVerifiedTemplate(application);
        
        const emailResult = await sendEmail({
            to: adminEmails,
            subject: `✅ Email Doğrulandı - Onay Bekliyor: ${application.first_name} ${application.last_name}`,
            html: emailHtml,
            headers: {
                'X-Priority': '2',
                'X-MSMail-Priority': 'Normal',
                'Importance': 'normal'
            }
        });

        return emailResult;

    } catch (error) {
        console.error('Send email verified notification error:', error);
        return { success: false, error: error.message };
    }
}

// Email doğrulama template'i
function getEmailVerifiedTemplate(application) {
    return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="tr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Email Doğrulandı - LegalAI</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: Arial, sans-serif;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 24px; font-weight: bold;">
                                ✅ Email Doğrulandı
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #d1fae5; font-size: 16px;">
                                Yeni üye onay bekliyor
                            </p>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px 30px;">
                            <!-- User Info -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid #10b981;">
                                <h2 style="margin: 0 0 15px 0; font-size: 18px; color: #2d3748;">
                                    Kullanıcı Bilgileri
                                </h2>
                                <div style="margin-bottom: 10px;">
                                    <strong style="color: #4a5568;">Ad Soyad:</strong>
                                    <span style="color: #2d3748;">${application.first_name} ${application.last_name}</span>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <strong style="color: #4a5568;">Email:</strong>
                                    <span style="color: #2d3748;">${application.email}</span>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <strong style="color: #4a5568;">Telefon:</strong>
                                    <span style="color: #2d3748;">${application.phone || 'Belirtilmemiş'}</span>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <strong style="color: #4a5568;">Meslek:</strong>
                                    <span style="color: #2d3748;">${application.profession || 'Belirtilmemiş'}</span>
                                </div>
                                <div>
                                    <strong style="color: #4a5568;">Başvuru Tarihi:</strong>
                                    <span style="color: #2d3748;">${new Date(application.created_at).toLocaleDateString('tr-TR')}</span>
                                </div>
                            </div>

                            <!-- Status -->
                            <div style="background: #ecfdf5; border: 1px solid #a7f3d0; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <span style="background: #10b981; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                                        EMAIL DOĞRULANDI
                                    </span>
                                    <span style="background: #f59e0b; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                                        ONAY BEKLİYOR
                                    </span>
                                </div>
                                <p style="margin: 0; font-size: 14px; color: #065f46;">
                                    Kullanıcı email adresini başarıyla doğruladı. Artık admin onayı için bekliyor.
                                </p>
                            </div>

                            <!-- Action Button -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/admin#applications" 
                                   style="background: #10b981; color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 16px;">
                                    👥 Admin Paneline Git
                                </a>
                            </div>

                            <!-- Footer -->
                            <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 30px; text-align: center;">
                                <p style="margin: 0; font-size: 12px; color: #718096;">
                                    Bu email otomatik olarak gönderilmiştir. Yanıtlamayın.
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #718096;">
                                    LegalAI Admin Sistemi
                                </p>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    `;
}
