// Destek Talebi Oluşturma API Endpoint

const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Supabase client
function getSupabase() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing');
    }

    return createClient(supabaseUrl, supabaseKey);
}

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece POST metodunu kabul et
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Request body'yi parse et
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz JSON formatı.' })
            };
        }

        // Kullanıcının varlığını kontrol et
        const supabase = getSupabase();
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, first_name, last_name, status')
            .eq('id', decoded.userId)
            .single();

        if (userError || !user) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
            };
        }

        // Kullanıcı onaylanmış mı kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({ 
                    error: 'Destek talebi oluşturmak için hesabınızın onaylanmış olması gerekir.' 
                })
            };
        }

        // Input validasyonu
        const { category_id, subject, description, priority } = requestData;

        if (!category_id) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Kategori seçimi zorunludur.' })
            };
        }

        if (!subject || subject.trim().length < 5 || subject.trim().length > 255) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Konu 5-255 karakter arasında olmalıdır.' })
            };
        }

        if (!description || description.trim().length < 20 || description.trim().length > 2000) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Açıklama 20-2000 karakter arasında olmalıdır.' })
            };
        }

        const validPriorities = ['low', 'normal', 'high', 'urgent'];
        if (!priority || !validPriorities.includes(priority)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz öncelik seviyesi.' })
            };
        }

        // Kategori varlığını kontrol et
        const { data: category, error: categoryError } = await supabase
            .from('support_categories')
            .select('id, name')
            .eq('id', category_id)
            .eq('is_active', true)
            .single();

        if (categoryError || !category) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz kategori.' })
            };
        }

        // Açık destek talebi kontrolü - maksimum 2 açık ticket
        const openStatuses = ['open', 'in_progress', 'waiting'];
        const { data: openTickets, error: openError } = await supabase
            .from('support_tickets')
            .select('id, ticket_number, subject, status')
            .eq('user_id', user.id)
            .in('status', openStatuses);

        if (openError) {
            console.error('Open tickets check error:', openError);
        } else if (openTickets && openTickets.length >= 2) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    error: 'Maksimum 2 açık destek talebiniz olabilir. Mevcut açık taleplerinizi kapatın veya çözümlenene kadar bekleyin.',
                    openTickets: openTickets.map(t => ({
                        number: t.ticket_number,
                        subject: t.subject,
                        status: t.status
                    }))
                })
            };
        }

        // Rate limiting - kullanıcı son 1 saatte kaç ticket oluşturmuş
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
        const { data: recentTickets, error: recentError } = await supabase
            .from('support_tickets')
            .select('id')
            .eq('user_id', user.id)
            .gte('created_at', oneHourAgo);

        if (recentError) {
            console.error('Recent tickets check error:', recentError);
        } else if (recentTickets && recentTickets.length >= 5) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({
                    error: 'Çok fazla destek talebi oluşturdunuz. Lütfen 1 saat sonra tekrar deneyin.'
                })
            };
        }

        // Ticket oluştur
        const ticketData = {
            user_id: user.id,
            category_id: category_id,
            subject: subject.trim(),
            description: description.trim(),
            priority: priority,
            user_email: user.email,
            user_name: `${user.first_name} ${user.last_name}`,
            status: 'open'
        };

        const { data: ticket, error: ticketError } = await supabase
            .from('support_tickets')
            .insert([ticketData])
            .select(`
                id,
                ticket_number,
                subject,
                description,
                priority,
                status,
                created_at,
                user_name,
                user_email,
                support_categories(name, color, icon)
            `)
            .single();

        if (ticketError) {
            console.error('Ticket creation error:', ticketError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Destek talebi oluşturulamadı.' })
            };
        }

        // İlk mesajı ticket replies tablosuna ekle
        const replyData = {
            ticket_id: ticket.id,
            user_id: user.id,
            message: description.trim(),
            is_admin_reply: false
        };

        const { error: replyError } = await supabase
            .from('support_ticket_replies')
            .insert([replyData]);

        if (replyError) {
            console.error('Initial reply creation error:', replyError);
            // Ticket oluşturuldu ama reply eklenemedi, devam et
        }

        // Admin'lere e-posta bildirimi gönder (async, hata durumunda devam et)
        try {
            const adminEmails = await getAdminEmails();
            if (adminEmails.length > 0) {
                await sendNewTicketNotification(ticket, user, adminEmails);
            }
        } catch (emailError) {
            console.error('Email notification error:', emailError);
            // E-posta hatası ticket oluşturulmasını engellemez
        }

        // Telegram bildirimi gönder (async, hata durumunda devam et)
        try {
            await sendTelegramTicketNotification(ticket, user);
        } catch (telegramError) {
            console.error('Telegram notification error:', telegramError);
            // Telegram hatası ticket oluşturulmasını engellemez
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Destek talebiniz başarıyla oluşturuldu.',
                ticket: {
                    id: ticket.id,
                    ticket_number: ticket.ticket_number,
                    subject: ticket.subject,
                    priority: ticket.priority,
                    status: ticket.status,
                    category: ticket.support_categories,
                    created_at: ticket.created_at
                }
            })
        };

    } catch (error) {
        console.error('Support ticket creation error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}

// Helper fonksiyonlar
async function getAdminEmails() {
    try {
        const supabase = getSupabase();
        const { data: admins, error } = await supabase
            .from('admins')
            .select('email')
            .eq('is_active', true);

        if (error) {
            console.error('Get admin emails error:', error);
            return [];
        }

        return admins ? admins.map(admin => admin.email) : [];

    } catch (error) {
        console.error('Get admin emails error:', error);
        return [];
    }
}

async function sendNewTicketNotification(ticket, user, adminEmails) {
    try {
        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/support-send-notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'new_ticket',
                data: {
                    ticket: ticket,
                    user: user,
                    adminEmails: adminEmails
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Email notification failed:', errorData);
        } else {
            console.log('New ticket email notification sent successfully');
        }

    } catch (error) {
        console.error('Send notification error:', error);
    }
}

// Telegram bildirimi gönder
async function sendTelegramTicketNotification(ticket, user) {
    try {
        const response = await fetch(`${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-telegram-notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'new_support_ticket',
                ticketInfo: {
                    ticket_number: ticket.ticket_number,
                    subject: ticket.subject,
                    description: ticket.description,
                    priority: ticket.priority,
                    category_name: ticket.support_categories?.name
                },
                userInfo: {
                    first_name: user.first_name,
                    last_name: user.last_name,
                    email: user.email
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Telegram notification failed:', errorData);
        } else {
            console.log('Telegram ticket notification sent successfully');
        }

    } catch (error) {
        console.error('Send telegram notification error:', error);
    }
}
