const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        console.log('Initializing system_settings table...');

        // Önce tabloyu kontrol et
        const { data: existingSettings, error: checkError } = await supabase
            .from('system_settings')
            .select('setting_key')
            .limit(1);

        if (checkError && checkError.code === '42P01') {
            // Tablo yok - manuel olarak oluşturulması gerekiyor
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'System_settings tablosu bulunamadı',
                    message: 'Lütfen Supabase SQL Editor\'da database/system_settings_table.sql dosyasını çalıştırın',
                    sql_file: 'database/system_settings_table.sql'
                })
            };
        } else if (checkError) {
            console.error('Table check error:', checkError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Tablo kontrolü başarısız',
                    details: checkError.message
                })
            };
        }

        console.log('System_settings tablosu mevcut, varsayılan ayarları kontrol ediliyor...');

        // Varsayılan ayarları ekle (mevcut tablo yapısına uygun)
        const defaultSettings = [
            {
                setting_key: 'gemini_model',
                setting_value: 'gemini-2.5-flash-lite-preview-06-17',
                display_name: 'Gemini AI Model',
                description: 'Aktif Gemini AI modeli',
                setting_type: 'select',
                options: {
                    "gemini-2.5-flash-lite-preview-06-17": "Gemini 2.5 Flash Lite Preview 06-17",
                    "gemini-1.5-flash": "Gemini 1.5 Flash",
                    "gemini-1.5-pro": "Gemini 1.5 Pro",
                    "gemini-2.0-flash-exp": "Gemini 2.0 Flash Experimental"
                },
                is_active: true
            },
            {
                setting_key: 'manual_api_key',
                setting_value: '', // NOT NULL constraint nedeniyle boş string
                display_name: 'Manuel API Key',
                description: 'Manuel olarak seçilen API key',
                setting_type: 'hidden',
                options: null,
                is_active: false
            }
        ];

        // Ayarları tek tek kontrol et ve ekle
        const results = [];
        for (const setting of defaultSettings) {
            // Önce var mı kontrol et
            const { data: existing } = await supabase
                .from('system_settings')
                .select('setting_key')
                .eq('setting_key', setting.setting_key)
                .single();

            if (!existing) {
                // Yoksa ekle
                const { data: inserted, error: insertError } = await supabase
                    .from('system_settings')
                    .insert(setting)
                    .select()
                    .single();

                if (insertError) {
                    console.error(`Error inserting ${setting.setting_key}:`, insertError);
                    results.push({ setting_key: setting.setting_key, status: 'error', error: insertError.message });
                } else {
                    console.log(`Inserted ${setting.setting_key}`);
                    results.push({ setting_key: setting.setting_key, status: 'inserted' });
                }
            } else {
                console.log(`${setting.setting_key} already exists`);
                results.push({ setting_key: setting.setting_key, status: 'exists' });
            }
        }

        // Sonuçları kontrol et
        const { data: allSettings, error: selectError } = await supabase
            .from('system_settings')
            .select('*');

        if (selectError) {
            console.error('Select error:', selectError);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'System_settings tablosu kontrol edildi ve varsayılan ayarlar eklendi',
                results: results,
                settings: allSettings || []
            })
        };

    } catch (error) {
        console.error('Init system settings error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sistem ayarları başlatılamadı',
                details: error.message
            })
        };
    }
};
