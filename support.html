<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Destek Talebi Oluştur - Hukuki Belge Özetleme</title>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .support-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .support-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .support-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .support-header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .support-header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .support-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2d3748;
            font-weight: 600;
            font-size: 0.95rem;
        }

        .form-group label .required {
            color: #e53e3e;
            margin-left: 3px;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #fff;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-control.textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .priority-options {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 8px;
        }

        .priority-option {
            position: relative;
        }

        .priority-option input[type="radio"] {
            display: none;
        }

        .priority-option label {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 8px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
            text-align: center;
            margin-bottom: 0;
        }

        .priority-option.low label {
            color: #38a169;
            border-color: #c6f6d5;
        }

        .priority-option.normal label {
            color: #3182ce;
            border-color: #bee3f8;
        }

        .priority-option.high label {
            color: #d69e2e;
            border-color: #faf089;
        }

        .priority-option.urgent label {
            color: #e53e3e;
            border-color: #fed7d7;
        }

        .priority-option input[type="radio"]:checked + label {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .priority-option.low input[type="radio"]:checked + label {
            background: #c6f6d5;
            border-color: #38a169;
        }

        .priority-option.normal input[type="radio"]:checked + label {
            background: #bee3f8;
            border-color: #3182ce;
        }

        .priority-option.high input[type="radio"]:checked + label {
            background: #faf089;
            border-color: #d69e2e;
        }

        .priority-option.urgent input[type="radio"]:checked + label {
            background: #fed7d7;
            border-color: #e53e3e;
        }

        .submit-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .submit-btn .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        .submit-btn.loading .loading-spinner {
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #5a67d8;
            transform: translateX(-5px);
        }

        .back-link i {
            margin-right: 8px;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #e53e3e;
            display: none;
        }

        .success-message {
            background: #c6f6d5;
            color: #2f855a;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #38a169;
            display: none;
        }

        @media (max-width: 768px) {
            .support-card {
                padding: 30px 20px;
                margin: 10px;
            }

            .support-header h1 {
                font-size: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .priority-options {
                grid-template-columns: 1fr 1fr;
                gap: 8px;
            }

            .priority-option label {
                padding: 10px 6px;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <div class="support-container">
        <div class="support-card">
            <a href="/profile.html" class="back-link">
                <i class="fas fa-arrow-left"></i>
                Profil Sayfasına Dön
            </a>

            <div class="support-header">
                <i class="fas fa-headset support-icon"></i>
                <h1>Destek Talebi Oluştur</h1>
                <p>Sorununuzu detaylı bir şekilde açıklayın, size en kısa sürede yardımcı olalım.</p>
            </div>

            <div class="error-message" id="error-message"></div>
            <div class="success-message" id="success-message"></div>

            <form id="support-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="category">Kategori <span class="required">*</span></label>
                        <select id="category" name="category" class="form-control" required>
                            <option value="">Kategori seçiniz...</option>
                            <!-- Kategoriler dinamik olarak yüklenecek -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="subject">Konu <span class="required">*</span></label>
                        <input type="text" id="subject" name="subject" class="form-control" 
                               placeholder="Sorununuzun kısa başlığı" required maxlength="255">
                    </div>
                </div>

                <div class="form-group">
                    <label>Öncelik Seviyesi <span class="required">*</span></label>
                    <div class="priority-options">
                        <div class="priority-option low">
                            <input type="radio" id="priority-low" name="priority" value="low" required>
                            <label for="priority-low">
                                <i class="fas fa-circle" style="margin-right: 5px;"></i>
                                Düşük
                            </label>
                        </div>
                        <div class="priority-option normal">
                            <input type="radio" id="priority-normal" name="priority" value="normal" checked>
                            <label for="priority-normal">
                                <i class="fas fa-circle" style="margin-right: 5px;"></i>
                                Normal
                            </label>
                        </div>
                        <div class="priority-option high">
                            <input type="radio" id="priority-high" name="priority" value="high">
                            <label for="priority-high">
                                <i class="fas fa-circle" style="margin-right: 5px;"></i>
                                Yüksek
                            </label>
                        </div>
                        <div class="priority-option urgent">
                            <input type="radio" id="priority-urgent" name="priority" value="urgent">
                            <label for="priority-urgent">
                                <i class="fas fa-circle" style="margin-right: 5px;"></i>
                                Acil
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group full-width">
                    <label for="description">Detaylı Açıklama <span class="required">*</span></label>
                    <textarea id="description" name="description" class="form-control textarea" 
                              placeholder="Sorununuzu mümkün olduğunca detaylı açıklayın. Hangi adımları izlediğinizi, ne tür bir hata aldığınızı veya hangi konuda yardıma ihtiyacınız olduğunu belirtin." 
                              required maxlength="2000"></textarea>
                    <small style="color: #718096; font-size: 0.85rem; margin-top: 5px; display: block;">
                        <span id="char-count">0</span>/2000 karakter
                    </small>
                </div>

                <button type="submit" class="submit-btn" id="submit-btn">
                    <span class="loading-spinner"></span>
                    <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>
                    Destek Talebi Gönder
                </button>
            </form>
        </div>
    </div>

    <script src="support.js"></script>
</body>
</html>
