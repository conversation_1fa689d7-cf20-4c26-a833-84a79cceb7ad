// Check Admin in Database

import { supabase } from '../../supabase-config.js';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        const adminId = 'ed53610b-d41f-4467-bae4-2ba8763d669e';
        
        // Admin'i ID ile ara
        const { data: adminById, error: byIdError } = await supabase
            .from('admins')
            .select('*')
            .eq('id', adminId)
            .single();

        // Admin'i email ile ara
        const { data: adminByEmail, error: byEmailError } = await supabase
            .from('admins')
            .select('*')
            .eq('email', '<EMAIL>')
            .single();

        // Tüm adminleri listele
        const { data: allAdmins, error: allError } = await supabase
            .from('admins')
            .select('id, email, role, is_active, full_name');

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                searchedAdminId: adminId,
                searchedEmail: '<EMAIL>',
                adminById: {
                    found: !byIdError && adminById,
                    data: adminById,
                    error: byIdError?.message
                },
                adminByEmail: {
                    found: !byEmailError && adminByEmail,
                    data: adminByEmail,
                    error: byEmailError?.message
                },
                allAdmins: {
                    count: allAdmins?.length || 0,
                    data: allAdmins,
                    error: allError?.message
                }
            })
        };

    } catch (error) {
        console.error('Check admin error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Sunucu hatası',
                details: error.message 
            })
        };
    }
}
