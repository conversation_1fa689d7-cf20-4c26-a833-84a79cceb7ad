// Destek Sistemi E-posta Template'leri

function getNewTicketEmailTemplate(ticket, user) {
    // Debug: Ticket ve user bilgilerini logla
    console.log('📧 Email template debug:', {
        ticket: ticket,
        user: user,
        hasDescription: !!ticket?.description,
        description: ticket?.description
    });

    const priorityColors = {
        low: '#38a169',
        normal: '#3182ce',
        high: '#d69e2e',
        urgent: '#e53e3e'
    };

    const priorityTexts = {
        low: 'Düşük',
        normal: 'Normal',
        high: 'Yü<PERSON><PERSON>',
        urgent: 'Acil'
    };

    return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="tr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Yeni Destek Talebi - LegalAI</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: Arial, sans-serif;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 24px; font-weight: bold;">
                                🎧 Yeni Destek Talebi
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #e2e8f0; font-size: 16px;">
                                LegalAI Destek Sistemi
                            </p>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px 30px;">
                            <!-- Ticket Info -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid ${priorityColors[ticket.priority]};">
                                <h2 style="margin: 0 0 15px 0; font-size: 18px; color: #2d3748;">
                                    Ticket #${ticket.ticket_number}
                                </h2>
                                <p style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; color: #1a202c;">
                                    ${ticket.subject}
                                </p>
                                <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                                    <span style="background: ${priorityColors[ticket.priority]}; color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold; text-transform: uppercase;">
                                        ${priorityTexts[ticket.priority]} ÖNCELİK
                                    </span>
                                    <span style="color: #718096; font-size: 14px;">
                                        📧 ${user.email}
                                    </span>
                                </div>
                                <div style="color: #4a5568; font-size: 14px;">
                                    👤 <strong>${user.first_name} ${user.last_name}</strong><br>
                                    📱 ${user.phone || 'Belirtilmemiş'}<br>
                                    💼 ${user.profession || 'Belirtilmemiş'}
                                </div>
                            </div>

                            <!-- Message -->
                            <div style="background: #ffffff; border: 1px solid #e2e8f0; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <h3 style="margin: 0 0 15px 0; font-size: 16px; color: #2d3748;">Mesaj:</h3>
                                <p style="margin: 0; font-size: 14px; color: #4a5568; line-height: 1.6; white-space: pre-wrap;">${ticket.description || 'Mesaj içeriği bulunamadı.'}</p>
                            </div>

                            <!-- Action Button -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/admin"
                                   style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px;">
                                    🎧 Admin Panelinde Görüntüle
                                </a>
                            </div>

                            <!-- Footer -->
                            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
                                <p style="margin: 0; font-size: 12px; color: #9ca3af; text-align: center;">
                                    Bu e-posta LegalAI destek sistemi tarafından otomatik olarak gönderilmiştir.<br>
                                    Destek: <EMAIL>
                                </p>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    `;
}

function getTicketReplyEmailTemplate(ticket, reply, isAdminReply, adminInfo = null) {
    // Debug: Gelen verileri logla
    console.log('📧 Reply email template debug:', {
        ticket: ticket,
        reply: reply,
        isAdminReply: isAdminReply,
        adminInfo: adminInfo,
        ticketNumber: ticket?.ticket_number
    });

    const statusColors = {
        open: '#e53e3e',
        in_progress: '#3182ce',
        waiting: '#d69e2e',
        resolved: '#38a169',
        closed: '#718096'
    };

    const statusTexts = {
        open: 'Açık',
        in_progress: 'Devam Ediyor',
        waiting: 'Beklemede',
        resolved: 'Çözüldü',
        closed: 'Kapatıldı'
    };

    // Admin bilgilerini doğru şekilde al
    const replyAuthor = isAdminReply
        ? (adminInfo?.full_name || adminInfo?.fullName || 'Destek Ekibi')
        : 'Kullanıcı';
    const replyIcon = isAdminReply ? '💬' : '📝';

    return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="tr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${isAdminReply ? 'Destek Ekibinden Yanıt' : 'Destek Talebinize Yanıt'} - LegalAI</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: Arial, sans-serif;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background: ${isAdminReply ? 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'}; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 24px; font-weight: bold;">
                                ${isAdminReply ? '💬 Destek Ekibinden Yanıt' : '📝 Destek Talebinize Yanıt'}
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #e2e8f0; font-size: 16px;">
                                Ticket #${ticket.ticket_number}
                            </p>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px 30px;">
                            <!-- Ticket Info -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <h2 style="margin: 0 0 10px 0; font-size: 18px; color: #2d3748;">
                                    ${ticket.subject}
                                </h2>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <span style="background: ${statusColors[ticket.status]}; color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold; text-transform: uppercase;">
                                        ${statusTexts[ticket.status]}
                                    </span>
                                    <span style="color: #718096; font-size: 14px;">
                                        📅 ${new Date(reply.created_at).toLocaleDateString('tr-TR', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </span>
                                </div>
                            </div>

                            <!-- Reply -->
                            <div style="background: ${isAdminReply ? '#f0f9ff' : '#ffffff'}; border: 2px solid ${isAdminReply ? '#0ea5e9' : '#e2e8f0'}; border-left: 4px solid ${isAdminReply ? '#0ea5e9' : '#667eea'}; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                                    <span style="font-weight: bold; color: ${isAdminReply ? '#0ea5e9' : '#4a5568'};">
                                        ${isAdminReply ? '🎧' : '👤'} ${reply.author_name}
                                    </span>
                                    ${isAdminReply ? '<span style="background: #0ea5e9; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: bold;">DESTEK EKİBİ</span>' : ''}
                                </div>
                                <p style="margin: 0; font-size: 14px; color: #2d3748; line-height: 1.6; white-space: pre-wrap;">${reply.message}</p>
                            </div>

                            <!-- Action Button -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/support-tickets"
                                   style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px;">
                                    💬 Yanıt Ver
                                </a>
                            </div>

                            <!-- Footer -->
                            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
                                <p style="margin: 0; font-size: 12px; color: #9ca3af; text-align: center;">
                                    Bu e-posta LegalAI destek sistemi tarafından otomatik olarak gönderilmiştir.<br>
                                    Destek: <EMAIL>
                                </p>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    `;
}

function getTicketStatusChangeEmailTemplate(ticket, oldStatus, newStatus, changedBy) {
    const statusColors = {
        open: '#e53e3e',
        in_progress: '#3182ce',
        waiting: '#d69e2e',
        resolved: '#38a169',
        closed: '#718096'
    };

    const statusTexts = {
        open: 'Açık',
        in_progress: 'Devam Ediyor',
        waiting: 'Beklemede',
        resolved: 'Çözüldü',
        closed: 'Kapatıldı'
    };

    return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="tr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Destek Talebi Durumu Güncellendi - LegalAI</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: Arial, sans-serif;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 24px; font-weight: bold;">
                                🔄 Durum Güncellendi
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #fef3c7; font-size: 16px;">
                                Ticket #${ticket.ticket_number}
                            </p>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px 30px;">
                            <!-- Ticket Info -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <h2 style="margin: 0 0 15px 0; font-size: 18px; color: #2d3748;">
                                    ${ticket.subject}
                                </h2>
                                <p style="margin: 0 0 20px 0; color: #4a5568; font-size: 14px;">
                                    Destek talebinizin durumu güncellendi.
                                </p>
                                
                                <!-- Status Change -->
                                <div style="display: flex; align-items: center; gap: 15px; justify-content: center; margin: 20px 0;">
                                    <span style="background: ${statusColors[oldStatus]}; color: white; padding: 8px 16px; border-radius: 12px; font-size: 14px; font-weight: bold;">
                                        ${statusTexts[oldStatus]}
                                    </span>
                                    <span style="font-size: 20px; color: #718096;">→</span>
                                    <span style="background: ${statusColors[newStatus]}; color: white; padding: 8px 16px; border-radius: 12px; font-size: 14px; font-weight: bold;">
                                        ${statusTexts[newStatus]}
                                    </span>
                                </div>
                                
                                <p style="margin: 15px 0 0 0; color: #718096; font-size: 12px; text-align: center;">
                                    Güncelleme: ${changedBy} tarafından
                                </p>
                            </div>

                            ${newStatus === 'resolved' ? `
                            <div style="background: #dcfce7; border: 1px solid #16a34a; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <h3 style="margin: 0 0 10px 0; color: #15803d; font-size: 16px;">✅ Talebiniz Çözüldü!</h3>
                                <p style="margin: 0; color: #166534; font-size: 14px;">
                                    Destek talebiniz başarıyla çözüldü. Eğer sorunuz devam ediyorsa, lütfen yanıt vererek bize bildirin.
                                </p>
                            </div>
                            ` : ''}

                            ${newStatus === 'closed' ? `
                            <div style="background: #f3f4f6; border: 1px solid #6b7280; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                                <h3 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">🔒 Talep Kapatıldı</h3>
                                <p style="margin: 0; color: #4b5563; font-size: 14px;">
                                    Destek talebiniz kapatıldı. Yeni bir sorunuz olursa, yeni bir destek talebi oluşturabilirsiniz.
                                </p>
                            </div>
                            ` : ''}

                            <!-- Action Button -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${process.env.SITE_URL || 'https://hukukibelgeozetleme.netlify.app'}/support-tickets"
                                   style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px;">
                                    📋 Destek Kayıtlarım
                                </a>
                            </div>

                            <!-- Footer -->
                            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
                                <p style="margin: 0; font-size: 12px; color: #9ca3af; text-align: center;">
                                    Bu e-posta LegalAI destek sistemi tarafından otomatik olarak gönderilmiştir.<br>
                                    Destek: <EMAIL>
                                </p>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    `;
}

module.exports = {
    getNewTicketEmailTemplate,
    getTicketReplyEmailTemplate,
    getTicketStatusChangeEmailTemplate
};
