// Admin Destek Kategorileri API Endpoint

import { supabase } from '../../supabase-config.js';
import jwt from 'jsonwebtoken';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece GET metodunu kabul et
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // JWT token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;

        let decoded;
        try {
            decoded = jwt.verify(token, jwtSecret);
        } catch (jwtError) {
            console.error('JWT verification failed:', jwtError);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Admin kontrolü - detaylı debug ile
        const { data: allAdmins, error: allError } = await supabase
            .from('admins')
            .select('id, email, role, is_active')
            .eq('email', decoded.email);

        // Aktif admin'i bul
        const activeAdmin = allAdmins?.find(a => a.is_active === true);

        if (!activeAdmin) {
            return {
                statusCode: 403,
                headers,
                body: JSON.stringify({
                    error: 'Admin yetkisi gerekli.',
                    debug: {
                        tokenEmail: decoded.email,
                        allError: allError?.message,
                        allAdminsCount: allAdmins?.length || 0,
                        allAdmins: allAdmins,
                        activeAdminFound: !!activeAdmin
                    }
                })
            };
        }

        // Aktif admin bulundu, devam et
        const admin = activeAdmin;

        // Tüm kategorileri getir (aktif ve pasif)
        const { data: categories, error: categoriesError } = await supabase
            .from('support_categories')
            .select('id, name, description, color, icon, sort_order, is_active')
            .order('sort_order');

        if (categoriesError) {
            console.error('Categories fetch error:', categoriesError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Kategoriler getirilemedi.' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                categories: categories || []
            })
        };

    } catch (error) {
        console.error('Admin support categories error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}
