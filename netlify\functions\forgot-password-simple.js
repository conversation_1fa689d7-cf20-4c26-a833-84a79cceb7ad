exports.handler = async (event, context) => {
    console.log('Simple forgot password function called');
    
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        console.log('Processing forgot password request');
        
        const body = JSON.parse(event.body || '{}');
        const { email } = body;

        console.log('Email received:', email);

        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'E-posta adresi gereklidir.' })
            };
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçerli bir e-posta adresi girin.' })
            };
        }

        console.log('Email validation passed');

        // For now, just return success without actually sending email
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Test: Şifre sıfırlama linki e-posta adresinize gönderildi.'
            })
        };

    } catch (error) {
        console.error('Forgot password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Şifre sıfırlama isteği işlenirken hata oluştu.',
                details: error.message 
            })
        };
    }
};
