// Admin Logout Function'ı
const { createClient } = require('@supabase/supabase-js');
const { verifyAdminToken } = require('./admin-verify-token');
const crypto = require('crypto');

// Supabase client (Service Key ile)
const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

// IP adresini al
function getClientIP(event) {
    return event.headers['x-forwarded-for'] || 
           event.headers['x-real-ip'] || 
           event.requestContext?.identity?.sourceIp || 
           'unknown';
}

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    const clientIP = getClientIP(event);
    const userAgent = event.headers['user-agent'] || 'unknown';

    try {
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        
        // JWT token kontrolü
        let adminEmail = 'unknown';
        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminEmail = decoded.email;
        } catch (error) {
            // Token geçersiz ama logout'a izin ver
            console.log('Invalid token during logout:', error.message);
        }

        // Logout log'u
        await supabase.from('admin_logs').insert([{
            admin_username: adminEmail,
            admin_email: adminEmail,
            action: 'logout',
            details: { ip: clientIP },
            ip_address: clientIP,
            user_agent: userAgent
        }]).catch(err => console.error('Log insert error:', err));

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true,
                message: 'Başarıyla çıkış yapıldı.'
            })
        };

    } catch (error) {
        console.error('Admin logout error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Çıkış işlemi sırasında hata oluştu.' })
        };
    }
};
