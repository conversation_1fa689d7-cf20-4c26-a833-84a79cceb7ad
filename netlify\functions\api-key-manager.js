const { createClient } = require('@supabase/supabase-js');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Dinamik model yükleme fonksiyonu
async function getCurrentGeminiModel() {
    try {
        const { data: setting, error } = await supabase
            .from('system_settings')
            .select('setting_value, options')
            .eq('setting_key', 'gemini_model')
            .eq('is_active', true)
            .single();

        if (error || !setting) {
            console.warn('Model ayarı bulunamadı, varsayılan model kullanılıyor');
            return {
                model: 'gemini-2.5-flash-lite-preview-06-17',
                displayName: 'Gemini 2.5 Flash Lite Preview 06-17',
                description: 'Varsayılan model'
            };
        }

        return {
            model: setting.setting_value,
            displayName: setting.options[setting.setting_value] || setting.setting_value,
            description: 'Supabase\'den yüklenen aktif model'
        };
    } catch (error) {
        console.error('Model yükleme hatası:', error);
        return {
            model: 'gemini-2.5-flash-lite-preview-06-17',
            displayName: 'Gemini 2.5 Flash Lite Preview 06-17',
            description: 'Hata durumunda varsayılan model'
        };
    }
}

// API Key Manager - Otomatik failover sistemi
class ApiKeyManager {
    constructor() {
        this.apiKeys = this.loadApiKeys();
        this.currentKeyIndex = 0;
        this.failedKeys = new Set();
        this.manuallySelectedKey = null; // Admin tarafından manuel seçilen key
        this.initialized = false;
    }

    // Manuel seçimi Supabase'den yükle
    async loadManualSelection() {
        if (this.initialized) return;

        try {
            console.log('🔍 Loading manual API key selection from database...');
            const { data: setting, error } = await supabase
                .from('system_settings')
                .select('setting_value, is_active')
                .eq('setting_key', 'manual_api_key')
                .single();

            console.log('🔍 Database query result:', { setting, error });

            // NOT NULL constraint nedeniyle boş string kontrolü yapıyoruz
            if (!error && setting && setting.is_active && setting.setting_value && setting.setting_value.trim() !== '') {
                this.manuallySelectedKey = setting.setting_value.trim();
                console.log(`✅ Loaded manual API key selection from database: ${this.manuallySelectedKey}`);
            } else {
                console.log('❌ No active manual API key selection found');
                console.log('Setting details:', setting);
                this.manuallySelectedKey = null;
            }
        } catch (error) {
            console.error('❌ Could not load manual API key selection:', error.message);
            this.manuallySelectedKey = null;
        }

        this.initialized = true;
    }

    // Manuel seçimi Supabase'e kaydet
    async saveManualSelection(keyName) {
        try {
            if (keyName) {
                console.log(`💾 Saving manual API key selection: ${keyName}`);

                // Önce mevcut kaydı kontrol et
                const { data: existing, error: selectError } = await supabase
                    .from('system_settings')
                    .select('id')
                    .eq('setting_key', 'manual_api_key')
                    .single();

                console.log('💾 Existing record:', existing);
                console.log('💾 Select error:', selectError);

                if (existing) {
                    // Mevcut kayıt varsa güncelle
                    const { error: updateError } = await supabase
                        .from('system_settings')
                        .update({
                            setting_value: keyName,
                            is_active: true,
                            updated_at: new Date().toISOString()
                        })
                        .eq('setting_key', 'manual_api_key');

                    if (updateError) {
                        console.error('❌ Failed to update manual API key selection:', updateError);
                    } else {
                        console.log(`✅ Successfully updated manual API key selection: ${keyName}`);
                    }
                } else {
                    // Kayıt yoksa yeni oluştur
                    const { error: insertError } = await supabase
                        .from('system_settings')
                        .insert({
                            setting_key: 'manual_api_key',
                            setting_value: keyName,
                            display_name: 'Manuel API Key',
                            description: 'Manuel olarak seçilen API key',
                            setting_type: 'hidden',
                            is_active: true
                        });

                    if (insertError) {
                        console.error('❌ Failed to insert manual API key selection:', insertError);
                    } else {
                        console.log(`✅ Successfully inserted manual API key selection: ${keyName}`);
                    }
                }
            } else {
                console.log('💾 Clearing manual API key selection');
                // Manuel seçimi temizle (NOT NULL constraint nedeniyle boş string kullanıyoruz)
                const { error } = await supabase
                    .from('system_settings')
                    .update({
                        setting_value: '', // NOT NULL olduğu için boş string
                        is_active: false,
                        updated_at: new Date().toISOString()
                    })
                    .eq('setting_key', 'manual_api_key');

                if (error) {
                    console.error('❌ Failed to clear manual API key selection:', error);
                } else {
                    console.log('✅ Successfully cleared manual API key selection');
                }
            }
        } catch (error) {
            console.error('❌ Error saving manual API key selection:', error);
        }
    }

    // Tüm API key'leri yükle
    loadApiKeys() {
        const keys = [];
        
        for (let i = 1; i <= 20; i++) {
            const keyName = i === 1 ? 'GEMINI_API_KEY' : `GEMINI_API_KEY${i}`;
            const keyValue = process.env[keyName];
            
            if (keyValue) {
                keys.push({
                    name: keyName,
                    value: keyValue,
                    index: i - 1
                });
            }
        }
        
        return keys;
    }

    // Aktif API key'i al
    async getCurrentApiKey() {
        // Manuel seçimi her seferinde yükle (Netlify functions state paylaşımı sorunu için)
        this.initialized = false; // Force reload
        await this.loadManualSelection();

        console.log(`🔑 getCurrentApiKey called - manuallySelectedKey: ${this.manuallySelectedKey}`);
        console.log(`🔑 Available API keys: ${this.apiKeys.map(k => k.name).join(', ')}`);
        console.log(`🔑 Failed keys: ${Array.from(this.failedKeys).join(', ')}`);

        // Önce manuel seçilen key'i kontrol et
        if (this.manuallySelectedKey) {
            const manualKey = this.apiKeys.find(k => k.name === this.manuallySelectedKey);
            console.log(`🔑 Manual key found in available keys: ${!!manualKey}`);
            console.log(`🔑 Manual key failed: ${this.failedKeys.has(this.manuallySelectedKey)}`);

            if (manualKey && !this.failedKeys.has(manualKey.name)) {
                // Manuel seçilen key çalışıyor, onu kullan
                // currentKeyIndex'i güncellemiyoruz, manuel seçim öncelikli
                console.log(`✅ Using manually selected API key: ${manualKey.name}`);
                return manualKey;
            } else if (manualKey && this.failedKeys.has(manualKey.name)) {
                console.log(`❌ Manually selected key ${this.manuallySelectedKey} has failed, falling back to automatic selection`);
                this.manuallySelectedKey = null; // Manuel seçimi temizle
                await this.saveManualSelection(null); // Veritabanından da temizle
            }
        } else {
            console.log('🔑 No manual selection found, using automatic selection');
        }

        // Eğer tüm key'ler başarısız olduysa, failed listesini temizle ve baştan başla
        if (this.failedKeys.size >= this.apiKeys.length) {
            console.log('All API keys failed, resetting failed list');
            this.failedKeys.clear();
            this.currentKeyIndex = 0;
            this.manuallySelectedKey = null; // Manuel seçimi de temizle
        }

        // Çalışan bir key bul (sadece manuel seçim yoksa)
        for (let i = 0; i < this.apiKeys.length; i++) {
            const keyIndex = (this.currentKeyIndex + i) % this.apiKeys.length;
            const key = this.apiKeys[keyIndex];

            if (!this.failedKeys.has(key.name)) {
                this.currentKeyIndex = keyIndex;
                return key;
            }
        }

        // Hiç key yoksa ilkini döndür
        return this.apiKeys[0] || null;
    }

    // API key başarısız olduğunda çağrıl
    async markKeyAsFailed(keyName, error) {
        console.log(`Marking API key as failed: ${keyName}, Error: ${error}`);

        this.failedKeys.add(keyName);

        // Eğer başarısız olan key manuel seçilen key ise, manuel seçimi temizle
        if (this.manuallySelectedKey === keyName) {
            console.log(`Manually selected key ${keyName} failed, clearing manual selection`);
            this.manuallySelectedKey = null;
        }

        // Bir sonraki key'e geç (sadece otomatik mod için)
        if (!this.manuallySelectedKey) {
            this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
        }

        // Log kaydet
        await this.logApiKeyEvent(keyName, 'api_failure', 0, error);

        // Yeni aktif key'i log'la
        const newKey = await this.getCurrentApiKey();
        if (newKey && newKey.name !== keyName) {
            const activationType = this.manuallySelectedKey ? 'manual_fallback' : 'auto_activation';
            await this.logApiKeyEvent(newKey.name, activationType, 200, `Otomatik olarak ${keyName} yerine aktif edildi`);
        }
    }

    // API key başarılı olduğunda çağrıl
    async markKeyAsSuccess(keyName, responseTime = null) {
        // Başarılı key'i failed listesinden çıkar
        this.failedKeys.delete(keyName);
        
        // Log kaydet
        await this.logApiKeyEvent(keyName, 'api_success', 200, 'Başarılı istek', responseTime);
    }

    // API key event log
    async logApiKeyEvent(apiKeyName, requestType, status, message, responseTime = null) {
        try {
            const key = this.apiKeys.find(k => k.name === apiKeyName);
            const keyPreview = key ? key.value.substring(0, 8) + '...' + key.value.substring(key.value.length - 4) : 'Bilinmeyen';
            
            await supabase.from('gemini_api_logs').insert([{
                api_key_name: apiKeyName,
                api_key_preview: keyPreview,
                status: status >= 200 && status < 300 ? 'success' : 'error',
                request_type: requestType,
                response_status: status,
                error_message: message,
                response_time_ms: responseTime,
                usage_info: { 
                    failed_keys_count: this.failedKeys.size,
                    total_keys_count: this.apiKeys.length,
                    current_key_index: this.currentKeyIndex
                }
            }]);
        } catch (error) {
            console.error('API log error:', error);
        }
    }

    // Manuel API key seçimi
    async setManualApiKey(keyName) {
        console.log(`🔧 setManualApiKey called with: ${keyName}`);
        console.log(`🔧 Available keys: ${this.apiKeys.map(k => k.name).join(', ')}`);

        const key = this.apiKeys.find(k => k.name === keyName);
        console.log(`🔧 Key found in available keys: ${!!key}`);

        if (key) {
            this.manuallySelectedKey = keyName;
            // Manuel seçim yapıldığında failed listesinden çıkar (eğer varsa)
            this.failedKeys.delete(keyName);
            console.log(`🔧 Set manuallySelectedKey to: ${this.manuallySelectedKey}`);

            // Veritabanına kaydet
            console.log(`🔧 Calling saveManualSelection...`);
            await this.saveManualSelection(keyName);
            console.log(`✅ Manually selected API key: ${keyName} (removed from failed list if present)`);
            return true;
        } else {
            console.log(`❌ API key ${keyName} not found in available keys`);
            return false;
        }
    }

    // Manuel seçimi temizle
    async clearManualSelection() {
        this.manuallySelectedKey = null;
        await this.saveManualSelection(null);
        console.log('Manual API key selection cleared');
    }

    // API key durumunu kontrol et
    getKeyStatus() {
        return {
            totalKeys: this.apiKeys.length,
            failedKeys: Array.from(this.failedKeys),
            currentKeyIndex: this.currentKeyIndex,
            currentKeyName: this.apiKeys[this.currentKeyIndex]?.name || 'None',
            manuallySelectedKey: this.manuallySelectedKey
        };
    }
}

// Global instance
const apiKeyManager = new ApiKeyManager();

// Gemini API çağrısı yapan fonksiyon (geliştirilmiş)
async function callGeminiWithFailover(prompt, maxRetries = 3) {
    let lastError = null;

    // Aktif modeli al
    const modelInfo = await getCurrentGeminiModel();

    for (let attempt = 0; attempt < maxRetries; attempt++) {
        const currentKey = await apiKeyManager.getCurrentApiKey();

        if (!currentKey) {
            throw new Error('Hiç API key tanımlanmamış');
        }

        const startTime = Date.now();

        try {
            console.log(`Attempt ${attempt + 1}: Using API key ${currentKey.name} with model ${modelInfo.model}`);

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelInfo.model}:generateContent?key=${currentKey.value}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.2,
                        topK: 20,
                        topP: 0.8,
                        maxOutputTokens: 4096,
                    },
                    safetySettings: [
                        {
                            category: "HARM_CATEGORY_HARASSMENT",
                            threshold: "BLOCK_NONE",
                        },
                        {
                            category: "HARM_CATEGORY_HATE_SPEECH",
                            threshold: "BLOCK_NONE",
                        },
                        {
                            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            threshold: "BLOCK_NONE",
                        },
                        {
                            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                            threshold: "BLOCK_NONE",
                        }
                    ]
                })
            });

            const responseTime = Date.now() - startTime;
            const result = await response.json();

            if (response.ok && result.candidates && result.candidates[0]) {
                // Başarılı
                await apiKeyManager.markKeyAsSuccess(currentKey.name, responseTime);
                return result.candidates[0].content.parts[0].text;
            } else {
                // API hatası
                const errorMessage = result.error?.message || `HTTP ${response.status}`;
                console.error(`API Error with ${currentKey.name}:`, errorMessage);
                
                // Quota veya rate limit hatası kontrolü
                if (errorMessage.includes('quota') || 
                    errorMessage.includes('rate') || 
                    errorMessage.includes('limit') ||
                    response.status === 429) {
                    
                    await apiKeyManager.markKeyAsFailed(currentKey.name, errorMessage);
                    lastError = new Error(errorMessage);
                    continue; // Bir sonraki key'i dene
                } else {
                    // Diğer hatalar için tekrar deneme
                    throw new Error(errorMessage);
                }
            }
        } catch (error) {
            console.error(`Network error with ${currentKey.name}:`, error.message);
            
            // Network hatası - key'i başarısız olarak işaretle ve devam et
            await apiKeyManager.markKeyAsFailed(currentKey.name, error.message);
            lastError = error;
        }
    }
    
    throw lastError || new Error('Tüm API key\'ler başarısız oldu');
}

// Export edilecek fonksiyonlar
module.exports = {
    apiKeyManager,
    callGeminiWithFailover,
    getCurrentGeminiModel,
    
    // Eski fonksiyon ile uyumluluk için
    async generateSummaryWithGemini(text, apiKey = null) {
        // Eğer spesifik bir API key verilmişse onu kullan, yoksa failover sistemi kullan
        if (apiKey) {
            // Eski sistem - tek API key
            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: `Lütfen aşağıdaki hukuki belgeyi özetleyin. Özet net, anlaşılır ve önemli noktaları içermelidir:\n\n${text}`
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 2048,
                    }
                })
            });

            const result = await response.json();
            
            if (response.ok && result.candidates && result.candidates[0]) {
                return result.candidates[0].content.parts[0].text;
            } else {
                throw new Error(result.error?.message || 'Özet oluşturulamadı');
            }
        } else {
            // Yeni sistem - failover
            const prompt = `Lütfen aşağıdaki hukuki belgeyi özetleyin. Özet net, anlaşılır ve önemli noktaları içermelidir:\n\n${text}`;
            return await callGeminiWithFailover(prompt);
        }
    }
};
