<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Şifre <PERSON>fırla - LegalAI</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="mobile-menu.css">
    <style>
        /* Modern Corporate Design System */
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-900: #1e3a8a;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
        }

        /* Navbar styles are in styles.css and mobile-menu.css */

        /* Main Container */
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6rem 2rem 2rem;
        }

        .reset-wrapper {
            width: 100%;
            max-width: 500px;
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
        }

        .reset-header {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            padding: 2rem;
            text-align: center;
            color: white;
        }

        .reset-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }

        .reset-title {
            font-size: 1.75rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .reset-subtitle {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .reset-form-container {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: 0.75rem;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            background: white;
            color: var(--gray-900);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input::placeholder {
            color: var(--gray-400);
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .submit-button {
            width: 100%;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
            color: white;
            border: none;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
        }

        .submit-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
        }

        .submit-button:disabled {
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
        }

        .submit-button #reset-loading-spinner {
            display: inline-flex;
            align-items: center;
        }

        .submit-button #reset-loading-spinner i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .back-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--gray-200);
        }

        .back-link a {
            color: var(--primary-600);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            text-align: center;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        /* Loading overlay removed - using button spinner only */

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 5rem 1rem 1rem;
            }

            .reset-wrapper {
                margin: 1rem;
                border-radius: 1rem;
            }

            .reset-header {
                padding: 1.5rem;
            }

            .reset-form-container {
                padding: 1.5rem;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .nav-menu {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-balance-scale nav-icon"></i>
                <span class="nav-title">LegalAI</span>
            </div>

            <!-- Desktop Menu -->
            <div class="nav-menu">
                <a href="/" class="nav-link">Ana Sayfa</a>
                <button class="nav-btn" id="user-menu-btn" style="display: none;">
                    <i class="fas fa-user"></i>
                    <span id="user-name"></span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <a href="/apply" class="nav-link" id="register-link">Üye Ol</a>
                <a href="/login" class="nav-link" id="login-link">Giriş Yap</a>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle" type="button" aria-label="Menüyü aç/kapat">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Mobile Menu Overlay -->
        <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

        <!-- Mobile Menu -->
        <div class="mobile-menu" id="mobile-menu">
            <div class="mobile-menu-header">
                <div class="mobile-brand">
                    <i class="fas fa-balance-scale"></i>
                    <span>LegalAI</span>
                </div>
                <button class="mobile-menu-close" id="mobile-menu-close" type="button" aria-label="Menüyü kapat">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mobile-menu-content">
                <div class="mobile-nav-section">
                    <a href="/" class="mobile-nav-link">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </div>

                <!-- Mobile User Menu (when logged in) -->
                <div class="mobile-nav-section" id="mobile-user-section" style="display: none;">
                    <div class="mobile-user-info">
                        <div class="mobile-user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="mobile-user-details">
                            <span class="mobile-user-name" id="mobile-user-name">Kullanıcı</span>
                            <span class="mobile-user-status">Aktif Üye</span>
                        </div>
                    </div>

                    <a href="/profile" class="mobile-nav-link">
                        <i class="fas fa-user-circle"></i>
                        <span>Profil</span>
                    </a>

                    <button class="mobile-nav-link logout-btn" id="mobile-logout-btn" type="button">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Çıkış Yap</span>
                    </button>
                </div>

                <!-- Mobile Auth Buttons (when not logged in) -->
                <div class="mobile-nav-section" id="mobile-auth-section">
                    <div class="mobile-auth-buttons">
                        <a href="/apply" class="mobile-auth-btn primary">
                            <i class="fas fa-user-plus"></i>
                            <span>Üye Ol</span>
                        </a>
                        <a href="/login" class="mobile-auth-btn secondary">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Giriş Yap</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Container -->
    <div class="main-container">
        <div class="reset-wrapper">
            <!-- Header -->
            <div class="reset-header">
                <div class="reset-icon">
                    <i class="fas fa-key"></i>
                </div>
                <h1 class="reset-title">Şifre Sıfırla</h1>
                <p class="reset-subtitle">Hesabınız için yeni bir şifre belirleyin</p>
            </div>
            
            <!-- Form Container -->
            <div class="reset-form-container">
                <div id="success-message" class="success-message" style="display: none;"></div>
                <div id="error-message" class="error-message" style="display: none;"></div>
                
                <form id="reset-form">
                    <div class="form-group">
                        <label for="password" class="form-label">Yeni Şifre</label>
                        <input type="password" id="password" name="password" required 
                               class="form-input" placeholder="En az 6 karakter">
                        <div class="password-strength">
                            Şifreniz en az 6 karakter uzunluğunda olmalıdır.
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm-password" class="form-label">Yeni Şifre Tekrar</label>
                        <input type="password" id="confirm-password" name="confirmPassword" required 
                               class="form-input" placeholder="Şifrenizi tekrar girin">
                    </div>
                    
                    <button type="submit" id="reset-submit" class="submit-button">
                        <span id="reset-btn-text">
                            <i class="fas fa-save" style="margin-right: 0.5rem;"></i>
                            Şifremi Güncelle
                        </span>
                        <span id="reset-loading-spinner" style="display: none; margin-left: 0.5rem;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>
                </form>
                
                <div class="back-link">
                    <a href="/login">
                        <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>
                        Giriş sayfasına dön
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading overlay removed - using button spinner only -->

    <script src="reset-password.js"></script>
    <script src="mobile-menu.js"></script>
</body>
</html>
