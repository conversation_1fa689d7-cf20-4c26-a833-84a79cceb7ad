const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
const { sendEmail } = require('./utils/email-service');

// Initialize clients with error handling
let supabase = null;

function getSupabase() {
    if (!supabase) {
        try {
            // Support both SUPABASE_SERVICE_KEY and SUPABASE_SERVICE_ROLE_KEY
            const serviceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
            supabase = createClient(
                process.env.SUPABASE_URL,
                serviceKey
            );
        } catch (error) {
            console.error('Supabase initialization error:', error);
            throw new Error('Database connection failed');
        }
    }
    return supabase;
}

// getResend fonksiyonu artık gerekli değil - dinamik email service kullanıyoruz

// Rate limiting için basit in-memory store
const rateLimitStore = new Map();

function checkRateLimit(ip, action, maxAttempts = 3, windowMs = 15 * 60 * 1000) {
    const key = `${ip}:${action}`;
    const now = Date.now();
    
    if (!rateLimitStore.has(key)) {
        rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
        return { allowed: true };
    }
    
    const record = rateLimitStore.get(key);
    
    if (now > record.resetTime) {
        rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
        return { allowed: true };
    }
    
    if (record.count >= maxAttempts) {
        return { allowed: false, resetTime: record.resetTime };
    }
    
    record.count++;
    return { allowed: true };
}

function getClientIP(event) {
    return event.headers['x-forwarded-for']?.split(',')[0] || 
           event.headers['x-real-ip'] || 
           'unknown';
}

exports.handler = async (event, context) => {
    console.log('Forgot password function called:', {
        method: event.httpMethod,
        hasBody: !!event.body,
        headers: event.headers
    });

    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Environment variables check
        const serviceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        console.log('Environment check:', {
            hasSupabaseUrl: !!process.env.SUPABASE_URL,
            hasSupabaseServiceKey: !!process.env.SUPABASE_SERVICE_KEY,
            hasSupabaseServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
            hasServiceKey: !!serviceKey,
            hasEmailService: true // Dinamik email service kullanıyoruz
        });

        if (!process.env.SUPABASE_URL || !serviceKey) {
            console.error('Missing Supabase environment variables');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Server configuration error',
                    details: 'Supabase environment variables missing'
                })
            };
        }
        // Rate limiting
        const clientIP = getClientIP(event);
        const rateLimit = checkRateLimit(clientIP, 'forgot-password', 3, 15 * 60 * 1000);

        if (!rateLimit.allowed) {
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({
                    error: 'Çok fazla şifre sıfırlama isteği. 15 dakika sonra tekrar deneyin.',
                    retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
                })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { email } = body;

        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'E-posta adresi gereklidir.' })
            };
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçerli bir e-posta adresi girin.' })
            };
        }

        // Kullanıcıyı kontrol et
        const supabaseClient = getSupabase();
        const { data: user, error: userError } = await supabaseClient
            .from('users')
            .select('id, email, first_name, last_name, status')
            .eq('email', email)
            .single();

        if (userError || !user) {
            // Güvenlik için her zaman başarılı mesajı döndür
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderilecektir.'
                })
            };
        }

        // Kullanıcı durumu kontrol et
        if (user.status !== 'approved') {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderilecektir.'
                })
            };
        }

        // Mevcut aktif token'ları temizle
        await supabaseClient
            .from('password_reset_tokens')
            .delete()
            .eq('user_id', user.id)
            .is('used_at', null);

        // Yeni token oluştur
        const resetToken = crypto.randomBytes(32).toString('hex');
        const tokenHash = crypto.createHash('sha256').update(resetToken).digest('hex');
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 saat

        // Token'ı veritabanına kaydet
        const { error: tokenError } = await supabaseClient
            .from('password_reset_tokens')
            .insert({
                user_id: user.id,
                email: user.email,
                token_hash: tokenHash,
                expires_at: expiresAt.toISOString(),
                ip_address: clientIP,
                user_agent: event.headers['user-agent'] || null
            });

        if (tokenError) {
            console.error('Token save error:', tokenError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Şifre sıfırlama isteği işlenirken hata oluştu.' })
            };
        }

        // Reset URL oluştur
        const resetUrl = `${process.env.URL || 'https://hukukibelgeozetleme.netlify.app'}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;

        // E-posta gönder
        let emailSent = false;
        let emailError = null;

        try {
            const emailHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Şifre Sıfırlama</title>
                </head>
                <body style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 20px;">
                    <div style="max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 40px 30px; text-align: center;">
                            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 800;">LegalAI</h1>
                            <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 16px;">Şifre Sıfırlama</p>
                        </div>
                        
                        <div style="padding: 40px 30px;">
                            <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">Merhaba ${user.first_name},</h2>
                            
                            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.6;">
                                Hesabınız için şifre sıfırlama isteği aldık. Şifrenizi sıfırlamak için aşağıdaki butona tıklayın:
                            </p>
                            
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${resetUrl}" style="display: inline-block; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; text-decoration: none; padding: 14px 28px; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 14px rgba(37, 99, 235, 0.3);">
                                    Şifremi Sıfırla
                                </a>
                            </div>
                            
                            <p style="margin: 20px 0; font-size: 14px; color: #6b7280;">
                                Bu link 1 saat boyunca geçerlidir. Eğer şifre sıfırlama isteği yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.
                            </p>
                            
                            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
                                <p style="margin: 0; font-size: 12px; color: #9ca3af; text-align: center;">
                                    Bu e-posta LegalAI tarafından gönderilmiştir.<br>
                                    Sorularınız için: <EMAIL>
                                </p>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;

            const emailResult = await sendEmail({
                to: email,
                subject: 'Şifre Sıfırlama - LegalAI',
                html: emailHtml,
                text: `Şifre Sıfırlama - LegalAI\n\nMerhaba,\n\nŞifrenizi sıfırlamak için aşağıdaki linke tıklayın:\n${resetUrl}\n\nBu link 1 saat geçerlidir.\n\nEğer bu işlemi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.\n\nLegalAI Ekibi`
            });

            if (emailResult.success) {
                emailSent = true;
                console.log('Password reset email sent via', emailResult.provider, '- Email ID:', emailResult.emailId);
            } else {
                emailError = emailResult.error;
                console.error('Email send error:', emailResult.error);
            }

        } catch (error) {
            console.error('Email send error:', error);
            emailError = error.message;
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: emailSent
                    ? 'Şifre sıfırlama linki e-posta adresinize gönderildi.'
                    : 'Şifre sıfırlama isteği alındı.',
                debug: process.env.NODE_ENV === 'development' ? {
                    resetUrl: resetUrl,
                    emailSent: emailSent,
                    emailError: emailError?.message
                } : undefined
            })
        };

    } catch (error) {
        console.error('Forgot password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Şifre sıfırlama isteği işlenirken hata oluştu.' })
        };
    }
};
