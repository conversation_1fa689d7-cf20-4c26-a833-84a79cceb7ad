const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    // Otomatik sıfırlama devre dışı bırakıldı
    return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
            success: false,
            message: 'Otomatik günlük sıfırlama devre dışı bırakıldı. Sadece webhook ile sıfırlama kullanın.',
            disabled: true
        })
    };

    /* DEVRE DIŞI BIRAKILDI
    try {
        // Token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Yetkilendirme gerekli' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token doğrulama
        let userData;
        try {
            const jwt = require('jsonwebtoken');
            userData = jwt.verify(token, process.env.JWT_SECRET);
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token' })
            };
        }

        // Kullanıcı profilini al
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('daily_summary_count, last_summary_date')
            .eq('id', userData.userId)
            .single();

        if (profileError) {
            console.error('Profile fetch error:', profileError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Profil bilgisi alınamadı' })
            };
        }

        // Bugünün tarihini al (Türkiye saati)
        const today = new Date();
        const turkeyOffset = 3 * 60; // UTC+3 dakika cinsinden
        const turkeyTime = new Date(today.getTime() + (turkeyOffset * 60 * 1000));
        const todayStr = turkeyTime.toISOString().split('T')[0]; // YYYY-MM-DD

        // Son sıfırlama tarihini kontrol et
        const lastResetDate = profile.last_summary_date;

        // Eğer son sıfırlama bugün değilse, sıfırla
        let wasReset = false;
        if (!lastResetDate || lastResetDate !== todayStr) {
            const { error: resetError } = await supabase
                .from('users')
                .update({
                    daily_summary_count: 0,
                    last_summary_date: todayStr
                })
                .eq('id', userData.userId);

            if (resetError) {
                console.error('Auto reset error:', resetError);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Otomatik sıfırlama başarısız' })
                };
            }

            wasReset = true;
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                reset: wasReset,
                message: wasReset ? 'Günlük quota sıfırlandı' : 'Quota zaten güncel'
            })
        };

    } catch (error) {
        console.error('Auto reset daily quota error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası' })
        };
    }
    */ // DEVRE DIŞI BIRAKILDI SONU
};
