// Environment Variable Test Function
exports.handler = async (event, context) => {
    console.log('Test-env function called');

    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ message: 'CORS preflight' })
        };
    }

    try {
        // Environment variables'ları kontrol et
        const envCheck = {
            timestamp: new Date().toISOString(),
            nodeEnv: process.env.NODE_ENV,
            netlifyContext: context.clientContext,
            
            // Environment variables varlık kontrolü
            environmentVariables: {
                SYSTEM_PROMPT: {
                    exists: !!process.env.SYSTEM_PROMPT,
                    length: process.env.SYSTEM_PROMPT ? process.env.SYSTEM_PROMPT.length : 0,
                    preview: process.env.SYSTEM_PROMPT ? process.env.SYSTEM_PROMPT.substring(0, 100) + '...' : 'NOT_FOUND',
                    type: typeof process.env.SYSTEM_PROMPT
                },
                GEMINI_API_KEY: {
                    exists: !!process.env.GEMINI_API_KEY,
                    length: process.env.GEMINI_API_KEY ? process.env.GEMINI_API_KEY.length : 0,
                    preview: process.env.GEMINI_API_KEY ? process.env.GEMINI_API_KEY.substring(0, 10) + '...' : 'NOT_FOUND',
                    type: typeof process.env.GEMINI_API_KEY
                },
                SUPABASE_URL: {
                    exists: !!process.env.SUPABASE_URL,
                    length: process.env.SUPABASE_URL ? process.env.SUPABASE_URL.length : 0,
                    preview: process.env.SUPABASE_URL ? process.env.SUPABASE_URL.substring(0, 30) + '...' : 'NOT_FOUND',
                    type: typeof process.env.SUPABASE_URL
                },
                SUPABASE_SERVICE_KEY: {
                    exists: !!process.env.SUPABASE_SERVICE_KEY,
                    length: process.env.SUPABASE_SERVICE_KEY ? process.env.SUPABASE_SERVICE_KEY.length : 0,
                    preview: process.env.SUPABASE_SERVICE_KEY ? process.env.SUPABASE_SERVICE_KEY.substring(0, 10) + '...' : 'NOT_FOUND',
                    type: typeof process.env.SUPABASE_SERVICE_KEY
                }
            },

            // Tüm environment variables listesi (hassas olanlar hariç)
            allEnvKeys: Object.keys(process.env).filter(key => 
                !key.includes('KEY') && 
                !key.includes('SECRET') && 
                !key.includes('PASSWORD') &&
                !key.includes('TOKEN')
            ),

            // Function context bilgileri
            functionInfo: {
                functionName: context.functionName,
                functionVersion: context.functionVersion,
                invokedFunctionArn: context.invokedFunctionArn,
                memoryLimitInMB: context.memoryLimitInMB,
                remainingTimeInMillis: context.getRemainingTimeInMillis ? context.getRemainingTimeInMillis() : 'N/A'
            }
        };

        // Kritik kontroller
        const criticalIssues = [];
        
        if (!process.env.SYSTEM_PROMPT) {
            criticalIssues.push('SYSTEM_PROMPT environment variable bulunamadı!');
        }
        
        if (!process.env.GEMINI_API_KEY) {
            criticalIssues.push('GEMINI_API_KEY environment variable bulunamadı!');
        }
        
        if (!process.env.SUPABASE_URL) {
            criticalIssues.push('SUPABASE_URL environment variable bulunamadı!');
        }

        // Test sonucu
        const testResult = {
            success: criticalIssues.length === 0,
            message: criticalIssues.length === 0 ? 
                'Tüm environment variables başarıyla yüklendi!' : 
                'Bazı environment variables eksik!',
            criticalIssues,
            details: envCheck
        };

        console.log('Environment test result:', testResult);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(testResult)
        };

    } catch (error) {
        console.error('Environment test error:', error);

        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Environment test failed',
                message: error.message,
                stack: error.stack
            })
        };
    }
};
