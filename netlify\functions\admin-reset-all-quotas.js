const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing environment variables:', {
        SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
        SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY
    });
}

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    };

    // Environment variables kontrolü
    if (!supabaseUrl || !supabaseKey) {
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Server configuration error',
                details: 'Missing required environment variables'
            })
        };
    }

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Yetkilendirme gerekli' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token doğrulama
        let adminData;
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                adminId: decoded.adminId,
                email: decoded.email,
                role: decoded.role
            };
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token' })
            };
        }

        console.log('Admin resetting all quotas:', adminData.email);

        // Tüm aktif kullanıcıları al
        const { data: users, error: usersError } = await supabase
            .from('users')
            .select('id, email, first_name, last_name, daily_summary_count')
            .eq('status', 'approved');

        if (usersError) {
            console.error('Users fetch error:', usersError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Kullanıcılar getirilemedi',
                    details: usersError.message
                })
            };
        }

        console.log(`Found ${users.length} active users to reset`);

        // Tüm kullanıcıların haklarını sıfırla
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        
        const { data: resetData, error: resetError } = await supabase
            .from('users')
            .update({
                daily_summary_count: 0,
                last_summary_date: today
            })
            .eq('status', 'approved')
            .select('id, email, first_name, last_name');

        if (resetError) {
            console.error('Bulk reset error:', resetError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Toplu hak sıfırlama işlemi başarısız oldu',
                    details: resetError.message
                })
            };
        }

        console.log(`Successfully reset quotas for ${resetData.length} users`);

        // Admin log ekle
        try {
            const clientIP = event.headers['x-forwarded-for'] || 
                           event.headers['x-real-ip'] || 
                           event.requestContext?.identity?.sourceIp || 
                           'unknown';
            const userAgent = event.headers['user-agent'] || 'unknown';

            await supabase.from('admin_logs').insert([{
                admin_id: adminData.adminId,
                admin_email: adminData.email,
                action: 'reset_all_quotas',
                target_type: 'users',
                target_id: 'all_approved_users',
                details: { 
                    affected_users: resetData.length,
                    reset_date: today,
                    user_list: resetData.map(u => ({ id: u.id, email: u.email }))
                },
                ip_address: clientIP,
                user_agent: userAgent
            }]);
        } catch (logError) {
            console.error('Admin log error:', logError);
            // Log hatası ana işlemi etkilemesin
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: `${resetData.length} kullanıcının günlük özet hakkı başarıyla sıfırlandı`,
                affected_users: resetData.length,
                reset_date: today,
                users: resetData.map(u => ({
                    id: u.id,
                    email: u.email,
                    name: `${u.first_name} ${u.last_name}`
                }))
            })
        };

    } catch (error) {
        console.error('Admin reset all quotas error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası',
                details: error.message
            })
        };
    }
};
