// Admin kull<PERSON> yönetimi function'ı
const { createClient } = require('@supabase/supabase-js');
const { verifyAdminToken } = require('./admin-verify-token');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

// IP adresini al
function getClientIP(event) {
    return event.headers['x-forwarded-for'] ||
           event.headers['x-real-ip'] ||
           event.requestContext?.identity?.sourceIp ||
           'unknown';
}

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    const clientIP = getClientIP(event);
    const userAgent = event.headers['user-agent'] || 'unknown';

    try {
        // Admin token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Admin token gerekli.' })
            };
        }

        const token = authHeader.substring(7);

        // Basit token kontrolü
        let adminData;
        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                id: decoded.adminId,
                email: decoded.email
            };
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // GET - Kullanıcıları listele (sadece admin tarafından onaylanmış olanlar)
        if (event.httpMethod === 'GET') {
            // Sadece admin tarafından onaylanmış kullanıcıları getir
            // Bu kullanıcılar applications tablosundan admin-applications.js ile taşınmış olanlardır
            const { data: users, error } = await supabase
                .from('users')
                .select('*')
                .eq('status', 'approved') // Sadece approved olanlar (admin onaylamış)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Users fetch error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcılar getirilemedi.' })
                };
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({ users })
            };
        }

        // PUT - Kullanıcı durumu güncelle
        if (event.httpMethod === 'PUT') {
            console.log('PUT request received');
            const body = JSON.parse(event.body || '{}');
            const { userId, action } = body;
            console.log('PUT data:', { userId, action });

            if (!userId || !action) {
                console.log('Missing required fields');
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcı ID ve aksiyon gerekli.' })
                };
            }

            let newStatus;
            if (action === 'approve') {
                newStatus = 'approved';
            } else if (action === 'reject') {
                newStatus = 'suspended'; // Reddedilen kullanıcılar suspended olur
            } else if (action === 'suspend') {
                newStatus = 'suspended';
            } else if (action === 'activate') {
                newStatus = 'approved';
            } else {
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Geçersiz aksiyon.' })
                };
            }

            // Kullanıcı bilgilerini al (e-posta gönderimi için)
            const { data: user, error: userFetchError } = await supabase
                .from('users')
                .select('email, first_name, last_name, status')
                .eq('id', userId)
                .single();

            if (userFetchError || !user) {
                return {
                    statusCode: 404,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcı bulunamadı.' })
                };
            }

            console.log('Updating user status:', { userId, newStatus });
            const { error } = await supabase
                .from('users')
                .update({ status: newStatus })
                .eq('id', userId);

            console.log('Update result:', { error });

            if (error) {
                console.error('User update error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        error: 'Kullanıcı durumu güncellenemedi.',
                        details: error.message
                    })
                };
            }

            // Admin log ekle
            try {
                await supabase.from('admin_logs').insert([{
                    admin_username: adminData.email,
                    admin_email: adminData.email,
                    action: `user_${action}`,
                    target_id: userId,
                    target_type: 'user',
                    details: { newStatus, ip: clientIP },
                    ip_address: clientIP,
                    user_agent: userAgent
                }]);
            } catch (logError) {
                console.error('Log insert error:', logError);
            }

            // Eğer kullanıcı onaylandıysa e-posta gönder
            console.log('Email sending check:', {
                action: action,
                userCurrentStatus: user.status,
                newStatus: newStatus,
                shouldSendEmail: (action === 'approve' || action === 'activate') && user.status !== 'approved'
            });

            if ((action === 'approve' || action === 'activate') && user.status !== 'approved') {
                try {
                    // DETAYLI LOGLAMA - SORUN TESPİTİ İÇİN
                    console.log('=== USER EMAIL SENDING DEBUG INFO ===');
                    console.log('Request userId:', userId);
                    console.log('Fetched user ID:', user.id || 'NO ID IN USER OBJECT');
                    console.log('User email:', user.email);
                    console.log('User first_name:', user.first_name);
                    console.log('User last_name:', user.last_name);
                    console.log('User status:', user.status);
                    console.log('Full user object:', JSON.stringify(user, null, 2));
                    console.log('=== END USER DEBUG INFO ===');

                    const emailPayload = {
                        email: user.email,
                        firstName: user.first_name,
                        lastName: user.last_name
                    };

                    console.log('User email payload being sent:', JSON.stringify(emailPayload, null, 2));

                    // GÜVENLIK KONTROLÜ: Email adresinin user ID ile eşleştiğini doğrula
                    const { data: verifyUser, error: verifyError } = await supabase
                        .from('users')
                        .select('email, first_name, last_name')
                        .eq('id', userId)
                        .single();

                    if (verifyError || !verifyUser) {
                        console.error('USER VERIFICATION FAILED: Cannot re-fetch user for verification');
                        throw new Error('User email verification failed');
                    }

                    if (verifyUser.email !== emailPayload.email ||
                        verifyUser.first_name !== emailPayload.firstName ||
                        verifyUser.last_name !== emailPayload.lastName) {
                        console.error('CRITICAL USER SECURITY ERROR: Email payload mismatch!', {
                            original: emailPayload,
                            verified: verifyUser,
                            userId: userId
                        });
                        throw new Error('User email data mismatch detected - email sending aborted for security');
                    }

                    console.log('✅ User email payload verified successfully');

                    const emailResponse = await fetch(`${event.headers.origin || 'https://hukukibelgeozetleme.netlify.app'}/.netlify/functions/send-approval-email`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(emailPayload)
                    });

                    if (emailResponse.ok) {
                        console.log('Approval email sent successfully');
                    } else {
                        console.warn('Failed to send approval email:', await emailResponse.text());
                    }
                } catch (emailError) {
                    console.error('Email sending error:', emailError);
                    // E-posta hatası kullanıcı güncelleme işlemini etkilemesin
                }
            } else {
                console.log('Email not sent because:', {
                    action: action,
                    userStatus: user.status,
                    reason: action !== 'approve' ? 'Action is not approve' : 'User already approved'
                });
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: action === 'approve' ? 'Kullanıcı onaylandı ve bilgilendirme e-postası gönderildi.' :
                            action === 'suspend' ? 'Kullanıcı askıya alındı.' : 'Kullanıcı aktifleştirildi.'
                })
            };
        }

        // DELETE - Kullanıcı sil
        if (event.httpMethod === 'DELETE') {
            console.log('DELETE request received');
            const body = JSON.parse(event.body || '{}');
            const { userId } = body;
            console.log('DELETE data:', { userId });

            if (!userId) {
                console.log('Missing userId');
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Kullanıcı ID gerekli.' })
                };
            }

            console.log('Deleting user:', userId);
            const { error } = await supabase
                .from('users')
                .delete()
                .eq('id', userId);

            console.log('Delete result:', { error });

            if (error) {
                console.error('User delete error:', error);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        error: 'Kullanıcı silinemedi.',
                        details: error.message
                    })
                };
            }

            // Admin log ekle
            try {
                await supabase.from('admin_logs').insert([{
                    admin_username: adminData.email,
                    admin_email: adminData.email,
                    action: 'user_delete',
                    target_id: userId,
                    target_type: 'user',
                    details: { ip: clientIP },
                    ip_address: clientIP,
                    user_agent: userAgent
                }]);
            } catch (logError) {
                console.error('Log insert error:', logError);
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({ 
                    success: true, 
                    message: 'Kullanıcı silindi.' 
                })
            };
        }

        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };

    } catch (error) {
        console.error('Admin users error:', error);
        console.error('Error stack:', error.stack);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası.',
                details: error.message,
                stack: error.stack
            })
        };
    }
};
