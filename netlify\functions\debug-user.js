// Debug User - <PERSON><PERSON>ıcı durumunu kontrol et
const { createClient } = require('@supabase/supabase-js');

// Supabase client
function getSupabase() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing');
    }
    
    return createClient(supabaseUrl, supabaseKey);
}

exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    try {
        const { email } = JSON.parse(event.body || '{}');
        
        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Email required' })
            };
        }

        const supabase = getSupabase();
        
        // Users tablosunda ara
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .single();
            
        // Applications tablosunda ara
        const { data: application, error: appError } = await supabase
            .from('applications')
            .select('*')
            .eq('email', email)
            .single();
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                debug_info: {
                    email: email,
                    user_found: !!user,
                    user_error: userError?.message,
                    user_data: user ? {
                        id: user.id,
                        email: user.email,
                        status: user.status,
                        email_verified_at: user.email_verified_at,
                        has_email_verification: !!user.email_verified_at,
                        created_at: user.created_at
                    } : null,
                    application_found: !!application,
                    application_error: appError?.message,
                    application_data: application ? {
                        email: application.email,
                        status: application.status,
                        email_verified_at: application.email_verified_at,
                        has_email_verification: !!application.email_verified_at,
                        created_at: application.created_at
                    } : null
                }
            })
        };
        
    } catch (error) {
        console.error('Debug user error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: error.message,
                stack: error.stack
            })
        };
    }
};
