const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing environment variables:', {
        SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
        SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY
    });
}

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Yetkilendirme gerekli' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token doğrulama
        let adminData;
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                adminId: decoded.adminId,
                email: decoded.email,
                role: decoded.role
            };
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token' })
            };
        }

        if (event.httpMethod === 'GET') {
            // Model ayarlarını getir
            const { data: settings, error: settingsError } = await supabase
                .from('system_settings')
                .select('*')
                .eq('setting_key', 'gemini_model')
                .eq('is_active', true)
                .single();

            if (settingsError) {
                console.error('Settings fetch error:', settingsError);
                return {
                    statusCode: 500,
                    headers,
                    body: JSON.stringify({
                        error: 'Model ayarları getirilemedi',
                        details: settingsError.message
                    })
                };
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    currentModel: settings.setting_value,
                    displayName: settings.options[settings.setting_value] || settings.setting_value,
                    availableModels: settings.options,
                    description: settings.description
                })
            };
        }

        if (event.httpMethod === 'POST') {
            const requestBody = JSON.parse(event.body);
            const { action, modelName } = requestBody;

            if (action === 'update_model') {
                // Model güncelle
                if (!modelName) {
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'Model ismi gerekli' })
                    };
                }

                // Önce mevcut ayarı getir
                const { data: currentSetting, error: fetchError } = await supabase
                    .from('system_settings')
                    .select('*')
                    .eq('setting_key', 'gemini_model')
                    .single();

                if (fetchError) {
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'Mevcut ayar getirilemedi',
                            details: fetchError.message
                        })
                    };
                }

                // Model isminin geçerli olup olmadığını kontrol et
                if (!currentSetting.options[modelName]) {
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({
                            error: 'Geçersiz model ismi',
                            availableModels: Object.keys(currentSetting.options)
                        })
                    };
                }

                // Model güncelle
                const { error: updateError } = await supabase
                    .from('system_settings')
                    .update({
                        setting_value: modelName,
                        updated_at: new Date().toISOString()
                    })
                    .eq('setting_key', 'gemini_model');

                if (updateError) {
                    console.error('Model update error:', updateError);
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'Model güncellenemedi',
                            details: updateError.message
                        })
                    };
                }

                // Admin log ekle
                try {
                    const clientIP = event.headers['x-forwarded-for'] || 
                                   event.headers['x-real-ip'] || 
                                   event.requestContext?.identity?.sourceIp || 
                                   'unknown';
                    const userAgent = event.headers['user-agent'] || 'unknown';

                    await supabase.from('admin_logs').insert([{
                        admin_id: adminData.adminId,
                        admin_email: adminData.email,
                        action: 'update_gemini_model',
                        target_type: 'system_settings',
                        target_id: 'gemini_model',
                        details: { 
                            old_model: currentSetting.setting_value,
                            new_model: modelName,
                            model_display_name: currentSetting.options[modelName]
                        },
                        ip_address: clientIP,
                        user_agent: userAgent
                    }]);
                } catch (logError) {
                    console.error('Admin log error:', logError);
                    // Log hatası ana işlemi etkilemesin
                }

                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify({
                        success: true,
                        message: `Model başarıyla ${currentSetting.options[modelName]} olarak güncellendi`,
                        newModel: modelName,
                        displayName: currentSetting.options[modelName]
                    })
                };
            }

            if (action === 'test_model') {
                // Seçilen modeli test et
                if (!modelName) {
                    return {
                        statusCode: 400,
                        headers,
                        body: JSON.stringify({ error: 'Model ismi gerekli' })
                    };
                }

                try {
                    // Test API key'i al (ilk mevcut key'i kullan)
                    let testApiKey = null;
                    for (let i = 1; i <= 20; i++) {
                        const keyName = i === 1 ? 'GEMINI_API_KEY' : `GEMINI_API_KEY${i}`;
                        const keyValue = process.env[keyName];
                        if (keyValue) {
                            testApiKey = keyValue;
                            break;
                        }
                    }

                    if (!testApiKey) {
                        return {
                            statusCode: 500,
                            headers,
                            body: JSON.stringify({ error: 'Test için API key bulunamadı' })
                        };
                    }

                    // Model test et
                    const startTime = Date.now();
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${testApiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: "Test mesajı - bu model çalışıyor mu?"
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.2,
                                topK: 20,
                                topP: 0.8,
                                maxOutputTokens: 100,
                            }
                        })
                    });

                    const responseTime = Date.now() - startTime;
                    const result = await response.json();

                    if (response.ok && result.candidates && result.candidates[0]) {
                        return {
                            statusCode: 200,
                            headers,
                            body: JSON.stringify({
                                success: true,
                                message: `${modelName} modeli başarıyla test edildi`,
                                responseTime: responseTime,
                                testResponse: result.candidates[0].content.parts[0].text.substring(0, 100) + '...'
                            })
                        };
                    } else {
                        return {
                            statusCode: 400,
                            headers,
                            body: JSON.stringify({
                                error: `${modelName} modeli test başarısız`,
                                details: result.error?.message || 'Bilinmeyen hata',
                                responseTime: responseTime
                            })
                        };
                    }
                } catch (error) {
                    return {
                        statusCode: 500,
                        headers,
                        body: JSON.stringify({
                            error: 'Model test edilirken hata oluştu',
                            details: error.message
                        })
                    };
                }
            }
        }

        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };

    } catch (error) {
        console.error('Model settings error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası',
                details: error.message
            })
        };
    }
};
