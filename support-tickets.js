// Destek Kayıtları Sayfası JavaScript

let userToken = null;
let userData = null;
let allTickets = [];
let filteredTickets = [];

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    checkAuthentication();
    setupEventListeners();
    loadTickets();
});

// Kullanıcı kimlik doğrulaması kontrolü
function checkAuthentication() {
    userToken = localStorage.getItem('userToken');
    const userDataStr = localStorage.getItem('userData');
    
    if (!userToken || !userDataStr) {
        window.location.href = '/login';
        return;
    }
    
    try {
        userData = JSON.parse(userDataStr);
        console.log('User authenticated:', userData);
    } catch (e) {
        console.error('Invalid user data:', e);
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        window.location.href = '/login';
    }
}

// Event listener'ları ayarla
function setupEventListeners() {
    // Filter değişikliklerini dinle
    document.getElementById('status-filter').addEventListener('change', applyFilters);
    document.getElementById('priority-filter').addEventListener('change', applyFilters);
    document.getElementById('sort-filter').addEventListener('change', applyFilters);
    
    // Modal dışına tıklandığında kapat
    document.getElementById('ticket-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeTicketModal();
        }
    });
    
    // ESC tuşu ile modal kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeTicketModal();
        }
    });
}

// Destek kayıtlarını yükle
async function loadTickets() {
    try {
        showLoading(true);
        
        const response = await fetch('/.netlify/functions/support-user-tickets', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            allTickets = data.tickets || [];
            applyFilters();
        } else {
            const errorData = await response.json();
            console.error('Failed to load tickets:', errorData);
            showError('Destek kayıtları yüklenemedi.');
        }
    } catch (error) {
        console.error('Error loading tickets:', error);
        showError('Destek kayıtları yüklenirken bir hata oluştu.');
    } finally {
        showLoading(false);
    }
}

// Filtreleri uygula
function applyFilters() {
    const statusFilter = document.getElementById('status-filter').value;
    const priorityFilter = document.getElementById('priority-filter').value;
    const sortFilter = document.getElementById('sort-filter').value;
    
    // Filtreleme
    filteredTickets = allTickets.filter(ticket => {
        const statusMatch = !statusFilter || ticket.status === statusFilter;
        const priorityMatch = !priorityFilter || ticket.priority === priorityFilter;
        return statusMatch && priorityMatch;
    });
    
    // Sıralama
    filteredTickets.sort((a, b) => {
        switch (sortFilter) {
            case 'oldest':
                return new Date(a.created_at) - new Date(b.created_at);
            case 'priority':
                const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            case 'status':
                return a.status.localeCompare(b.status);
            case 'newest':
            default:
                return new Date(b.created_at) - new Date(a.created_at);
        }
    });
    
    displayTickets();
}

// Destek kayıtlarını görüntüle
function displayTickets() {
    const ticketsGrid = document.getElementById('tickets-grid');
    const emptyState = document.getElementById('empty-state');
    
    if (filteredTickets.length === 0) {
        ticketsGrid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    ticketsGrid.style.display = 'grid';
    
    ticketsGrid.innerHTML = filteredTickets.map(ticket => createTicketCard(ticket)).join('');
}

// Ticket kartı oluştur
function createTicketCard(ticket) {
    const createdDate = new Date(ticket.created_at).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const categoryColor = ticket.support_categories?.color || '#667eea';
    const categoryIcon = ticket.support_categories?.icon || 'fas fa-question-circle';
    const categoryName = ticket.support_categories?.name || 'Genel';
    
    return `
        <div class="ticket-card" onclick="openTicketModal('${ticket.id}')">
            <div class="ticket-header">
                <div class="ticket-info">
                    <div class="ticket-number">#${ticket.ticket_number}</div>
                    <div class="ticket-subject">${escapeHtml(ticket.subject)}</div>
                    <div class="ticket-meta">
                        <div class="ticket-category" style="background-color: ${categoryColor}">
                            <i class="${categoryIcon}"></i>
                            ${categoryName}
                        </div>
                        <div class="ticket-date">
                            <i class="fas fa-clock"></i>
                            ${createdDate}
                        </div>
                    </div>
                </div>
                <div class="ticket-badges">
                    <div class="status-badge status-${ticket.status}">
                        ${getStatusText(ticket.status)}
                    </div>
                    <div class="priority-badge priority-${ticket.priority}">
                        ${getPriorityText(ticket.priority)}
                    </div>
                </div>
            </div>
            <div class="ticket-description">
                ${escapeHtml(ticket.description)}
            </div>
            <div class="reply-count">
                <i class="fas fa-comments"></i>
                ${ticket.reply_count || 0} yanıt
                ${ticket.last_reply_by === 'admin' && !ticket.is_read_by_user ? 
                    '<span style="color: #e53e3e; font-weight: 600; margin-left: 10px;">• Yeni yanıt</span>' : ''}
            </div>
        </div>
    `;
}

// Durum metni
function getStatusText(status) {
    const statusTexts = {
        open: 'Açık',
        in_progress: 'Devam Ediyor',
        waiting: 'Beklemede',
        resolved: 'Çözüldü',
        closed: 'Kapatıldı'
    };
    return statusTexts[status] || status;
}

// Öncelik metni
function getPriorityText(priority) {
    const priorityTexts = {
        low: 'Düşük',
        normal: 'Normal',
        high: 'Yüksek',
        urgent: 'Acil'
    };
    return priorityTexts[priority] || priority;
}

// HTML escape
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Loading durumunu göster/gizle
function showLoading(show) {
    const loadingState = document.getElementById('loading-state');
    const ticketsGrid = document.getElementById('tickets-grid');
    const emptyState = document.getElementById('empty-state');
    
    if (show) {
        loadingState.style.display = 'block';
        ticketsGrid.style.display = 'none';
        emptyState.style.display = 'none';
    } else {
        loadingState.style.display = 'none';
    }
}

// Hata mesajı göster
function showError(message) {
    alert(message); // Basit hata gösterimi, daha sonra toast notification eklenebilir
}

// Ticket modal'ını aç
async function openTicketModal(ticketId) {
    try {
        const modal = document.getElementById('ticket-modal');
        const modalBody = document.getElementById('modal-body');
        
        // Loading göster
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div class="loading-spinner" style="margin: 0 auto 20px;"></div>
                <p>Ticket detayları yükleniyor...</p>
            </div>
        `;
        
        modal.style.display = 'flex';
        
        // Ticket detaylarını getir
        const response = await fetch(`/.netlify/functions/support-ticket-details?id=${ticketId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            displayTicketDetails(data.ticket);
        } else {
            const errorData = await response.json();
            modalBody.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #e53e3e;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <p>Ticket detayları yüklenemedi: ${errorData.error}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading ticket details:', error);
        const modalBody = document.getElementById('modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #e53e3e;">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <p>Ticket detayları yüklenirken bir hata oluştu.</p>
            </div>
        `;
    }
}

// Ticket detaylarını göster
function displayTicketDetails(ticket) {
    const modalBody = document.getElementById('modal-body');
    const modalTitle = document.getElementById('modal-title');
    
    modalTitle.textContent = `#${ticket.ticket_number} - ${ticket.subject}`;
    
    const createdDate = new Date(ticket.created_at).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const categoryColor = ticket.support_categories?.color || '#667eea';
    const categoryIcon = ticket.support_categories?.icon || 'fas fa-question-circle';
    const categoryName = ticket.support_categories?.name || 'Genel';
    
    modalBody.innerHTML = `
        <div style="margin-bottom: 25px;">
            <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 15px; flex-wrap: wrap;">
                <div class="status-badge status-${ticket.status}">
                    ${getStatusText(ticket.status)}
                </div>
                <div class="priority-badge priority-${ticket.priority}">
                    ${getPriorityText(ticket.priority)}
                </div>
                <div class="ticket-category" style="background-color: ${categoryColor}">
                    <i class="${categoryIcon}"></i>
                    ${categoryName}
                </div>
            </div>
            <p style="color: #718096; margin: 0;">
                <i class="fas fa-clock"></i>
                Oluşturulma: ${createdDate}
            </p>
        </div>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
            <h4 style="margin: 0 0 10px 0; color: #2d3748;">İlk Mesaj:</h4>
            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${escapeHtml(ticket.description)}</p>
        </div>
        
        <div id="ticket-replies">
            <!-- Yanıtlar buraya yüklenecek -->
        </div>
        
        <div style="margin-top: 25px; padding-top: 20px; border-top: 2px solid #e2e8f0;">
            <h4 style="margin: 0 0 15px 0; color: #2d3748;">Yanıt Ekle:</h4>
            <form id="reply-form" onsubmit="submitReply(event, '${ticket.id}')">
                <textarea id="reply-message" placeholder="Yanıtınızı buraya yazın..." 
                         style="width: 100%; min-height: 100px; padding: 15px; border: 2px solid #e2e8f0; border-radius: 10px; font-family: inherit; resize: vertical;" 
                         required maxlength="2000"></textarea>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                    <small style="color: #718096;">
                        <span id="reply-char-count">0</span>/2000 karakter
                    </small>
                    <button type="submit" class="btn btn-primary" id="reply-submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        Yanıt Gönder
                    </button>
                </div>
            </form>
        </div>
    `;
    
    // Yanıtları yükle
    loadTicketReplies(ticket.id);
    
    // Karakter sayacını ayarla
    const replyTextarea = document.getElementById('reply-message');
    const charCount = document.getElementById('reply-char-count');
    
    replyTextarea.addEventListener('input', function() {
        charCount.textContent = this.value.length;
        if (this.value.length > 1900) {
            charCount.style.color = '#e53e3e';
        } else {
            charCount.style.color = '#718096';
        }
    });
}

// Modal'ı kapat
function closeTicketModal() {
    document.getElementById('ticket-modal').style.display = 'none';
}

// Ticket yanıtlarını yükle
async function loadTicketReplies(ticketId) {
    const repliesContainer = document.getElementById('ticket-replies');

    try {
        const response = await fetch(`/.netlify/functions/support-ticket-details?id=${ticketId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayTicketReplies(data.ticket.replies || []);
        } else {
            repliesContainer.innerHTML = '<p style="color: #e53e3e; text-align: center;">Yanıtlar yüklenemedi.</p>';
        }
    } catch (error) {
        console.error('Error loading replies:', error);
        repliesContainer.innerHTML = '<p style="color: #e53e3e; text-align: center;">Yanıtlar yüklenirken hata oluştu.</p>';
    }
}

// Yanıtları görüntüle
function displayTicketReplies(replies) {
    const repliesContainer = document.getElementById('ticket-replies');

    if (!replies || replies.length === 0) {
        repliesContainer.innerHTML = '<p style="color: #718096; text-align: center;">Henüz yanıt bulunmuyor.</p>';
        return;
    }

    repliesContainer.innerHTML = replies.map(reply => {
        const replyDate = new Date(reply.created_at).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        const isAdmin = reply.is_admin_reply;
        const bgColor = isAdmin ? '#f0f9ff' : '#f8f9fa';
        const borderColor = isAdmin ? '#0ea5e9' : '#e2e8f0';
        const authorColor = isAdmin ? '#0ea5e9' : '#4a5568';

        return `
            <div style="background: ${bgColor}; border-left: 4px solid ${borderColor}; padding: 15px; margin-bottom: 15px; border-radius: 0 8px 8px 0;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div style="font-weight: 600; color: ${authorColor}; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-${isAdmin ? 'user-tie' : 'user'}"></i>
                        ${escapeHtml(reply.author_name)}
                        ${isAdmin ? '<span style="background: #0ea5e9; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7rem;">DESTEK EKİBİ</span>' : ''}
                    </div>
                    <div style="color: #718096; font-size: 0.875rem;">
                        <i class="fas fa-clock"></i>
                        ${replyDate}
                    </div>
                </div>
                <div style="color: #2d3748; line-height: 1.6; white-space: pre-wrap;">
                    ${escapeHtml(reply.message)}
                </div>
            </div>
        `;
    }).join('');
}

// Yanıt gönder
async function submitReply(event, ticketId) {
    event.preventDefault();

    const messageTextarea = document.getElementById('reply-message');
    const submitBtn = document.getElementById('reply-submit-btn');
    const message = messageTextarea.value.trim();

    if (!message) {
        showError('Lütfen bir mesaj yazın.');
        return;
    }

    if (message.length < 5) {
        showError('Mesaj en az 5 karakter olmalıdır.');
        return;
    }

    if (message.length > 2000) {
        showError('Mesaj 2000 karakterden uzun olamaz.');
        return;
    }

    // Buton durumunu güncelle
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('/.netlify/functions/support-add-reply', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ticketId: ticketId,
                message: message,
                isAdminReply: false
            })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            // Mesajı temizle
            messageTextarea.value = '';
            document.getElementById('reply-char-count').textContent = '0';

            // Yanıtları yeniden yükle
            loadTicketReplies(ticketId);

            // Ana listeyi de yenile
            loadTickets();

            showSuccess('Yanıtınız başarıyla gönderildi.');
        } else {
            showError(data.error || 'Yanıt gönderilemedi.');
        }

    } catch (error) {
        console.error('Error submitting reply:', error);
        showError('Yanıt gönderilirken bir hata oluştu.');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Başarı mesajı göster
function showSuccess(message) {
    // Basit başarı gösterimi, daha sonra toast notification eklenebilir
    alert(message);
}
