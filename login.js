// Login Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // FontAwesome kontrolünü devre dışı bırak - modern CSS ile iconlar çalışıyor
    // checkFontAwesome();
    setupLoginForm();
    checkExistingAuth();
});

// FontAwesome fallback sistemi kaldırıldı - Modern CSS ile iconlar çalışıyor

function checkExistingAuth() {
    const token = localStorage.getItem('userToken');
    if (token) {
        try {
            // JWT token decode
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
                let payload = tokenParts[1];
                while (payload.length % 4) {
                    payload += '=';
                }
                const userData = JSON.parse(atob(payload));

                // Token süresi kontrol et (saniye cinsinden)
                if (userData.exp && userData.exp > Math.floor(Date.now() / 1000)) {
                    // User already logged in, redirect to home
                    window.location.href = '/';
                    return;
                }
            }
        } catch (e) {
            console.warn('Token decode error in login check:', e.message);
        }
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
    }
}

function setupLoginForm() {
    const form = document.getElementById('login-form');
    const forgotPasswordLink = document.getElementById('forgot-password-link');

    form.addEventListener('submit', handleLogin);
    forgotPasswordLink.addEventListener('click', handleForgotPassword);
}

async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = document.getElementById('login-submit');
    const btnText = document.getElementById('btn-text');
    const spinner = document.getElementById('loading-spinner');
    
    // Show loading
    btnText.style.display = 'none';
    spinner.style.display = 'flex';
    submitBtn.disabled = true;
    
    try {
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password')
        };

        console.log('Login attempt:', loginData);

        const response = await fetch('/.netlify/functions/user-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });

        const data = await response.json();
        console.log('🔍 Login response DEBUG:', {
            status: response.status,
            data: data,
            hasType: !!data.type,
            type: data.type,
            hasError: !!data.error,
            error: data.error,
            hasEmail: !!data.email,
            email: data.email
        });

        if (data.success) {
            // Save token and user data
            localStorage.setItem('userToken', data.token);
            localStorage.setItem('userData', JSON.stringify(data.user));

            // Redirect to home page
            window.location.href = '/';
        } else {
            console.log('🚨 Login failed, checking error type:', data.type);

            // Özel hata tipine göre mesaj göster
            if (data.type === 'email_verification_required') {
                console.log('✉️ Showing email verification error');
                showEmailVerificationError(data.error, data.email);
            } else if (data.type === 'admin_approval_required') {
                console.log('⏳ Showing admin approval error');
                showAdminApprovalError(data.error, data.email);
            } else if (data.type === 'application_rejected') {
                console.log('❌ Showing rejection error');
                showRejectionError(data.error, data.email);
            } else {
                console.log('⚠️ Showing generic error:', data.error);
                showError(data.error || 'Giriş başarısız.');
            }
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showError('Giriş yapılırken hata oluştu.');
    } finally {
        // Hide loading
        btnText.style.display = 'flex';
        spinner.style.display = 'none';
        submitBtn.disabled = false;
    }
}

function showError(message) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; gap: 10px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 1.2rem; color: #dc2626;"></i>
            <span>${message}</span>
        </div>
    `;
    errorDiv.style.display = 'block';
    errorDiv.style.background = '#fef2f2';
    errorDiv.style.border = '2px solid #fecaca';
    errorDiv.style.borderRadius = '12px';
    errorDiv.style.color = '#dc2626';
    errorDiv.style.padding = '20px';
    errorDiv.style.marginTop = '15px';
    errorDiv.style.marginBottom = '15px';
    errorDiv.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';

    // Scroll to error message
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Hide after 10 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 10000);
}

function showEmailVerificationError(message, email) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.innerHTML = `
        <div style="display: flex; flex-direction: column; gap: 15px; text-align: center;">
            <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <i class="fas fa-envelope-open-text" style="font-size: 1.2rem; color: #2563eb;"></i>
                <strong>E-posta Doğrulaması Gerekli</strong>
            </div>
            <p style="margin: 0; line-height: 1.5;">${message}</p>
            <button id="resend-verification-btn" style="
                background: #2563eb;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 500;
                transition: background-color 0.2s;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                margin: 0 auto;
            " onmouseover="this.style.background='#1d4ed8'" onmouseout="this.style.background='#2563eb'">
                <i class="fas fa-paper-plane"></i>
                Doğrulama E-postasını Tekrar Gönder
            </button>
        </div>
    `;
    errorDiv.style.display = 'block';
    errorDiv.style.background = '#eff6ff';
    errorDiv.style.border = '2px solid #bfdbfe';
    errorDiv.style.borderRadius = '12px';
    errorDiv.style.color = '#1e40af';
    errorDiv.style.padding = '20px';
    errorDiv.style.marginTop = '15px';
    errorDiv.style.marginBottom = '15px';
    errorDiv.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';

    // Add event listener to resend button
    document.getElementById('resend-verification-btn').addEventListener('click', () => {
        resendVerificationEmail(email);
    });

    // Scroll to error message
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function showSuccess(message) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; gap: 10px; text-align: center;">
            <i class="fas fa-check-circle" style="font-size: 1.2rem; color: #059669;"></i>
            <span>${message}</span>
        </div>
    `;
    errorDiv.style.display = 'block';
    errorDiv.style.background = '#ecfdf5';
    errorDiv.style.border = '2px solid #a7f3d0';
    errorDiv.style.borderRadius = '12px';
    errorDiv.style.color = '#059669';
    errorDiv.style.padding = '20px';
    errorDiv.style.marginTop = '15px';
    errorDiv.style.marginBottom = '15px';
    errorDiv.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';

    // Scroll to message
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Hide after 5 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

function showAdminApprovalError(message, email) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.innerHTML = `
        <div class="flex items-center gap-3">
            <i class="fas fa-clock text-xl"></i>
            <div>
                <p>${message}</p>
                <p class="text-sm mt-1">E-posta adresiniz doğrulanmış, admin onayı bekleniyor.</p>
            </div>
        </div>
    `;
    errorDiv.style.display = 'block';
    errorDiv.style.background = '#fef3c7';
    errorDiv.style.borderColor = '#fde68a';
    errorDiv.style.color = '#92400e';
}

function showRejectionError(message, email) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.innerHTML = `
        <div class="flex items-center gap-3">
            <i class="fas fa-times-circle text-xl"></i>
            <div>
                <p>${message}</p>
                <a href="/apply" class="text-sm underline mt-1 inline-block">Yeni başvuru yapmak için tıklayın</a>
            </div>
        </div>
    `;
    errorDiv.style.display = 'block';
    errorDiv.style.background = '#fef2f2';
    errorDiv.style.borderColor = '#fecaca';
    errorDiv.style.color = '#dc2626';
}

// Cooldown timer için global değişken
let resendCooldownTimer = null;
let resendCooldownSeconds = 0;

async function resendVerificationEmail(email) {
    const resendBtn = document.getElementById('resend-verification-btn');

    // Cooldown kontrolü
    if (resendCooldownSeconds > 0) {
        showError(`Lütfen ${resendCooldownSeconds} saniye bekleyin.`);
        return;
    }

    try {
        resendBtn.disabled = true;
        resendBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Gönderiliyor...';

        const response = await fetch('/.netlify/functions/send-verification-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('Doğrulama e-postası tekrar gönderildi. Lütfen e-posta kutunuzu kontrol edin.');
            startResendCooldown(60); // 60 saniye cooldown
        } else {
            if (response.status === 429) {
                showError(data.error || 'Çok fazla deneme. Lütfen daha sonra tekrar deneyin.');
                startResendCooldown(300); // 5 dakika cooldown rate limit durumunda
            } else {
                showError(data.error || 'E-posta gönderilemedi.');
            }
        }
    } catch (error) {
        console.error('Resend verification error:', error);
        showError('Bağlantı hatası. Lütfen tekrar deneyin.');
    } finally {
        if (resendCooldownSeconds === 0) {
            resendBtn.disabled = false;
            resendBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Doğrulama E-postasını Tekrar Gönder';
        }
    }
}

function startResendCooldown(seconds) {
    resendCooldownSeconds = seconds;
    const resendBtn = document.getElementById('resend-verification-btn');

    // If button doesn't exist, don't start the cooldown
    if (!resendBtn) {
        console.warn('Resend verification button not found, skipping cooldown');
        return;
    }

    if (resendCooldownTimer) {
        clearInterval(resendCooldownTimer);
    }

    resendCooldownTimer = setInterval(() => {
        resendCooldownSeconds--;

        // Get the button again in case it was removed from DOM
        const currentResendBtn = document.getElementById('resend-verification-btn');

        // If button doesn't exist anymore, clear the interval
        if (!currentResendBtn) {
            console.warn('Resend verification button no longer exists, stopping cooldown');
            clearInterval(resendCooldownTimer);
            resendCooldownTimer = null;
            return;
        }

        if (resendCooldownSeconds > 0) {
            currentResendBtn.disabled = true;
            currentResendBtn.innerHTML = `<i class="fas fa-clock mr-2"></i>Tekrar gönder (${resendCooldownSeconds}s)`;
        } else {
            currentResendBtn.disabled = false;
            currentResendBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Doğrulama E-postasını Tekrar Gönder';
            clearInterval(resendCooldownTimer);
            resendCooldownTimer = null;
        }
    }, 1000);
}

function showSuccess(message) {
    const errorDiv = document.getElementById('login-error');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    errorDiv.style.background = '#f0fdf4';
    errorDiv.style.borderColor = '#bbf7d0';
    errorDiv.style.color = '#166534';

    // Hide after 8 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
        // Reset styles
        errorDiv.style.background = '#fef2f2';
        errorDiv.style.borderColor = '#fecaca';
        errorDiv.style.color = '#dc2626';
    }, 8000);
}

async function handleForgotPassword(e) {
    e.preventDefault();

    const emailInput = document.getElementById('email');
    const email = emailInput.value.trim();

    if (!email) {
        showError('Lütfen e-posta adresinizi girin.');
        emailInput.focus();
        return;
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showError('Geçerli bir e-posta adresi girin.');
        emailInput.focus();
        return;
    }

    const forgotLink = document.getElementById('forgot-password-link');
    const originalText = forgotLink.textContent;

    // Show loading
    forgotLink.textContent = 'Gönderiliyor...';
    forgotLink.style.pointerEvents = 'none';

    try {
        const response = await fetch('/.netlify/functions/forgot-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
        });

        const data = await response.json();
        console.log('Forgot password response:', { status: response.status, data });

        if (data.success) {
            showSuccess(data.message);
        } else {
            showError(data.error || 'Şifre sıfırlama isteği gönderilemedi.');
        }

    } catch (error) {
        console.error('Forgot password error:', error);
        showError('Bağlantı hatası. Lütfen tekrar deneyin.');
    } finally {
        // Reset link
        setTimeout(() => {
            forgotLink.textContent = originalText;
            forgotLink.style.pointerEvents = 'auto';
        }, 2000);
    }
}
