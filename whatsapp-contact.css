/* WhatsApp <PERSON> */

/* WhatsApp Floating <PERSON><PERSON> */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #25d366, #128c7e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(37, 211, 102, 0.4);
}

.whatsapp-float i {
    color: white;
    font-size: 28px;
}

@keyframes pulse {
    0% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.5), 0 0 0 10px rgba(37, 211, 102, 0.1);
    }
    100% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    }
}

/* WhatsApp Contact Button (for profile page) */
.whatsapp-contact-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
}

.whatsapp-contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    color: white;
    text-decoration: none;
}

.whatsapp-contact-btn i {
    font-size: 1.25rem;
}

/* Modal Overlay */
.whatsapp-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 1rem;
    box-sizing: border-box;
}

.whatsapp-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Container */
.whatsapp-modal {
    background: white;
    border-radius: 1.5rem;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.whatsapp-modal-overlay.active .whatsapp-modal {
    transform: scale(1) translateY(0);
}

/* Modal Header */
.whatsapp-modal-header {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.whatsapp-modal-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.whatsapp-modal-title i {
    font-size: 1.5rem;
}

.whatsapp-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background 0.2s ease;
}

.whatsapp-modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Modal Body */
.whatsapp-modal-body {
    padding: 2rem;
    flex: 1;
    overflow-y: auto;
}

.whatsapp-form-group {
    margin-bottom: 1.5rem;
}

.whatsapp-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.whatsapp-form-input,
.whatsapp-form-textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.75rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    font-family: inherit;
}

.whatsapp-form-input:focus,
.whatsapp-form-textarea:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.whatsapp-form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* Character Counter */
.whatsapp-char-counter {
    text-align: right;
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.25rem;
}

/* Modal Footer */
.whatsapp-modal-footer {
    padding: 1.5rem 2rem;
    background: var(--gray-50);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-shrink: 0;
    border-top: 1px solid var(--gray-200);
}

.whatsapp-btn {
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.whatsapp-btn-cancel {
    background: var(--gray-200);
    color: var(--gray-700);
}

.whatsapp-btn-cancel:hover {
    background: var(--gray-300);
}

.whatsapp-btn-send {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.whatsapp-btn-send:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.whatsapp-btn-send:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Info Box */
.whatsapp-info {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.whatsapp-info i {
    color: #0ea5e9;
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.whatsapp-info-text {
    color: #0c4a6e;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .whatsapp-float {
        bottom: 20px;
        right: 20px;
        width: 55px;
        height: 55px;
    }

    .whatsapp-float i {
        font-size: 24px;
    }

    .whatsapp-modal {
        width: 95%;
        margin: 1rem;
        max-height: 85vh;
    }

    .whatsapp-modal-body {
        padding: 1.5rem;
        max-height: calc(85vh - 200px);
    }

    .whatsapp-modal-footer {
        padding: 1rem 1.5rem;
        flex-direction: column;
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 2px solid var(--gray-200);
        box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
    }

    .whatsapp-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
    }
}

@media (max-width: 480px) {
    .whatsapp-contact-btn {
        width: 100%;
        justify-content: center;
        padding: 1rem 1.5rem;
    }

    .whatsapp-modal {
        width: 98%;
        margin: 0.5rem;
        max-height: 90vh;
        border-radius: 1rem;
    }

    .whatsapp-modal-header {
        padding: 1rem 1.5rem;
    }

    .whatsapp-modal-body {
        padding: 1rem;
        max-height: calc(90vh - 180px);
    }

    .whatsapp-modal-footer {
        padding: 1rem;
        gap: 0.75rem;
    }

    .whatsapp-btn {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }

    .whatsapp-form-input,
    .whatsapp-form-textarea {
        padding: 0.75rem;
        font-size: 0.95rem;
    }

    .whatsapp-form-textarea {
        min-height: 100px;
    }
}
