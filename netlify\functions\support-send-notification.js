// Destek Sistemi E-posta Bildirimleri

const { createClient } = require('@supabase/supabase-js');
const { sendEmail } = require('./utils/email-service');
const {
    getNewTicketEmailTemplate,
    getTicketReplyEmailTemplate,
    getTicketStatusChangeEmailTemplate
} = require('./email-templates/support-notifications');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Sadece POST metodunu kabul et
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Request body'yi parse et
        let requestData;
        try {
            requestData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz JSON formatı.' })
            };
        }

        const { type, data } = requestData;

        if (!type || !data) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Bildirim tipi ve veri gerekli.' })
            };
        }

        let emailResult;

        switch (type) {
            case 'new_ticket':
                emailResult = await sendNewTicketNotification(data);
                break;
            case 'ticket_reply':
                emailResult = await sendTicketReplyNotification(data);
                break;
            case 'status_change':
                emailResult = await sendStatusChangeNotification(data);
                break;
            default:
                return {
                    statusCode: 400,
                    headers,
                    body: JSON.stringify({ error: 'Geçersiz bildirim tipi.' })
                };
        }

        if (emailResult.success) {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    success: true,
                    message: 'E-posta bildirimi gönderildi.',
                    emailId: emailResult.emailId
                })
            };
        } else {
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'E-posta gönderilemedi.',
                    details: emailResult.error
                })
            };
        }

    } catch (error) {
        console.error('Support notification error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Sunucu hatası.' })
        };
    }
}

// Yeni ticket bildirimi (Admin'e)
async function sendNewTicketNotification(data) {
    try {
        const { ticket, user, adminEmails } = data;

        if (!ticket || !user || !adminEmails || adminEmails.length === 0) {
            return { success: false, error: 'Eksik veri: ticket, user veya adminEmails' };
        }

        console.log('📧 Sending new ticket notification:', {
            ticket_id: ticket.id,
            ticket_number: ticket.ticket_number,
            subject: ticket.subject,
            description: ticket.description,
            hasDescription: !!ticket.description,
            user_name: user.first_name + ' ' + user.last_name,
            adminEmails: adminEmails
        });

        const emailHtml = getNewTicketEmailTemplate(ticket, user);
        
        // Email ayarlarını al
        const emailSettings = await getEmailSettings();

        const emailResult = await sendEmail({
            to: adminEmails,
            subject: `🎧 Yeni Destek Talebi: ${ticket.subject} (#${ticket.ticket_number})`,
            html: emailHtml,
            headers: {
                'X-Priority': '1',
                'X-MSMail-Priority': 'High',
                'Importance': 'high'
            }
        }, emailSettings);

        if (!emailResult.success) {
            console.error('New ticket email error:', emailResult.error);
            return { success: false, error: emailResult.error };
        }

        console.log('New ticket notification sent:', {
            ticketId: ticket.id,
            emailId: emailResult.emailId,
            provider: emailResult.provider
        });

        return { success: true, emailId: emailResult.emailId, provider: emailResult.provider };

    } catch (error) {
        console.error('Send new ticket notification error:', error);
        return { success: false, error: error.message };
    }
}

// Ticket yanıt bildirimi
async function sendTicketReplyNotification(data) {
    try {
        const { ticket, reply, recipientEmail, isAdminReply, adminInfo } = data;

        console.log('🔍 sendTicketReplyNotification called with:', {
            hasTicket: !!ticket,
            hasReply: !!reply,
            recipientEmail: recipientEmail,
            isAdminReply: isAdminReply,
            hasAdminInfo: !!adminInfo
        });

        if (!ticket || !reply || !recipientEmail) {
            console.error('❌ Missing required data:', { ticket: !!ticket, reply: !!reply, recipientEmail: !!recipientEmail });
            return { success: false, error: 'Eksik veri: ticket, reply veya recipientEmail' };
        }

        // Reply objesine author_name ekle
        const enhancedReply = {
            ...reply,
            author_name: isAdminReply
                ? (adminInfo?.full_name || adminInfo?.fullName || 'Destek Ekibi')
                : 'Kullanıcı'
        };

        console.log('📧 Enhanced reply data:', {
            originalReply: reply,
            enhancedReply: enhancedReply,
            adminInfo: adminInfo,
            isAdminReply: isAdminReply,
            recipientEmail: recipientEmail
        });

        const emailHtml = getTicketReplyEmailTemplate(ticket, enhancedReply, isAdminReply, adminInfo);

        // Test modu kontrolü email-service.js içinde yapılıyor, burada hardcode etmeyelim
        const targetEmail = recipientEmail;

        const subject = isAdminReply
            ? `💬 Destek Ekibinden Yanıt: ${ticket.subject} (#${ticket.ticket_number})`
            : `📝 Destek Talebinize Yanıt: ${ticket.subject} (#${ticket.ticket_number})`;

        // Email ayarlarını al
        const emailSettings = await getEmailSettings();
        console.log('📧 Email settings loaded:', !!emailSettings);

        console.log('📧 Sending email with:', {
            to: targetEmail,
            subject: subject,
            isAdminReply: isAdminReply
        });

        const emailResult = await sendEmail({
            to: targetEmail,
            subject: subject,
            html: emailHtml,
            headers: {
                'X-Priority': isAdminReply ? '1' : '3',
                'X-MSMail-Priority': isAdminReply ? 'High' : 'Normal',
                'Importance': isAdminReply ? 'high' : 'normal'
            }
        }, emailSettings);

        if (!emailResult.success) {
            console.error('❌ Ticket reply email error:', emailResult.error);
            return { success: false, error: emailResult.error };
        }

        console.log('✅ Ticket reply notification sent successfully:', {
            ticketId: ticket.id,
            replyId: reply.id,
            emailId: emailResult.emailId,
            recipient: targetEmail,
            isAdminReply,
            provider: emailResult.provider
        });

        return { success: true, emailId: emailResult.emailId, provider: emailResult.provider };

    } catch (error) {
        console.error('Send ticket reply notification error:', error);
        return { success: false, error: error.message };
    }
}

// Durum değişikliği bildirimi
async function sendStatusChangeNotification(data) {
    try {
        const { ticket, oldStatus, newStatus, changedBy, recipientEmail } = data;

        if (!ticket || !oldStatus || !newStatus || !changedBy || !recipientEmail) {
            return { success: false, error: 'Eksik veri: ticket, oldStatus, newStatus, changedBy veya recipientEmail' };
        }

        // Sadece önemli durum değişikliklerinde e-posta gönder
        const importantStatusChanges = ['resolved', 'closed'];
        if (!importantStatusChanges.includes(newStatus)) {
            return { success: true, emailId: null, message: 'Bu durum değişikliği için e-posta gönderilmedi.' };
        }

        const emailHtml = getTicketStatusChangeEmailTemplate(ticket, oldStatus, newStatus, changedBy);
        
        const statusTexts = {
            resolved: 'Çözüldü',
            closed: 'Kapatıldı'
        };

        // Email ayarlarını al
        const emailSettings = await getEmailSettings();

        const emailResult = await sendEmail({
            to: recipientEmail,
            subject: `🔄 Destek Talebi ${statusTexts[newStatus]}: ${ticket.subject} (#${ticket.ticket_number})`,
            html: emailHtml,
            headers: {
                'X-Priority': '2',
                'X-MSMail-Priority': 'Normal',
                'Importance': 'normal'
            }
        }, emailSettings);

        if (!emailResult.success) {
            console.error('Status change email error:', emailResult.error);
            return { success: false, error: emailResult.error };
        }

        console.log('Status change notification sent:', {
            ticketId: ticket.id,
            oldStatus,
            newStatus,
            emailId: emailResult.emailId,
            recipient: recipientEmail,
            provider: emailResult.provider
        });

        return { success: true, emailId: emailResult.emailId, provider: emailResult.provider };

    } catch (error) {
        console.error('Send status change notification error:', error);
        return { success: false, error: error.message };
    }
}

// Email ayarlarını Supabase'den al
async function getEmailSettings() {
    try {
        const { data: emailSettings, error: settingsError } = await supabase
            .from('system_settings')
            .select('setting_key, setting_value, setting_type')
            .in('setting_key', [
                'email_enabled',
                'email_service_provider',
                'email_test_mode',
                'email_test_address',
                'brevo_api_key',
                'brevo_sender_email',
                'brevo_sender_name'
            ])
            .eq('is_active', true);

        if (settingsError) {
            console.error('Failed to fetch email settings:', settingsError);
            return null;
        }

        return emailSettings;
    } catch (error) {
        console.error('Get email settings error:', error);
        return null;
    }
}

// Helper fonksiyon: Admin e-postalarını al
async function getAdminEmails() {
    try {
        const { createClient } = require('@supabase/supabase-js');
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
        const supabase = createClient(supabaseUrl, supabaseKey);

        const { data: admins, error } = await supabase
            .from('admins')
            .select('email')
            .eq('is_active', true);

        if (error) {
            console.error('Get admin emails error:', error);
            return [];
        }

        return admins ? admins.map(admin => admin.email) : [];

    } catch (error) {
        console.error('Get admin emails error:', error);
        return [];
    }
}
