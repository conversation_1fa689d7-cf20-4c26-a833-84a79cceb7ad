// Güvenlik Middleware'i
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

// Rate limiting için in-memory store (production'da Redis kullanın)
const rateLimitStore = new Map();

// IP adresini al - X-Forwarded-For'dan ilk IP'yi al
function getClientIP(event) {
    // X-Forwarded-For header'ı "client, proxy1, proxy2" formatında olabilir
    const forwardedFor = event.headers['x-forwarded-for'];
    if (forwardedFor) {
        // İlk IP adresini al (gerçek client IP)
        const firstIP = forwardedFor.split(',')[0].trim();
        if (firstIP && firstIP !== 'unknown') {
            return firstIP;
        }
    }

    return event.headers['x-real-ip'] ||
           event.requestContext?.identity?.sourceIp ||
           'unknown';
}

// Rate limiting kontrolü
function checkRateLimit(ip, endpoint = 'default') {
    const key = `${ip}:${endpoint}`;
    const now = Date.now();
    const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW) || 900000; // 15 dakika
    const maxAttempts = parseInt(process.env.RATE_LIMIT_MAX_ATTEMPTS) || 10;
    
    if (!rateLimitStore.has(key)) {
        rateLimitStore.set(key, { count: 1, firstAttempt: now });
        return { allowed: true, remaining: maxAttempts - 1 };
    }
    
    const attempts = rateLimitStore.get(key);
    
    // Zaman penceresi sıfırlandı mı?
    if (now - attempts.firstAttempt > windowMs) {
        rateLimitStore.set(key, { count: 1, firstAttempt: now });
        return { allowed: true, remaining: maxAttempts - 1 };
    }
    
    // Limit aşıldı mı?
    if (attempts.count >= maxAttempts) {
        return { 
            allowed: false, 
            remaining: 0,
            resetTime: attempts.firstAttempt + windowMs
        };
    }
    
    attempts.count++;
    return { 
        allowed: true, 
        remaining: maxAttempts - attempts.count 
    };
}

// Güvenlik header'ları
function getSecurityHeaders() {
    return {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; worker-src 'self' blob: https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com;"
    };
}

// Suspicious activity detection
async function detectSuspiciousActivity(ip, userAgent, endpoint) {
    try {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 3600000); // 1 saat önce
        
        // Son 1 saatte bu IP'den kaç istek gelmiş?
        const { data: recentLogs, error } = await supabase
            .from('admin_logs')
            .select('id, action, created_at')
            .eq('ip_address', ip)
            .gte('created_at', oneHourAgo.toISOString())
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Suspicious activity check error:', error);
            return { suspicious: false };
        }

        const requestCount = recentLogs?.length || 0;
        const failedLogins = recentLogs?.filter(log => 
            log.action === 'login_failed' || log.action === 'login_blocked'
        ).length || 0;

        // Şüpheli aktivite kriterleri
        const isSuspicious = 
            requestCount > 100 || // 1 saatte 100'den fazla istek
            failedLogins > 10 ||   // 1 saatte 10'dan fazla başarısız giriş
            (requestCount > 50 && failedLogins > 5); // Orta seviye şüpheli aktivite

        if (isSuspicious) {
            // Şüpheli aktivite log'u
            await supabase.from('admin_logs').insert([{
                admin_username: 'system', // Mevcut yapıya uygun
                admin_email: 'system',
                action: 'suspicious_activity_detected',
                details: {
                    ip: ip,
                    user_agent: userAgent,
                    endpoint: endpoint,
                    request_count: requestCount,
                    failed_logins: failedLogins,
                    detection_time: now.toISOString()
                },
                ip_address: ip,
                user_agent: userAgent
            }]).catch(err => console.error('Log insert error:', err));
        }

        return {
            suspicious: isSuspicious,
            requestCount: requestCount,
            failedLogins: failedLogins
        };

    } catch (error) {
        console.error('Suspicious activity detection error:', error);
        return { suspicious: false };
    }
}

// Input validation
function validateInput(data, rules) {
    const errors = [];
    
    for (const [field, rule] of Object.entries(rules)) {
        const value = data[field];
        
        if (rule.required && (!value || value.toString().trim() === '')) {
            errors.push(`${field} alanı gereklidir.`);
            continue;
        }
        
        if (value && rule.type) {
            if (rule.type === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                errors.push(`${field} geçerli bir e-posta adresi olmalıdır.`);
            }
            
            if (rule.type === 'string' && typeof value !== 'string') {
                errors.push(`${field} metin olmalıdır.`);
            }
            
            if (rule.type === 'number' && isNaN(Number(value))) {
                errors.push(`${field} sayı olmalıdır.`);
            }
        }
        
        if (value && rule.minLength && value.toString().length < rule.minLength) {
            errors.push(`${field} en az ${rule.minLength} karakter olmalıdır.`);
        }
        
        if (value && rule.maxLength && value.toString().length > rule.maxLength) {
            errors.push(`${field} en fazla ${rule.maxLength} karakter olmalıdır.`);
        }
        
        if (value && rule.pattern && !rule.pattern.test(value)) {
            errors.push(`${field} geçersiz format.`);
        }
    }
    
    return {
        valid: errors.length === 0,
        errors: errors
    };
}

// SQL injection koruması
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    // Tehlikeli karakterleri temizle
    return input
        .replace(/['"\\;]/g, '') // Quotes ve semicolon
        .replace(/--/g, '') // SQL comments
        .replace(/\/\*/g, '') // Multi-line comments start
        .replace(/\*\//g, '') // Multi-line comments end
        .trim();
}

module.exports = {
    getClientIP,
    checkRateLimit,
    getSecurityHeaders,
    detectSuspiciousActivity,
    validateInput,
    sanitizeInput
};
