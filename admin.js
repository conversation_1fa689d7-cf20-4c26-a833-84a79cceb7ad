// Admin Panel JavaScript
let adminToken = null;

document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    setupAdminLogin();
});

async function checkAdminAuth() {
    adminToken = localStorage.getItem('adminToken');

    if (adminToken) {
        try {
            // Token'ı sunucuda doğrula
            const response = await fetch('/.netlify/functions/admin-verify-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${adminToken}`
                }
            });

            const data = await response.json();

            if (data.valid) {
                // Admin bilgilerini sakla
                localStorage.setItem('adminData', JSON.stringify(data.admin));
                showDashboard();
                return;
            }
        } catch (e) {
            console.error('Token verification error:', e);
        }

        // Token geçersiz, temizle
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminData');
    }

    showLogin();
}

function showLogin() {
    document.getElementById('admin-login').style.display = 'flex';
    document.getElementById('admin-dashboard').style.display = 'none';
}

function showDashboard() {
    document.getElementById('admin-login').style.display = 'none';
    document.getElementById('admin-dashboard').style.display = 'block';
    setupDashboard();
    setupMobileMenu();
    loadApplications();
}

function setupAdminLogin() {
    document.getElementById('admin-login-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        const email = document.getElementById('admin-email').value.trim();
        const password = document.getElementById('admin-password').value;

        // Basit validation
        if (!email || !password) {
            showError('E-posta ve şifre gereklidir.');
            return;
        }

        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            showError('Geçerli bir e-posta adresi giriniz.');
            return;
        }

        // Loading state
        const submitBtn = document.querySelector('#admin-login-form button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Giriş yapılıyor...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/.netlify/functions/admin-auth', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });

            const data = await response.json();
            console.log('Login response:', { status: response.status, data });

            if (data.success) {
                adminToken = data.token;
                localStorage.setItem('adminToken', adminToken);
                localStorage.setItem('adminData', JSON.stringify(data.admin));
                console.log('Token saved:', adminToken.substring(0, 20) + '...');
                showSuccess('Giriş başarılı! Yönlendiriliyorsunuz...');

                setTimeout(() => {
                    showDashboard();
                }, 1000);
            } else {
                showError(data.error || 'Giriş başarısız');
            }
        } catch (error) {
            console.error('Login error:', error);
            showError('Giriş yapılırken hata oluştu. Lütfen tekrar deneyin.');
        } finally {
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
}

function setupDashboard() {
    // Tab switching
    document.querySelectorAll('.admin-nav-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.dataset.tab;
            switchTab(tab);
        });
    });

    // Password change form
    setupPasswordChange();

    // Load admin info
    loadAdminInfo();

    // Logout
    document.getElementById('admin-logout').addEventListener('click', async function() {
        if (!confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
            return;
        }

        try {
            // Sunucuda session'ı sonlandır
            if (adminToken) {
                await fetch('/.netlify/functions/admin-logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${adminToken}`
                    }
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            // Local storage'ı temizle
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminData');
            adminToken = null;
            showLogin();
            showSuccess('Başarıyla çıkış yapıldı.');
        }
    });
}

function setupMobileMenu() {
    const mobileToggle = document.getElementById('mobile-menu-toggle');
    const navMenu = document.getElementById('admin-nav-menu');
    const navButtons = document.querySelectorAll('.admin-nav-btn');
    const logoutBtn = document.getElementById('admin-logout');

    // Mobil menü toggle
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('show');

            // Icon değiştir
            const icon = this.querySelector('i');
            if (navMenu.classList.contains('show')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });
    }

    // Mobil cihazda tab değiştiğinde menüyü kapat
    function closeMobileMenu() {
        if (window.innerWidth <= 768 && navMenu && mobileToggle) {
            navMenu.classList.remove('show');
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }

            // Kısa bir gecikme ile scroll to top
            setTimeout(() => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }, 100);
        }
    }

    // Nav button'lara click event ekle
    navButtons.forEach(btn => {
        btn.addEventListener('click', closeMobileMenu);
    });

    // Logout button'a da ekle
    if (logoutBtn) {
        logoutBtn.addEventListener('click', closeMobileMenu);
    }

    // Dışarı tıklandığında menüyü kapat
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768 && navMenu && mobileToggle) {
            if (!navMenu.contains(e.target) && !mobileToggle.contains(e.target)) {
                navMenu.classList.remove('show');
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        }
    });

    // Resize event listener
    window.addEventListener('resize', function() {
        // Desktop modunda menüyü göster
        if (window.innerWidth > 768 && navMenu) {
            navMenu.classList.remove('show');
            navMenu.style.display = '';

            if (mobileToggle) {
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }
        }
    });
}

function switchTab(tabName) {
    // Update nav buttons
    document.querySelectorAll('.admin-nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.admin-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // Load content based on tab
    if (tabName === 'applications') {
        loadApplications();
    } else if (tabName === 'users') {
        loadUsers();
        loadUserStats(); // İstatistikleri ayrı yükle
    } else if (tabName === 'support') {
        loadSupportTickets();
        loadSupportCategories();
    } else if (tabName === 'stats') {
        loadStats();
    } else if (tabName === 'settings') {
        loadApiStatus();
    } else if (tabName === 'email-settings') {
        loadEmailSettings();
    }
}

async function loadApplications() {
    try {
        const response = await fetch('/.netlify/functions/admin-applications', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();
        console.log('Applications data:', data);

        if (data.applications) {
            displayApplications(data.applications);
        }
    } catch (error) {
        console.error('Applications load error:', error);
    }
}

function displayApplications(applications) {
    const container = document.getElementById('applications-list');

    // E-posta doğrulanmış ve doğrulanmamış başvuruları ayır
    const emailVerifiedApps = applications.filter(app => app.email_verified);
    const emailNotVerifiedApps = applications.filter(app => !app.email_verified);

    document.getElementById('pending-count').textContent = applications.length;

    if (applications.length === 0) {
        container.innerHTML = '<div class="no-data">Bekleyen başvuru bulunmuyor.</div>';
        return;
    }

    let html = '';

    // E-posta doğrulanmamış başvurular
    if (emailNotVerifiedApps.length > 0) {
        html += `
            <div class="application-section">
                <h3 class="section-title">
                    <i class="fas fa-envelope"></i>
                    E-posta Doğrulanmamış Başvurular (${emailNotVerifiedApps.length})
                </h3>
                <div class="applications-grid">
                    ${emailNotVerifiedApps.map(app => createApplicationCard(app)).join('')}
                </div>
            </div>
        `;
    }

    // E-posta doğrulanmış başvurular
    if (emailVerifiedApps.length > 0) {
        html += `
            <div class="application-section">
                <h3 class="section-title">
                    <i class="fas fa-envelope-open-text"></i>
                    E-posta Doğrulanmış - Admin Onayı Bekleyen (${emailVerifiedApps.length})
                </h3>
                <div class="applications-grid">
                    ${emailVerifiedApps.map(app => createApplicationCard(app)).join('')}
                </div>
            </div>
        `;
    }

    container.innerHTML = html;
}

function createApplicationCard(app) {
    return `
        <div class="application-card" data-id="${app.id}">
            <div class="app-header">
                <h4>${app.first_name} ${app.last_name}</h4>
                <span class="app-date">${new Date(app.created_at).toLocaleDateString('tr-TR')}</span>
            </div>
            <div class="app-details">
                <p><strong>E-posta:</strong> ${app.email}</p>
                <p><strong>E-posta Durumu:</strong>
                    ${app.email_verified ?
                        `<span class="email-verified">✅ Doğrulandı (${new Date(app.email_verified_at).toLocaleDateString('tr-TR')})</span>` :
                        `<span class="email-not-verified">❌ Doğrulanmamış</span>`
                    }
                </p>
                ${!app.email_verified ? `
                    <div class="email-actions" style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <p style="margin: 0 0 10px 0; font-size: 12px; color: #666;">Email Yönetimi:</p>
                        <button onclick="resendVerificationEmail('${app.id}', '${app.email}')"
                                class="btn-small btn-info" style="margin-right: 5px;">
                            📧 Doğrulama Maili Gönder
                        </button>
                        <button onclick="markEmailAsVerified('${app.id}', '${app.email}')"
                                class="btn-small btn-success" style="margin-right: 5px;">
                            ✅ Doğrulanmış İşaretle
                        </button>
                        <button onclick="deleteApplication('${app.id}', '${app.email}')"
                                class="btn-small btn-danger">
                            🗑️ Sil
                        </button>
                    </div>
                ` : ''}
                <p><strong>Telefon:</strong> ${app.phone}</p>
                <p><strong>Meslek:</strong> ${app.profession}</p>
                <p><strong>Kullanım Amacı:</strong> ${app.reason}</p>
            </div>
            <div class="app-actions">
                ${app.email_verified ? `
                    <button class="approve-btn" onclick="approveApplication('${app.id}')"
                            title="Başvuruyu onayla ve kullanıcı oluştur">
                        <i class="fas fa-check"></i> Onayla
                    </button>
                    <button class="reject-btn" onclick="rejectApplication('${app.id}')"
                            title="Başvuruyu reddet">
                        <i class="fas fa-times"></i> Reddet
                    </button>
                ` : `
                    <span class="email-pending-note">
                        <i class="fas fa-clock"></i>
                        E-posta doğrulaması bekleniyor
                    </span>
                `}
            </div>
        </div>
    `;
}

async function approveApplication(applicationId) {
    // DETAYLI LOGLAMA - SORUN TESPİTİ İÇİN
    console.log('=== FRONTEND APPROVE APPLICATION DEBUG ===');
    console.log('Function called with applicationId:', applicationId);
    console.log('Type of applicationId:', typeof applicationId);
    console.log('Current timestamp:', new Date().toISOString());

    if (!confirm('Bu başvuruyu onaylamak istediğinizden emin misiniz?')) {
        console.log('User cancelled approval');
        return;
    }

    try {
        const requestPayload = {
            applicationId,
            action: 'approve'
        };

        console.log('Request payload:', JSON.stringify(requestPayload, null, 2));
        console.log('=== END FRONTEND DEBUG ===');

        const response = await fetch('/.netlify/functions/admin-applications', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify(requestPayload)
        });
        
        const data = await response.json();
        console.log('Approve response:', { status: response.status, data });
        console.log('Full response details:', JSON.stringify(data, null, 2));

        if (data.success) {
            showSuccess('Başvuru onaylandı!');
            loadApplications();
        } else {
            console.error('Approval failed with error:', data.error);
            console.error('Error details:', data.details);
            showError(data.error || 'Onaylama başarısız');
        }
    } catch (error) {
        console.error('Approve error:', error);
        showError('Onaylama sırasında hata oluştu');
    }
}

async function rejectApplication(applicationId) {
    const reason = prompt('Red nedeni:');
    if (!reason) return;
    
    try {
        const response = await fetch('/.netlify/functions/admin-applications', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                applicationId,
                action: 'reject',
                reason
            })
        });
        
        const data = await response.json();
        console.log('Reject response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Başvuru reddedildi!');
            loadApplications();
        } else {
            showError(data.error || 'Reddetme başarısız');
        }
    } catch (error) {
        console.error('Reject error:', error);
        showError('Reddetme sırasında hata oluştu');
    }
}

async function loadUsers() {
    console.log('loadUsers called');
    try {
        document.getElementById('users-list').innerHTML = '<div class="loading">Kullanıcılar yükleniyor...</div>';

        // Sadece kullanıcı listesini al
        const response = await fetch('/.netlify/functions/admin-users', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        console.log('Users response status:', response.status);
        const data = await response.json();
        console.log('Users data:', data);

        if (data.users) {
            console.log('Final users to display:', data.users);
            displayUsers(data.users);
        } else {
            console.error('No users data received');
            document.getElementById('users-list').innerHTML = '<div class="no-data">Kullanıcılar yüklenemedi.</div>';
        }
    } catch (error) {
        console.error('Users load error:', error);
        document.getElementById('users-list').innerHTML = '<div class="no-data">Kullanıcılar yüklenirken hata oluştu.</div>';
    }
}

async function loadUserStats() {
    try {
        const response = await fetch('/.netlify/functions/admin-user-limits', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();
        console.log('User stats data:', data);

        if (data.success && data.stats) {
            updateLimitStats(data.stats);
        }
    } catch (error) {
        console.error('User stats load error:', error);
    }
}

function createUserCard(user) {
    const statusText = {
        'pending': 'Onay Bekliyor',
        'approved': 'Aktif',
        'suspended': 'Askıda'
    };

    const statusIcon = {
        'pending': 'fas fa-clock',
        'approved': 'fas fa-check-circle',
        'suspended': 'fas fa-ban'
    };

    return `
        <div class="user-card ${user.status}" data-id="${user.id}">
            <div class="user-header">
                <h4>${user.first_name} ${user.last_name}</h4>
                <span class="user-status ${user.status}">
                    <i class="${statusIcon[user.status]}"></i>
                    ${statusText[user.status]}
                </span>
            </div>
            <div class="user-details">
                <p><strong>E-posta:</strong> ${user.email}</p>
                <p><strong>Telefon:</strong> ${user.phone || 'Belirtilmemiş'}</p>
                <p><strong>Meslek:</strong> ${user.profession || 'Belirtilmemiş'}</p>
                <p><strong>Kayıt Tarihi:</strong> ${new Date(user.created_at).toLocaleDateString('tr-TR')}</p>
                <p><strong>E-posta Durumu:</strong>
                    ${user.email_verified_at ?
                        `<span class="email-verified">✅ Doğrulandı (${new Date(user.email_verified_at).toLocaleDateString('tr-TR')})</span>` :
                        `<span class="email-not-verified">❌ Doğrulanmamış</span>`
                    }
                </p>
                <p><strong>Son Giriş:</strong> ${user.last_login ? new Date(user.last_login).toLocaleDateString('tr-TR') : 'Hiç giriş yapmamış'}</p>
                ${user.status === 'approved' ? `
                <div class="user-limits">
                    <p><strong>Günlük Limit:</strong>
                        <input type="number"
                               class="limit-input"
                               value="${user.daily_summary_limit || 5}"
                               min="0"
                               max="100"
                               onchange="updateUserLimit('${user.id}', this.value)">
                    </p>
                    <p><strong>Bugün Kullanılan:</strong>
                        <span class="usage-count ${(user.daily_summary_count || 0) >= (user.daily_summary_limit || 5) ? 'at-limit' : ''}">
                            ${user.daily_summary_count || 0} / ${user.daily_summary_limit || 5}
                        </span>
                    </p>
                </div>
                ` : ''}
            </div>
            <div class="user-actions">
                ${user.status === 'pending' ? `
                    <button class="approve-btn" onclick="approveUser('${user.id}')"
                            title="Kullanıcıyı onayla">
                        <i class="fas fa-check"></i> Onayla
                    </button>
                    <button class="reject-btn" onclick="rejectUser('${user.id}')"
                            title="Kullanıcıyı reddet">
                        <i class="fas fa-times"></i> Reddet
                    </button>
                ` : user.status === 'approved' ?
                    `<button class="suspend-btn" onclick="suspendUser('${user.id}')">
                        <i class="fas fa-pause"></i> Askıya Al
                    </button>
                    <button class="reset-quota-btn" onclick="resetUserQuota('${user.id}')"
                            title="Kullanıcının günlük özet hakkını sıfırla">
                        <i class="fas fa-redo"></i> Hakkı Sıfırla
                    </button>` :
                    `<button class="activate-btn" onclick="activateUser('${user.id}')">
                        <i class="fas fa-play"></i> Aktifleştir
                    </button>`
                }
                <button class="delete-btn" onclick="deleteUser('${user.id}')">
                    <i class="fas fa-trash"></i> Sil
                </button>
            </div>
        </div>
    `;
}

function displayUsers(users) {
    console.log('displayUsers called with:', users);
    const container = document.getElementById('users-list');

    if (!container) {
        console.error('users-list container not found');
        return;
    }

    // Kullanıcıları status'e göre ayır
    const approvedUsers = users.filter(user => user.status === 'approved');
    const pendingUsers = users.filter(user => user.status === 'pending');
    const suspendedUsers = users.filter(user => user.status === 'suspended');

    console.log('User counts:', { approved: approvedUsers.length, pending: pendingUsers.length, suspended: suspendedUsers.length });

    // İstatistikleri güncelle
    document.getElementById('approved-count').textContent = approvedUsers.length;

    // Günlük kullanım toplamını hesapla (sadece approved kullanıcılar)
    const totalDailyUsage = approvedUsers.reduce((sum, user) => sum + (user.daily_summary_count || 0), 0);
    document.getElementById('daily-usage-count').textContent = totalDailyUsage;

    if (users.length === 0) {
        container.innerHTML = '<div class="no-data">Henüz kullanıcı bulunmuyor.</div>';
        return;
    }

    // Kullanıcıları gruplandırarak göster
    let html = '';

    // Pending kullanıcılar (önce bunları göster)
    if (pendingUsers.length > 0) {
        html += `
            <div class="user-section">
                <h3 class="user-section-title">
                    <i class="fas fa-clock"></i>
                    Onay Bekleyen Kullanıcılar (${pendingUsers.length})
                </h3>
                <div class="user-grid">
                    ${pendingUsers.map(user => createUserCard(user)).join('')}
                </div>
            </div>
        `;
    }

    // Approved kullanıcılar
    if (approvedUsers.length > 0) {
        html += `
            <div class="user-section">
                <h3 class="user-section-title">
                    <i class="fas fa-check-circle"></i>
                    Aktif Kullanıcılar (${approvedUsers.length})
                </h3>
                <div class="user-grid">
                    ${approvedUsers.map(user => createUserCard(user)).join('')}
                </div>
            </div>
        `;
    }

    // Suspended kullanıcılar
    if (suspendedUsers.length > 0) {
        html += `
            <div class="user-section">
                <h3 class="user-section-title">
                    <i class="fas fa-ban"></i>
                    Askıdaki Kullanıcılar (${suspendedUsers.length})
                </h3>
                <div class="user-grid">
                    ${suspendedUsers.map(user => createUserCard(user)).join('')}
                </div>
            </div>
        `;
    }

    container.innerHTML = html;
}



async function loadStats() {
    try {
        const response = await fetch('/.netlify/functions/admin-stats', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });
        
        const data = await response.json();
        
        if (data.stats) {
            displayStats(data.stats);
        }
    } catch (error) {
        console.error('Stats load error:', error);
    }
}

function displayStats(stats) {
    document.getElementById('total-summaries').textContent = stats.totalSummaries;
    document.getElementById('total-users').textContent = stats.totalUsers;
    document.getElementById('today-summaries').textContent = stats.todaySummaries;

    // Trial istatistikleri
    if (stats.trialStats) {
        document.getElementById('total-trials').textContent = stats.trialStats.total_trials;
        document.getElementById('unique-ips').textContent = stats.trialStats.unique_ips;
        document.getElementById('today-trials').textContent = stats.trialStats.today_trials;
    }
}

function showError(message) {
    alert('Hata: ' + message);
}

function showSuccess(message) {
    // Geçici başarı mesajı göster
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        z-index: 1000;
        font-family: Inter, sans-serif;
        font-weight: 500;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    `;
    successDiv.textContent = message;
    document.body.appendChild(successDiv);
    setTimeout(() => document.body.removeChild(successDiv), 3000);
}

// Şifre değiştirme setup
function setupPasswordChange() {
    const form = document.getElementById('password-change-form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const currentPassword = document.getElementById('current-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        // Validation
        if (!currentPassword || !newPassword || !confirmPassword) {
            showError('Tüm alanları doldurun.');
            return;
        }

        if (newPassword.length < 6) {
            showError('Yeni şifre en az 6 karakter olmalıdır.');
            return;
        }

        if (newPassword !== confirmPassword) {
            showError('Yeni şifreler eşleşmiyor.');
            return;
        }

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Değiştiriliyor...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/.netlify/functions/admin-change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${adminToken}`
                },
                body: JSON.stringify({
                    currentPassword,
                    newPassword
                })
            });

            const data = await response.json();

            if (data.success) {
                showSuccess('Şifre başarıyla değiştirildi!');
                form.reset();
            } else {
                showError(data.error || 'Şifre değiştirme başarısız.');
            }
        } catch (error) {
            console.error('Password change error:', error);
            showError('Şifre değiştirme sırasında hata oluştu.');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
}

// Admin bilgilerini yükle
function loadAdminInfo() {
    const adminData = JSON.parse(localStorage.getItem('adminData') || '{}');
    const adminInfoDiv = document.getElementById('admin-info');

    if (!adminInfoDiv || !adminData.email) return;

    adminInfoDiv.innerHTML = `
        <div class="info-item">
            <span class="info-label">E-posta:</span>
            <span class="info-value">${adminData.email}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Ad Soyad:</span>
            <span class="info-value">${adminData.fullName || 'Belirtilmemiş'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Rol:</span>
            <span class="info-value">${adminData.role || 'admin'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">ID:</span>
            <span class="info-value">${adminData.id}</span>
        </div>
    `;
}

// Kullanıcının günlük özet hakkını sıfırla
async function resetUserQuota(userId) {
    if (!confirm('Bu kullanıcının günlük özet hakkını sıfırlamak istediğinizden emin misiniz?')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-reset-quota', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ userId })
        });

        console.log('Reset response:', response.status);

        if (response.ok) {
            const result = await response.json();
            showSuccess('Kullanıcının günlük özet hakkı başarıyla sıfırlandı!');
            loadUsers(); // Listeyi yenile
        } else {
            const errorText = await response.text();
            console.error('Reset error response:', errorText);
            showError(`Hak sıfırlama işlemi başarısız oldu: ${response.status}`);
        }
    } catch (error) {
        console.error('Reset quota error:', error);
        showError('Hak sıfırlama sırasında bir hata oluştu: ' + error.message);
    }
}

// Tüm kullanıcıların günlük özet haklarını sıfırla
async function resetAllQuotas() {
    if (!confirm('TÜM kullanıcıların günlük özet haklarını sıfırlamak istediğinizden emin misiniz?\n\nBu işlem geri alınamaz ve tüm aktif kullanıcıları etkiler.')) return;

    // İkinci onay
    if (!confirm('Bu işlem tüm kullanıcıların günlük özet sayaçlarını 0\'a sıfırlayacak.\n\nDevam etmek istediğinizden emin misiniz?')) return;

    try {
        // Loading göster
        showLoading('Tüm kullanıcı hakları sıfırlanıyor...');

        const response = await fetch('/.netlify/functions/admin-reset-all-quotas', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        console.log('Reset all response:', response.status);

        if (response.ok) {
            const result = await response.json();
            hideLoading();
            showSuccess(`${result.affected_users} kullanıcının günlük özet hakkı başarıyla sıfırlandı!`);
            loadUsers(); // Listeyi yenile
            loadStats(); // İstatistikleri yenile
        } else {
            const errorText = await response.text();
            console.error('Reset all error response:', errorText);
            hideLoading();
            showError(`Toplu hak sıfırlama işlemi başarısız oldu: ${response.status}`);
        }
    } catch (error) {
        console.error('Reset all quotas error:', error);
        hideLoading();
        showError('Toplu hak sıfırlama sırasında bir hata oluştu: ' + error.message);
    }
}

// Pending kullanıcıları temizle
async function cleanupPendingUsers() {
    if (!confirm('Pending durumundaki kullanıcıları applications tablosuna taşımak istediğinizden emin misiniz?\n\nBu işlem e-posta doğrulanmış ama admin onayı bekleyen kullanıcıları yeni başvurular bölümüne taşıyacak.')) {
        return;
    }

    try {
        showLoading('Temizlik işlemi başlatılıyor...');

        const response = await fetch('/.netlify/functions/cleanup-pending-users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        const data = await response.json();

        if (data.success) {
            hideLoading();
            showSuccess(data.message);
            loadUsers(); // Kullanıcı listesini yenile
            loadApplications(); // Başvuru listesini yenile
        } else {
            hideLoading();
            showError(data.error || 'Temizlik işlemi başarısız.');
        }
    } catch (error) {
        console.error('Cleanup pending users error:', error);
        hideLoading();
        showError('Bağlantı hatası.');
    }
}

// Loading gösterme fonksiyonları
function showLoading(message) {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'admin-loading';
    loadingDiv.innerHTML = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 1rem; text-align: center; box-shadow: 0 10px 25px rgba(0,0,0,0.2);">
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-color);"></i>
                </div>
                <div style="font-weight: 600; color: var(--gray-700);">${message}</div>
            </div>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}

function hideLoading() {
    const loadingDiv = document.getElementById('admin-loading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// Kullanıcı yönetimi fonksiyonları
async function suspendUser(userId) {
    if (!confirm('Bu kullanıcıyı askıya almak istediğinizden emin misiniz?')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId,
                action: 'suspend'
            })
        });

        const data = await response.json();
        console.log('Suspend response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Kullanıcı askıya alındı!');
            loadUsers();
        } else {
            showError(data.error || 'Askıya alma başarısız');
        }
    } catch (error) {
        console.error('Suspend error:', error);
        showError('Askıya alma sırasında hata oluştu');
    }
}

async function activateUser(userId) {
    if (!confirm('Bu kullanıcıyı aktifleştirmek istediğinizden emin misiniz?')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId,
                action: 'activate'
            })
        });

        const data = await response.json();
        console.log('Activate response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Kullanıcı aktifleştirildi!');
            loadUsers();
        } else {
            showError(data.error || 'Aktifleştirme başarısız');
        }
    } catch (error) {
        console.error('Activate error:', error);
        showError('Aktifleştirme sırasında hata oluştu');
    }
}

async function deleteUser(userId) {
    if (!confirm('Bu kullanıcıyı kalıcı olarak silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId
            })
        });

        const data = await response.json();
        console.log('Delete response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Kullanıcı silindi!');
            loadUsers();
        } else {
            showError(data.error || 'Silme başarısız');
        }
    } catch (error) {
        console.error('Delete error:', error);
        showError('Silme sırasında hata oluştu');
    }
}

// Limit istatistiklerini güncelle
function updateLimitStats(stats) {
    if (stats.avg_limit_per_user !== undefined) {
        document.getElementById('avg-daily-limit').textContent = Math.round(stats.avg_limit_per_user * 10) / 10;
    }
    if (stats.users_at_limit !== undefined) {
        document.getElementById('users-at-limit').textContent = stats.users_at_limit;
    }
}

// Kullanıcı limitini güncelle
async function updateUserLimit(userId, newLimit) {
    const limitValue = parseInt(newLimit);

    if (isNaN(limitValue) || limitValue < 0 || limitValue > 100) {
        alert('Geçersiz Değer: Limit değeri 0-100 arasında olmalıdır.');
        loadUsers(); // Eski değeri geri yükle
        return;
    }

    try {
        const response = await fetch('/.netlify/functions/admin-user-limits', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId: userId,
                newLimit: limitValue
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('Kullanıcı limiti güncellendi!');
            // Kullanıcı listesini yenile
            setTimeout(() => loadUsers(), 1000);
        } else {
            showError(data.error || 'Limit güncellenemedi');
            loadUsers(); // Eski değeri geri yükle
        }
    } catch (error) {
        console.error('Limit update error:', error);
        showError('Limit güncellenirken hata oluştu');
        loadUsers(); // Eski değeri geri yükle
    }
}

// Kullanıcı yönetimi fonksiyonları
async function suspendUser(userId) {
    if (!confirm('Bu kullanıcıyı askıya almak istediğinizden emin misiniz?')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId,
                action: 'suspend'
            })
        });

        const data = await response.json();
        console.log('Suspend response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Kullanıcı askıya alındı!');
            loadUsers();
        } else {
            showError(data.error || 'Askıya alma başarısız');
        }
    } catch (error) {
        console.error('Suspend error:', error);
        showError('Askıya alma sırasında hata oluştu');
    }
}

async function activateUser(userId) {
    if (!confirm('Bu kullanıcıyı aktifleştirmek istediğinizden emin misiniz?')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId,
                action: 'activate'
            })
        });

        const data = await response.json();
        console.log('Activate response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Kullanıcı aktifleştirildi!');
            loadUsers();
        } else {
            showError(data.error || 'Aktifleştirme başarısız');
        }
    } catch (error) {
        console.error('Activate error:', error);
        showError('Aktifleştirme sırasında hata oluştu');
    }
}

async function deleteUser(userId) {
    if (!confirm('Bu kullanıcıyı kalıcı olarak silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz!')) return;

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId
            })
        });

        const data = await response.json();
        console.log('Delete response:', { status: response.status, data });

        if (data.success) {
            showSuccess('Kullanıcı silindi!');
            loadUsers();
        } else {
            showError(data.error || 'Silme başarısız');
        }
    } catch (error) {
        console.error('Delete error:', error);
        showError('Silme sırasında hata oluştu');
    }
}

// Kalıcı webhook URL oluşturma
async function generatePermanentWebhookUrl() {
    try {
        showLoading('Kalıcı webhook URL oluşturuluyor...');

        const response = await fetch('/.netlify/functions/webhook-reset-daily-quotas?generate=permanent', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();

            // Webhook URL'i göster
            document.getElementById('webhook-url').value = result.webhook_url;
            document.getElementById('webhook-label').textContent = 'Kalıcı Webhook URL:';
            document.getElementById('webhook-result').style.display = 'block';

            // Kullanım listesini güncelle
            const usageList = document.getElementById('webhook-usage-list');
            usageList.innerHTML = `
                <li>Bu URL kalıcıdır ve süresiz kullanılabilir</li>
                <li>Cron job servislerinde güvenle kullanabilirsiniz</li>
                <li>Günlük otomatik sıfırlama için ideal</li>
                <li>GET veya POST request ile tetiklenebilir</li>
                <li><strong>Güvenlik:</strong> URL'i güvenli bir yerde saklayın</li>
            `;

            showSuccess('Kalıcı webhook URL başarıyla oluşturuldu!');
        } else {
            const errorText = await response.text();
            console.error('Permanent webhook generation error:', errorText);
            showError('Kalıcı webhook URL oluşturulamadı');
        }
    } catch (error) {
        console.error('Permanent webhook generation error:', error);
        hideLoading();
        showError('Kalıcı webhook URL oluşturulurken hata oluştu: ' + error.message);
    }
}

// Geçici webhook URL oluşturma
async function generateTemporaryWebhookUrl() {
    try {
        showLoading('Geçici webhook URL oluşturuluyor...');

        const response = await fetch('/.netlify/functions/webhook-reset-daily-quotas?generate=token', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();

            // Webhook URL'i göster
            document.getElementById('webhook-url').value = result.webhook_url;
            document.getElementById('webhook-label').textContent = 'Geçici Webhook URL (24 saat geçerli):';
            document.getElementById('webhook-result').style.display = 'block';

            // Kullanım listesini güncelle
            const usageList = document.getElementById('webhook-usage-list');
            usageList.innerHTML = `
                <li>Bu URL 24 saat sonra otomatik olarak geçersiz olur</li>
                <li>Tek seferlik kullanım için idealdir</li>
                <li>Test amaçlı kullanabilirsiniz</li>
                <li>GET veya POST request ile tetiklenebilir</li>
            `;

            showSuccess('Geçici webhook URL başarıyla oluşturuldu! (24 saat geçerli)');
        } else {
            const errorText = await response.text();
            console.error('Temporary webhook generation error:', errorText);
            showError('Geçici webhook URL oluşturulamadı');
        }
    } catch (error) {
        console.error('Temporary webhook generation error:', error);
        hideLoading();
        showError('Geçici webhook URL oluşturulurken hata oluştu: ' + error.message);
    }
}

// Webhook URL'i kopyalama
async function copyWebhookUrl() {
    const webhookUrl = document.getElementById('webhook-url').value;

    try {
        await navigator.clipboard.writeText(webhookUrl);
        showSuccess('Webhook URL panoya kopyalandı!');

        // Copy butonunu geçici olarak değiştir
        const copyBtn = document.querySelector('.copy-btn');
        const originalHTML = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i>';
        copyBtn.style.background = 'var(--success-color)';

        setTimeout(() => {
            copyBtn.innerHTML = originalHTML;
            copyBtn.style.background = '';
        }, 2000);

    } catch (error) {
        console.error('Copy error:', error);
        // Fallback: Select text
        document.getElementById('webhook-url').select();
        document.getElementById('webhook-url').setSelectionRange(0, 99999);
        showSuccess('URL seçildi, Ctrl+C ile kopyalayabilirsiniz');
    }
}

// API Yönetimi Fonksiyonları
async function loadApiStatus() {
    try {
        showLoading('API durumu yükleniyor...');

        const response = await fetch('/.netlify/functions/admin-api-management', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            displayModelInfo(result.modelInfo);
            displayApiKeys(result.apiKeys, result.currentActiveKey);
            displayApiLogs(result.apiLogs);
            displayApiStats(result.keyStats);
            populateLogFilters(result.apiKeys);
            loadModelSettings(); // Model ayarlarını da yükle
        } else {
            const errorText = await response.text();
            console.error('API status load error:', errorText);
            showError('API durumu yüklenemedi');
        }
    } catch (error) {
        console.error('API status load error:', error);
        hideLoading();
        showError('API durumu yüklenirken hata oluştu: ' + error.message);
    }
}

// Model bilgisini görüntüle
function displayModelInfo(modelInfo) {
    const container = document.getElementById('model-info-card');

    if (!modelInfo) {
        container.innerHTML = '<div class="model-info-error">Model bilgisi yüklenemedi</div>';
        return;
    }

    container.innerHTML = `
        <div class="model-info-content">
            <div class="model-info-header">
                <div class="model-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="model-details">
                    <h4>Kullanılan AI Modeli</h4>
                    <div class="model-name">${modelInfo.displayName}</div>
                </div>
                <div class="model-status">
                    <span class="status-badge active">
                        <i class="fas fa-check-circle"></i>
                        Aktif
                    </span>
                    <button class="model-change-btn" onclick="toggleModelSelection()" title="Model değiştir">
                        <i class="fas fa-cog"></i>
                        Değiştir
                    </button>
                </div>
            </div>
            <div class="model-info-body">
                <div class="model-description">
                    <i class="fas fa-info-circle"></i>
                    ${modelInfo.description}
                </div>
                <div class="model-technical">
                    <div class="tech-detail">
                        <span class="tech-label">Model ID:</span>
                        <span class="tech-value">${modelInfo.name}</span>
                    </div>
                    <div class="tech-detail">
                        <span class="tech-label">API Versiyonu:</span>
                        <span class="tech-value">v1beta</span>
                    </div>
                    <div class="tech-detail">
                        <span class="tech-label">Endpoint:</span>
                        <span class="tech-value">generativelanguage.googleapis.com</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// API key'leri görüntüle
function displayApiKeys(apiKeys, currentActiveKey) {
    const container = document.getElementById('api-keys-grid');

    // Manuel seçim durumunu kontrol et
    const hasManualSelection = apiKeys.some(key => key.isActive);

    // Manuel seçimi temizleme butonu ekle
    const clearManualButton = hasManualSelection ? `
        <div class="api-management-controls">
            <button class="api-btn clear-manual" onclick="clearManualApiKeySelection()"
                    title="Manuel seçimi temizle ve otomatik failover'a dön">
                <i class="fas fa-undo"></i>
                Otomatik Failover'a Dön
            </button>
            <div class="manual-selection-info">
                <i class="fas fa-info-circle"></i>
                Manuel seçim aktif. Otomatik failover devre dışı.
            </div>
        </div>
    ` : `
        <div class="api-management-controls">
            <div class="auto-failover-info">
                <i class="fas fa-check-circle"></i>
                Otomatik failover aktif. API key'ler otomatik olarak değiştirilecek.
            </div>
        </div>
    `;

    container.innerHTML = clearManualButton + apiKeys.map(key => `
        <div class="api-key-card ${key.isActive ? 'active' : ''} ${!key.exists ? 'missing' : ''}">
            <div class="api-key-header">
                <div class="api-key-name">
                    <i class="fas fa-key"></i>
                    ${key.name}
                </div>
                <div class="api-key-status">
                    ${key.isActive ? '<span class="status-badge active">Aktif</span>' : ''}
                    ${!key.exists ? '<span class="status-badge missing">Eksik</span>' : ''}
                </div>
            </div>
            <div class="api-key-preview">
                ${key.preview}
            </div>
            ${key.exists ? `
                <div class="api-key-actions">
                    <button class="api-btn test" onclick="testApiKey('${key.name}')"
                            title="API key'i test et">
                        <i class="fas fa-vial"></i>
                        Test Et
                    </button>
                    ${!key.isActive ? `
                        <button class="api-btn activate" onclick="setActiveApiKey('${key.name}')"
                                title="Bu key'i aktif yap">
                            <i class="fas fa-play"></i>
                            Aktif Yap
                        </button>
                    ` : ''}
                </div>
            ` : `
                <div class="api-key-missing">
                    <p>Bu API key Netlify environment variables'da tanımlanmamış</p>
                </div>
            `}
        </div>
    `).join('');
}

// API loglarını görüntüle
function displayApiLogs(logs) {
    const container = document.getElementById('api-logs-list');

    if (!logs || logs.length === 0) {
        container.innerHTML = '<div class="no-logs">Henüz API log kaydı bulunmuyor</div>';
        return;
    }

    container.innerHTML = logs.map(log => `
        <div class="api-log-item ${log.status}">
            <div class="log-header">
                <div class="log-key">
                    <i class="fas fa-key"></i>
                    ${log.api_key_name}
                </div>
                <div class="log-time">
                    ${new Date(log.created_at).toLocaleString('tr-TR')}
                </div>
            </div>
            <div class="log-details">
                <div class="log-type">
                    <span class="type-badge ${log.request_type}">${log.request_type}</span>
                    <span class="status-badge ${log.status}">${log.status}</span>
                    ${log.response_status ? `<span class="response-code">${log.response_status}</span>` : ''}
                    ${log.response_time_ms ? `<span class="response-time">${log.response_time_ms}ms</span>` : ''}
                </div>
                ${log.error_message ? `
                    <div class="log-message">
                        <i class="fas fa-exclamation-circle"></i>
                        ${log.error_message}
                    </div>
                ` : ''}
            </div>
        </div>
    `).join('');
}

// API istatistiklerini görüntüle
function displayApiStats(keyStats) {
    const container = document.getElementById('api-stats-grid');

    if (!keyStats || Object.keys(keyStats).length === 0) {
        container.innerHTML = '<div class="no-stats">Son 24 saatte API kullanımı bulunmuyor</div>';
        return;
    }

    container.innerHTML = Object.entries(keyStats).map(([keyName, stats]) => `
        <div class="api-stat-card">
            <div class="stat-header">
                <i class="fas fa-key"></i>
                ${keyName}
            </div>
            <div class="stat-metrics">
                <div class="metric">
                    <span class="metric-value success">${stats.success || 0}</span>
                    <span class="metric-label">Başarılı</span>
                </div>
                <div class="metric">
                    <span class="metric-value error">${stats.error || 0}</span>
                    <span class="metric-label">Hatalı</span>
                </div>
                <div class="metric">
                    <span class="metric-value total">${stats.total || 0}</span>
                    <span class="metric-label">Toplam</span>
                </div>
            </div>
            <div class="stat-success-rate">
                ${stats.total > 0 ? `%${Math.round((stats.success || 0) / stats.total * 100)} başarı oranı` : 'Veri yok'}
            </div>
        </div>
    `).join('');
}

// Log filtrelerini doldur
function populateLogFilters(apiKeys) {
    const keyFilter = document.getElementById('log-filter-key');
    keyFilter.innerHTML = '<option value="">Tüm Key\'ler</option>' +
        apiKeys.filter(key => key.exists).map(key =>
            `<option value="${key.name}">${key.name}</option>`
        ).join('');
}

// API key test et
async function testApiKey(keyName) {
    try {
        showLoading(`${keyName} test ediliyor...`);

        const response = await fetch('/.netlify/functions/admin-api-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                action: 'test_key',
                apiKeyName: keyName
            })
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            showSuccess(`${keyName} test başarılı!`);
            loadApiStatus(); // Durumu yenile
        } else {
            const errorData = await response.json();
            showError(`${keyName} test başarısız: ${errorData.error}`);
        }
    } catch (error) {
        console.error('API key test error:', error);
        hideLoading();
        showError(`${keyName} test edilirken hata oluştu: ${error.message}`);
    }
}

// API key'i aktif yap
async function setActiveApiKey(keyName) {
    if (!confirm(`${keyName} key'ini aktif yapmak istediğinizden emin misiniz?`)) return;

    try {
        showLoading(`${keyName} aktif yapılıyor...`);

        const response = await fetch('/.netlify/functions/admin-api-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                action: 'set_active',
                apiKeyName: keyName
            })
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            showSuccess(`${keyName} aktif olarak ayarlandı!`);
            loadApiStatus(); // Durumu yenile
        } else {
            const errorData = await response.json();
            showError(`${keyName} aktif yapılamadı: ${errorData.error}`);
        }
    } catch (error) {
        console.error('Set active API key error:', error);
        hideLoading();
        showError(`${keyName} aktif yapılırken hata oluştu: ${error.message}`);
    }
}

// Manuel API key seçimini temizle
async function clearManualApiKeySelection() {
    if (!confirm('Manuel API key seçimini temizlemek ve otomatik failover\'a dönmek istediğinizden emin misiniz?')) return;

    try {
        showLoading('Manuel seçim temizleniyor...');

        const response = await fetch('/.netlify/functions/admin-api-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                action: 'clear_manual_selection'
            })
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message);
            loadApiStatus(); // Durumu yenile
        } else {
            const errorData = await response.json();
            showError(`Manuel seçim temizlenemedi: ${errorData.error}`);
        }
    } catch (error) {
        console.error('Manual selection clear error:', error);
        hideLoading();
        showError(`Manuel seçim temizlenirken hata oluştu: ${error.message}`);
    }
}

// Model ayarlarını yükle
async function loadModelSettings() {
    try {
        const response = await fetch('/.netlify/functions/admin-model-settings', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            displayModelSelection(result);
        } else {
            console.error('Model settings load error');
        }
    } catch (error) {
        console.error('Model settings load error:', error);
    }
}

// Model seçimi görüntüle
function displayModelSelection(modelData) {
    const container = document.getElementById('model-selection-card');

    const modelOptions = Object.entries(modelData.availableModels).map(([key, name]) => `
        <div class="model-option ${key === modelData.currentModel ? 'selected' : ''}"
             onclick="selectModel('${key}')">
            <div class="model-option-header">
                <div class="model-option-name">${name}</div>
                ${key === modelData.currentModel ? '<i class="fas fa-check-circle model-selected-icon"></i>' : ''}
            </div>
            <div class="model-option-id">${key}</div>
            <div class="model-option-actions">
                <button class="model-test-btn" onclick="testModel('${key}', event)" title="Bu modeli test et">
                    <i class="fas fa-vial"></i>
                    Test Et
                </button>
                ${key !== modelData.currentModel ? `
                    <button class="model-select-btn" onclick="changeModel('${key}', event)" title="Bu modeli aktif yap">
                        <i class="fas fa-check"></i>
                        Seç
                    </button>
                ` : ''}
            </div>
        </div>
    `).join('');

    container.innerHTML = `
        <div class="model-selection-content">
            <div class="model-selection-header">
                <h4>
                    <i class="fas fa-brain"></i>
                    Model Seçimi
                </h4>
                <button class="close-selection-btn" onclick="toggleModelSelection()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="model-options-grid">
                ${modelOptions}
            </div>
        </div>
    `;
}

// Model seçimi toggle
function toggleModelSelection() {
    const container = document.getElementById('model-selection-card');
    if (container.style.display === 'none') {
        container.style.display = 'block';
        loadModelSettings(); // Güncel verileri yükle
    } else {
        container.style.display = 'none';
    }
}

// Model test et
async function testModel(modelName, event) {
    event.stopPropagation();

    try {
        showLoading(`${modelName} test ediliyor...`);

        const response = await fetch('/.netlify/functions/admin-model-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                action: 'test_model',
                modelName: modelName
            })
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            showSuccess(`${modelName} test başarılı! (${result.responseTime}ms)`);
        } else {
            const errorData = await response.json();
            showError(`${modelName} test başarısız: ${errorData.error}`);
        }
    } catch (error) {
        console.error('Model test error:', error);
        hideLoading();
        showError(`${modelName} test edilirken hata oluştu: ${error.message}`);
    }
}

// Model değiştir
async function changeModel(modelName, event) {
    event.stopPropagation();

    if (!confirm(`Aktif modeli ${modelName} olarak değiştirmek istediğinizden emin misiniz?`)) return;

    try {
        showLoading(`Model ${modelName} olarak değiştiriliyor...`);

        const response = await fetch('/.netlify/functions/admin-model-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                action: 'update_model',
                modelName: modelName
            })
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message);
            loadApiStatus(); // Tüm API durumunu yenile
            toggleModelSelection(); // Model seçimini kapat
        } else {
            const errorData = await response.json();
            showError(`Model değiştirilemedi: ${errorData.error}`);
        }
    } catch (error) {
        console.error('Model change error:', error);
        hideLoading();
        showError(`Model değiştirilirken hata oluştu: ${error.message}`);
    }
}

// Kullanıcı onaylama
async function approveUser(userId) {
    // DETAYLI LOGLAMA - SORUN TESPİTİ İÇİN
    console.log('=== FRONTEND APPROVE USER DEBUG ===');
    console.log('Function called with userId:', userId);
    console.log('Type of userId:', typeof userId);
    console.log('Current timestamp:', new Date().toISOString());

    if (!confirm('Bu kullanıcıyı onaylamak istediğinizden emin misiniz?')) {
        console.log('User cancelled user approval');
        return;
    }

    try {
        const requestPayload = {
            userId: userId,
            action: 'approve'
        };

        console.log('User request payload:', JSON.stringify(requestPayload, null, 2));
        console.log('=== END FRONTEND USER DEBUG ===');

        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify(requestPayload)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message || 'Kullanıcı onaylandı ve bilgilendirme e-postası gönderildi.');
            loadUsers(); // Listeyi yenile
        } else {
            showError(data.error || 'Kullanıcı onaylanamadı.');
        }
    } catch (error) {
        console.error('Approve user error:', error);
        showError('Bağlantı hatası.');
    }
}

// Kullanıcı reddetme
async function rejectUser(userId) {
    const reason = prompt('Reddetme sebebini girin:');
    if (!reason) {
        return;
    }

    try {
        const response = await fetch('/.netlify/functions/admin-users', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                userId: userId,
                action: 'reject',
                reason: reason
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('Kullanıcı reddedildi.');
            loadUsers(); // Listeyi yenile
        } else {
            showError(data.error || 'Kullanıcı reddedilemedi.');
        }
    } catch (error) {
        console.error('Reject user error:', error);
        showError('Bağlantı hatası.');
    }
}

// Destek Talepleri Yönetimi Fonksiyonları
async function loadSupportTickets() {
    try {
        showLoading('Destek talepleri yükleniyor...');

        // Filtreleri al
        const statusFilter = document.getElementById('support-status-filter').value;
        const priorityFilter = document.getElementById('support-priority-filter').value;
        const categoryFilter = document.getElementById('support-category-filter').value;

        let url = '/.netlify/functions/admin-support-tickets?';
        const params = new URLSearchParams();

        if (statusFilter) params.append('status', statusFilter);
        if (priorityFilter) params.append('priority', priorityFilter);
        if (categoryFilter) params.append('category', categoryFilter);

        url += params.toString();

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            displaySupportTickets(result.tickets);
            updateSupportStats(result.stats);
        } else {
            const errorText = await response.text();
            console.error('Support tickets load error:', errorText);
            showError('Destek talepleri yüklenemedi');
        }
    } catch (error) {
        hideLoading();
        console.error('Support tickets load error:', error);
        showError('Destek talepleri yüklenirken hata oluştu');
    }
}

async function loadSupportCategories() {
    try {
        const response = await fetch('/.netlify/functions/admin-support-categories', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            populateCategoryFilter(result.categories);
        }
    } catch (error) {
        console.error('Support categories load error:', error);
    }
}

function populateCategoryFilter(categories) {
    const categoryFilter = document.getElementById('support-category-filter');

    // Mevcut seçenekleri temizle (ilk option hariç)
    while (categoryFilter.children.length > 1) {
        categoryFilter.removeChild(categoryFilter.lastChild);
    }

    // Kategorileri ekle
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categoryFilter.appendChild(option);
    });
}

function displaySupportTickets(tickets) {
    const ticketsList = document.getElementById('support-tickets-list');

    if (!tickets || tickets.length === 0) {
        ticketsList.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #718096;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 20px; color: #cbd5e0;"></i>
                <h3>Destek talebi bulunamadı</h3>
                <p>Seçilen filtrelere uygun destek talebi bulunmuyor.</p>
            </div>
        `;
        return;
    }

    ticketsList.innerHTML = tickets.map(ticket => createSupportTicketCard(ticket)).join('');
}

function createSupportTicketCard(ticket) {
    const createdDate = new Date(ticket.created_at).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    const priorityColors = {
        low: '#38a169',
        normal: '#3182ce',
        high: '#d69e2e',
        urgent: '#e53e3e'
    };

    const statusColors = {
        open: '#e53e3e',
        in_progress: '#3182ce',
        waiting: '#d69e2e',
        resolved: '#38a169',
        closed: '#718096'
    };

    const statusTexts = {
        open: 'Açık',
        in_progress: 'Devam Ediyor',
        waiting: 'Beklemede',
        resolved: 'Çözüldü',
        closed: 'Kapatıldı'
    };

    const priorityTexts = {
        low: 'Düşük',
        normal: 'Normal',
        high: 'Yüksek',
        urgent: 'Acil'
    };

    return `
        <div class="support-ticket-card" onclick="openSupportTicketModal('${ticket.id}')">
            <div class="ticket-header">
                <div class="ticket-info">
                    <div class="ticket-meta">
                        <span class="ticket-number">#${ticket.ticket_number}</span>
                        <span class="priority-badge priority-${ticket.priority}">
                            ${priorityTexts[ticket.priority]}
                        </span>
                        <span class="status-badge status-${ticket.status}">
                            ${statusTexts[ticket.status]}
                        </span>
                    </div>
                    <h4 class="ticket-subject">${escapeHtml(ticket.subject)}</h4>
                    <div class="ticket-details">
                        <span><i class="fas fa-user"></i> ${escapeHtml(ticket.user_name)}</span>
                        <span><i class="fas fa-envelope"></i> ${escapeHtml(ticket.user_email)}</span>
                        <span><i class="fas fa-clock"></i> ${createdDate}</span>
                        ${ticket.support_categories ? `<span><i class="fas fa-tag"></i> ${escapeHtml(ticket.support_categories.name)}</span>` : ''}
                    </div>
                </div>
                <div class="ticket-actions">
                    <span class="reply-count">
                        <i class="fas fa-comments"></i> ${ticket.reply_count || 0} yanıt
                    </span>
                    ${ticket.last_reply_by === 'user' && !ticket.is_read_by_admin ?
                        '<span class="new-message-badge">Yeni Mesaj</span>' : ''}
                </div>
            </div>
            <p class="ticket-description">
                ${escapeHtml(ticket.description)}
            </p>
        </div>
    `;
}

function updateSupportStats(stats) {
    if (stats) {
        document.getElementById('open-tickets-count').textContent = (stats.open || 0) + (stats.in_progress || 0);
        document.getElementById('urgent-tickets-count').textContent = stats.urgent || 0;
        document.getElementById('pending-replies-count').textContent = stats.pending_replies || 0;
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

async function openSupportTicketModal(ticketId) {
    try {
        const modal = document.getElementById('ticket-detail-modal');
        const modalBody = document.getElementById('ticket-modal-body');
        const modalTitle = document.getElementById('ticket-modal-title');

        // Loading göster
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div class="loading-spinner" style="margin: 0 auto 20px; width: 40px; height: 40px; border: 4px solid #e2e8f0; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <p>Ticket detayları yükleniyor...</p>
            </div>
        `;

        modal.style.display = 'flex';

        // Ticket detaylarını getir
        const response = await fetch(`/.netlify/functions/admin-support-ticket-details?id=${ticketId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayAdminTicketDetails(data.ticket);
        } else {
            const errorData = await response.json();
            modalBody.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #e53e3e;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <p>Ticket detayları yüklenemedi: ${errorData.error}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading ticket details:', error);
        const modalBody = document.getElementById('ticket-modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #e53e3e;">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <p>Ticket detayları yüklenirken bir hata oluştu.</p>
            </div>
        `;
    }
}

function closeTicketDetailModal() {
    document.getElementById('ticket-detail-modal').style.display = 'none';
}

function displayAdminTicketDetails(ticket) {
    const modalBody = document.getElementById('ticket-modal-body');
    const modalTitle = document.getElementById('ticket-modal-title');

    modalTitle.textContent = `#${ticket.ticket_number} - ${ticket.subject}`;

    const createdDate = new Date(ticket.created_at).toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    const categoryColor = ticket.support_categories?.color || '#667eea';
    const categoryIcon = ticket.support_categories?.icon || 'fas fa-question-circle';
    const categoryName = ticket.support_categories?.name || 'Genel';

    const statusColors = {
        open: '#e53e3e',
        in_progress: '#3182ce',
        waiting: '#d69e2e',
        resolved: '#38a169',
        closed: '#718096'
    };

    const statusTexts = {
        open: 'Açık',
        in_progress: 'Devam Ediyor',
        waiting: 'Beklemede',
        resolved: 'Çözüldü',
        closed: 'Kapatıldı'
    };

    const priorityTexts = {
        low: 'Düşük',
        normal: 'Normal',
        high: 'Yüksek',
        urgent: 'Acil'
    };

    modalBody.innerHTML = `
        <div style="margin-bottom: 25px;">
            <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 15px; flex-wrap: wrap;">
                <div style="background: ${statusColors[ticket.status]}; color: white; padding: 6px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; text-transform: uppercase;">
                    ${statusTexts[ticket.status]}
                </div>
                <div style="background: #667eea; color: white; padding: 6px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; text-transform: uppercase;">
                    ${priorityTexts[ticket.priority]}
                </div>
                <div style="background-color: ${categoryColor}; color: white; padding: 6px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                    <i class="${categoryIcon}"></i>
                    ${categoryName}
                </div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; color: #718096; font-size: 0.9rem;">
                <div><strong>Kullanıcı:</strong> ${escapeHtml(ticket.user_name)}</div>
                <div><strong>E-posta:</strong> ${escapeHtml(ticket.user_email)}</div>
                <div><strong>Oluşturulma:</strong> ${createdDate}</div>
                <div><strong>Yanıt Sayısı:</strong> ${ticket.reply_count || 0}</div>
            </div>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
            <h4 style="margin: 0 0 10px 0; color: #2d3748;">İlk Mesaj:</h4>
            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${escapeHtml(ticket.description)}</p>
        </div>

        <div id="admin-ticket-replies">
            <!-- Yanıtlar buraya yüklenecek -->
        </div>

        <div style="margin-top: 25px; padding-top: 20px; border-top: 2px solid #e2e8f0;">
            <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">Durum Değiştir:</label>
                    <select id="ticket-status-select" style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                        <option value="open" ${ticket.status === 'open' ? 'selected' : ''}>Açık</option>
                        <option value="in_progress" ${ticket.status === 'in_progress' ? 'selected' : ''}>Devam Ediyor</option>
                        <option value="waiting" ${ticket.status === 'waiting' ? 'selected' : ''}>Beklemede</option>
                        <option value="resolved" ${ticket.status === 'resolved' ? 'selected' : ''}>Çözüldü</option>
                        <option value="closed" ${ticket.status === 'closed' ? 'selected' : ''}>Kapatıldı</option>
                    </select>
                </div>
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">Öncelik:</label>
                    <select id="ticket-priority-select" style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                        <option value="low" ${ticket.priority === 'low' ? 'selected' : ''}>Düşük</option>
                        <option value="normal" ${ticket.priority === 'normal' ? 'selected' : ''}>Normal</option>
                        <option value="high" ${ticket.priority === 'high' ? 'selected' : ''}>Yüksek</option>
                        <option value="urgent" ${ticket.priority === 'urgent' ? 'selected' : ''}>Acil</option>
                    </select>
                </div>
            </div>

            <h4 style="margin: 0 0 15px 0; color: #2d3748;">Admin Yanıtı Ekle:</h4>
            <div id="admin-reply-form">
                <textarea id="admin-reply-message" placeholder="Yanıtınızı buraya yazın..."
                         style="width: 100%; min-height: 120px; padding: 15px; border: 2px solid #e2e8f0; border-radius: 10px; font-family: inherit; resize: vertical; box-sizing: border-box;"
                         required maxlength="2000"></textarea>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                    <div style="display: flex; gap: 15px;">
                        <button type="button" onclick="updateTicketStatus('${ticket.id}')" style="background: #f59e0b; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-edit"></i> Durumu Güncelle
                        </button>
                        <small style="color: #718096;">
                            <span id="admin-reply-char-count">0</span>/2000 karakter
                        </small>
                    </div>
                    <button type="button" onclick="submitAdminReply(event, '${ticket.id}')" style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600;" id="admin-reply-submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        Yanıt Gönder
                    </button>
                </div>
            </div>
        </div>
    `;

    // Yanıtları yükle
    loadAdminTicketReplies(ticket.id);

    // Karakter sayacını ayarla
    const replyTextarea = document.getElementById('admin-reply-message');
    const charCount = document.getElementById('admin-reply-char-count');

    replyTextarea.addEventListener('input', function() {
        charCount.textContent = this.value.length;
        if (this.value.length > 1900) {
            charCount.style.color = '#e53e3e';
        } else {
            charCount.style.color = '#718096';
        }
    });
}

// Admin ticket yanıtlarını yükle
async function loadAdminTicketReplies(ticketId) {
    const repliesContainer = document.getElementById('admin-ticket-replies');

    try {
        const response = await fetch(`/.netlify/functions/admin-support-ticket-details?id=${ticketId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayAdminTicketReplies(data.ticket.replies || []);
        } else {
            repliesContainer.innerHTML = '<p style="color: #e53e3e; text-align: center;">Yanıtlar yüklenemedi.</p>';
        }
    } catch (error) {
        console.error('Error loading replies:', error);
        repliesContainer.innerHTML = '<p style="color: #e53e3e; text-align: center;">Yanıtlar yüklenirken hata oluştu.</p>';
    }
}

// Admin ticket yanıtlarını görüntüle
function displayAdminTicketReplies(replies) {
    const repliesContainer = document.getElementById('admin-ticket-replies');

    if (!replies || replies.length === 0) {
        repliesContainer.innerHTML = '<p style="color: #718096; text-align: center;">Henüz yanıt bulunmuyor.</p>';
        return;
    }

    repliesContainer.innerHTML = replies.map(reply => {
        const replyDate = new Date(reply.created_at).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        const isAdmin = reply.is_admin_reply;
        const bgColor = isAdmin ? '#f0f9ff' : '#f8f9fa';
        const borderColor = isAdmin ? '#0ea5e9' : '#e2e8f0';
        const authorColor = isAdmin ? '#0ea5e9' : '#4a5568';

        return `
            <div style="background: ${bgColor}; border-left: 4px solid ${borderColor}; padding: 15px; margin-bottom: 15px; border-radius: 0 8px 8px 0;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div style="font-weight: 600; color: ${authorColor}; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-${isAdmin ? 'user-tie' : 'user'}"></i>
                        ${escapeHtml(reply.author_name)}
                        ${isAdmin ? '<span style="background: #0ea5e9; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7rem;">ADMIN</span>' : ''}
                    </div>
                    <div style="color: #718096; font-size: 0.875rem;">
                        <i class="fas fa-clock"></i>
                        ${replyDate}
                    </div>
                </div>
                <div style="color: #2d3748; line-height: 1.6; white-space: pre-wrap;">
                    ${escapeHtml(reply.message)}
                </div>
            </div>
        `;
    }).join('');
}

// Admin yanıt gönder
async function submitAdminReply(event, ticketId) {
    // Form submit değil, button click olduğu için preventDefault gerekmez

    const messageTextarea = document.getElementById('admin-reply-message');
    const submitBtn = document.getElementById('admin-reply-submit-btn');
    const message = messageTextarea.value.trim();

    if (!message) {
        showError('Lütfen bir mesaj yazın.');
        return;
    }

    if (message.length < 5) {
        showError('Mesaj en az 5 karakter olmalıdır.');
        return;
    }

    if (message.length > 2000) {
        showError('Mesaj 2000 karakterden uzun olamaz.');
        return;
    }

    // Buton durumunu güncelle
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('/.netlify/functions/support-add-reply', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ticketId: ticketId,
                message: message,
                isAdminReply: true
            })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            // Mesajı temizle
            messageTextarea.value = '';
            document.getElementById('admin-reply-char-count').textContent = '0';

            // Yanıtları yeniden yükle
            loadAdminTicketReplies(ticketId);

            // Ana listeyi de yenile
            loadSupportTickets();

            showSuccess('Yanıtınız başarıyla gönderildi.');
        } else {
            console.error('Admin reply error response:', data);
            showError(data.error || 'Yanıt gönderilemedi.');

            // Detaylı hata bilgisini göster
            if (data.details) {
                console.error('Error details:', data.details);
            }
            if (data.debug) {
                console.error('Debug info:', data.debug);
            }
        }

    } catch (error) {
        console.error('Error submitting reply:', error);
        showError('Yanıt gönderilirken bir hata oluştu.');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Ticket durumunu güncelle
async function updateTicketStatus(ticketId) {
    const statusSelect = document.getElementById('ticket-status-select');
    const prioritySelect = document.getElementById('ticket-priority-select');

    const newStatus = statusSelect.value;
    const newPriority = prioritySelect.value;

    try {
        const response = await fetch('/.netlify/functions/admin-support-tickets', {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ticketId: ticketId,
                status: newStatus,
                priority: newPriority
            })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            showSuccess('Ticket durumu başarıyla güncellendi.');
            loadSupportTickets(); // Ana listeyi yenile
        } else {
            showError(data.error || 'Ticket güncellenemedi.');
        }

    } catch (error) {
        console.error('Error updating ticket:', error);
        showError('Ticket güncellenirken bir hata oluştu.');
    }
}

// Email Ayarları Fonksiyonları
async function loadEmailSettings() {
    try {
        showLoading('Email ayarları yükleniyor...');

        const response = await fetch('/.netlify/functions/admin-email-settings', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            console.log('Email settings API response:', result);
            console.log('Settings array:', result.settings);
            displayEmailSettings(result.settings);
            setupEmailProviderHandlers();
        } else {
            const errorText = await response.text();
            console.error('Email settings load error:', errorText);
            showError('Email ayarları yüklenemedi');
        }
    } catch (error) {
        console.error('Email settings load error:', error);
        hideLoading();
        showError('Email ayarları yüklenirken hata oluştu: ' + error.message);
    }
}

function displayEmailSettings(settings) {
    console.log('displayEmailSettings called with:', settings);

    // Settings'i key-value object'e çevir
    const settingsObj = {};
    settings.forEach(setting => {
        settingsObj[setting.setting_key] = setting.setting_value;
    });

    console.log('Settings object:', settingsObj);

    // Email status indicator'ı güncelle
    updateEmailStatusIndicator(settingsObj);

    // Sadece Brevo destekleniyor, provider seçimi kaldırıldı
    const provider = 'brevo';



    // Brevo ayarları
    console.log('Brevo settings from server:', {
        api_key: settingsObj.brevo_api_key,
        api_key_length: settingsObj.brevo_api_key?.length,
        is_masked: settingsObj.brevo_api_key === '••••••••',
        sender_email: settingsObj.brevo_sender_email,
        sender_name: settingsObj.brevo_sender_name
    });

    const brevoApiKeyField = document.getElementById('brevo-api-key');
    const brevoEmailField = document.getElementById('brevo-sender-email');
    const brevoNameField = document.getElementById('brevo-sender-name');

    if (brevoApiKeyField) {
        // Eğer masked değilse ve değer varsa göster, yoksa boş bırak
        if (settingsObj.brevo_api_key && settingsObj.brevo_api_key !== '••••••••') {
            brevoApiKeyField.value = settingsObj.brevo_api_key;
        } else if (settingsObj.brevo_api_key === '••••••••') {
            brevoApiKeyField.placeholder = 'API key mevcut - değiştirmek için yeni key girin';
            brevoApiKeyField.value = '';
        } else {
            brevoApiKeyField.value = '';
        }
    }

    if (brevoEmailField) brevoEmailField.value = settingsObj.brevo_sender_email || '';
    if (brevoNameField) brevoNameField.value = settingsObj.brevo_sender_name || '';



    // Genel ayarlar
    document.getElementById('email-enabled').checked = settingsObj.email_enabled === 'true';
    document.getElementById('email-test-mode').checked = settingsObj.email_test_mode === 'true';
    document.getElementById('email-test-address').value = settingsObj.email_test_address || '';
    document.getElementById('admin-notification-emails').value = settingsObj.admin_notification_emails || '';

    // Brevo ayarları her zaman görünür
    showEmailProviderSection('brevo');
}

function setupEmailProviderHandlers() {
    // Artık sadece Brevo destekleniyor, provider handler'a gerek yok
    // Brevo ayarları her zaman görünür olacak
}

function showEmailProviderSection(provider) {
    // Tüm provider section'larını gizle
    const brevoSettings = document.getElementById('brevo-settings');

    if (brevoSettings) brevoSettings.style.display = 'none';

    // Seçilen provider'ı göster (sadece Brevo)
    if (provider === 'brevo' && brevoSettings) {
        brevoSettings.style.display = 'block';
    }
}

function updateEmailStatusIndicator(settingsObj) {
    const indicator = document.getElementById('email-status-indicator');
    const statusText = document.getElementById('email-status-text');

    if (!indicator || !statusText) return;

    const emailEnabled = settingsObj.email_enabled === 'true' || settingsObj.email_enabled === true;
    const provider = settingsObj.email_service_provider || 'brevo';
    const testMode = settingsObj.email_test_mode === 'true' || settingsObj.email_test_mode === true;

    // Provider'a göre API key kontrolü (sadece Brevo)
    let hasApiKey = false;
    if (provider === 'brevo') {
        hasApiKey = settingsObj.brevo_api_key && settingsObj.brevo_api_key !== '';
    }

    // Status belirleme
    if (!emailEnabled) {
        indicator.className = 'email-status-indicator disabled';
        statusText.textContent = 'Email Devre Dışı';
    } else if (!hasApiKey) {
        indicator.className = 'email-status-indicator disabled';
        statusText.textContent = `${provider.toUpperCase()} Yapılandırılmamış`;
    } else if (testMode) {
        indicator.className = 'email-status-indicator active';
        statusText.textContent = `${provider.toUpperCase()} Aktif (Test Modu)`;
    } else {
        indicator.className = 'email-status-indicator active';
        statusText.textContent = `${provider.toUpperCase()} Aktif`;
    }
}

async function saveEmailSettings() {
    try {
        showLoading('Email ayarları kaydediliyor...');

        // Form verilerini topla
        const settings = [];

        // Email provider (sadece Brevo destekleniyor)
        settings.push({ setting_key: 'email_service_provider', setting_value: 'brevo' });



        // Brevo ayarları
        const brevoApiKey = document.getElementById('brevo-api-key').value;
        console.log('Brevo API Key being saved:', brevoApiKey);
        settings.push({ setting_key: 'brevo_api_key', setting_value: brevoApiKey || '' });
        settings.push({ setting_key: 'brevo_sender_email', setting_value: document.getElementById('brevo-sender-email').value });
        settings.push({ setting_key: 'brevo_sender_name', setting_value: document.getElementById('brevo-sender-name').value });



        // Genel ayarlar
        settings.push({ setting_key: 'email_enabled', setting_value: document.getElementById('email-enabled').checked.toString() });
        settings.push({ setting_key: 'email_test_mode', setting_value: document.getElementById('email-test-mode').checked.toString() });
        settings.push({ setting_key: 'email_test_address', setting_value: document.getElementById('email-test-address').value });
        settings.push({ setting_key: 'admin_notification_emails', setting_value: document.getElementById('admin-notification-emails').value });

        const response = await fetch('/.netlify/functions/admin-email-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ settings })
        });

        hideLoading();

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                showSuccess('Email ayarları başarıyla kaydedildi!');
            } else {
                showError(result.message || 'Bazı ayarlar kaydedilemedi');
            }
        } else {
            const errorText = await response.text();
            console.error('Email settings save error:', errorText);
            showError('Email ayarları kaydedilemedi');
        }
    } catch (error) {
        console.error('Email settings save error:', error);
        hideLoading();
        showError('Email ayarları kaydedilirken hata oluştu: ' + error.message);
    }
}

async function testEmailSettings() {
    try {
        showLoading('Email ayarları test ediliyor...');

        const response = await fetch('/.netlify/functions/admin-email-settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            }
        });

        hideLoading();

        const result = await response.json();
        const testResultDiv = document.getElementById('email-test-result');

        if (result.success && result.test_result.success) {
            testResultDiv.className = 'email-test-result success';
            testResultDiv.innerHTML = `
                <h4><i class="fas fa-check-circle"></i> Test Başarılı!</h4>
                <p>Email ayarları doğru şekilde yapılandırılmış.</p>
                <p><strong>Provider:</strong> ${result.test_result.provider}</p>
                <p><strong>Email ID:</strong> ${result.test_result.emailId || 'N/A'}</p>
            `;
        } else {
            testResultDiv.className = 'email-test-result error';
            testResultDiv.innerHTML = `
                <h4><i class="fas fa-exclamation-circle"></i> Test Başarısız!</h4>
                <p>Email gönderimi sırasında hata oluştu:</p>
                <p><strong>Hata:</strong> ${result.test_result?.error || result.error || 'Bilinmeyen hata'}</p>
            `;
        }

        testResultDiv.style.display = 'block';

        // 10 saniye sonra test sonucunu gizle
        setTimeout(() => {
            testResultDiv.style.display = 'none';
        }, 10000);

    } catch (error) {
        console.error('Email settings test error:', error);
        hideLoading();
        showError('Email ayarları test edilirken hata oluştu: ' + error.message);
    }
}

// Email doğrulama mailini tekrar gönder
async function resendVerificationEmail(applicationId, email) {
    if (!confirm(`${email} adresine doğrulama e-postası tekrar gönderilsin mi?`)) {
        return;
    }

    try {
        showLoading('Doğrulama e-postası gönderiliyor...');

        const response = await fetch('/.netlify/functions/admin-email-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
            },
            body: JSON.stringify({
                action: 'resend_verification',
                applicationId: applicationId,
                email: email
            })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('Doğrulama e-postası başarıyla gönderildi.');
        } else {
            showError(result.error || 'E-posta gönderimi başarısız.');
        }

    } catch (error) {
        console.error('Resend verification email error:', error);
        showError('E-posta gönderimi sırasında hata oluştu.');
    } finally {
        hideLoading();
    }
}

// Email'i doğrulanmış olarak işaretle
async function markEmailAsVerified(applicationId, email) {
    if (!confirm(`${email} adresi admin tarafından doğrulanmış olarak işaretlensin mi?`)) {
        return;
    }

    try {
        showLoading('Email doğrulanmış olarak işaretleniyor...');

        const response = await fetch('/.netlify/functions/admin-email-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
            },
            body: JSON.stringify({
                action: 'mark_verified',
                applicationId: applicationId,
                email: email
            })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('Email başarıyla doğrulanmış olarak işaretlendi.');
            // Sayfayı yenile
            loadApplications();
        } else {
            showError(result.error || 'İşlem başarısız.');
        }

    } catch (error) {
        console.error('Mark email as verified error:', error);
        showError('İşlem sırasında hata oluştu.');
    } finally {
        hideLoading();
    }
}

// Başvuruyu sil
async function deleteApplication(applicationId, email) {
    if (!confirm(`${email} adresli başvuru kalıcı olarak silinsin mi?\n\nBu işlem geri alınamaz!`)) {
        return;
    }

    try {
        showLoading('Başvuru siliniyor...');

        const response = await fetch('/.netlify/functions/admin-applications', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
            },
            body: JSON.stringify({
                applicationId: applicationId
            })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('Başvuru başarıyla silindi.');
            // Sayfayı yenile
            loadApplications();
        } else {
            showError(result.error || 'Silme işlemi başarısız.');
        }

    } catch (error) {
        console.error('Delete application error:', error);
        showError('Silme işlemi sırasında hata oluştu.');
    } finally {
        hideLoading();
    }
}
