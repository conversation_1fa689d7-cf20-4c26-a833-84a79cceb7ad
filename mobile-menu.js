// Modern Mobile Menu System
class MobileMenu {
    constructor() {
        this.mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        this.mobileMenu = document.getElementById('mobile-menu');
        this.mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        this.mobileMenuClose = document.getElementById('mobile-menu-close');
        this.isActive = false;
        
        this.init();
    }

    init() {
        console.log('Initializing mobile menu...', {
            toggle: !!this.mobileMenuToggle,
            menu: !!this.mobileMenu,
            overlay: !!this.mobileMenuOverlay,
            close: !!this.mobileMenuClose
        });

        this.bindEvents();
        this.updateAuthButtons();
    }

    bindEvents() {
        // Toggle button events
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggle();
            });
            
            // Add touch support for better mobile experience
            this.mobileMenuToggle.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggle();
            });
        }

        // Close button events
        if (this.mobileMenuClose) {
            this.mobileMenuClose.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.close();
            });
        }

        // Overlay click to close
        if (this.mobileMenuOverlay) {
            this.mobileMenuOverlay.addEventListener('click', () => {
                this.close();
            });
        }

        // Close menu when clicking on nav links
        this.bindNavLinkEvents();

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isActive) {
                this.close();
            }
        });

        // Window resize events
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && this.isActive) {
                this.close();
            }
        });

        // Logout button event
        const mobileLogoutBtn = document.getElementById('mobile-logout-btn');
        if (mobileLogoutBtn) {
            mobileLogoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
        }
    }

    bindNavLinkEvents() {
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link, .mobile-auth-btn');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Don't close for logout button - let it handle its own logic
                if (!link.classList.contains('logout-btn')) {
                    // Small delay for better UX
                    setTimeout(() => this.close(), 100);
                }
            });
        });
    }

    toggle() {
        console.log('Toggle mobile menu');
        if (this.isActive) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        console.log('Opening mobile menu');
        this.isActive = true;

        if (this.mobileMenu) {
            this.mobileMenu.classList.add('active');
        }

        if (this.mobileMenuOverlay) {
            this.mobileMenuOverlay.classList.add('active');
        }

        // Prevent background scroll
        document.body.style.overflow = 'hidden';

        // Update toggle icon
        this.updateToggleIcon(true);

        // Update auth buttons when menu opens
        this.updateAuthButtons();
    }

    close() {
        console.log('Closing mobile menu');
        this.isActive = false;
        
        if (this.mobileMenu) {
            this.mobileMenu.classList.remove('active');
        }
        
        if (this.mobileMenuOverlay) {
            this.mobileMenuOverlay.classList.remove('active');
        }
        
        // Restore background scroll
        document.body.style.overflow = '';
        
        // Update toggle icon
        this.updateToggleIcon(false);
    }

    updateToggleIcon(isOpen) {
        if (this.mobileMenuToggle) {
            const icon = this.mobileMenuToggle.querySelector('i');
            if (icon) {
                icon.className = isOpen ? 'fas fa-times' : 'fas fa-bars';
            }
        }
    }

    updateAuthButtons() {
        // Check if user is logged in
        const userToken = localStorage.getItem('userToken');
        const userData = localStorage.getItem('userData');

        console.log('Mobile menu - updateAuthButtons:', {
            hasToken: !!userToken,
            hasUserData: !!userData,
            userData: userData
        });

        const mobileUserSection = document.getElementById('mobile-user-section');
        const mobileAuthSection = document.getElementById('mobile-auth-section');
        const mobileUserName = document.getElementById('mobile-user-name');

        if (userToken && userData) {
            // User is logged in
            try {
                const user = JSON.parse(userData);
                console.log('Parsed user data:', user);

                if (mobileUserSection) {
                    mobileUserSection.style.display = 'block';
                }

                if (mobileAuthSection) {
                    mobileAuthSection.style.display = 'none';
                }

                if (mobileUserName) {
                    // Try different field combinations
                    let displayName = '';
                    if (user.first_name && user.last_name) {
                        displayName = `${user.first_name} ${user.last_name}`;
                    } else if (user.firstName && user.lastName) {
                        displayName = `${user.firstName} ${user.lastName}`;
                    } else if (user.name) {
                        displayName = user.name;
                    } else if (user.email) {
                        displayName = user.email.split('@')[0]; // Use email username as fallback
                    } else {
                        displayName = 'Kullanıcı';
                    }

                    console.log('Setting display name:', displayName);
                    mobileUserName.textContent = displayName;
                }

            } catch (error) {
                console.error('Error parsing user data:', error);
                this.showAuthButtons();
            }
        } else {
            // User is not logged in
            this.showAuthButtons();
        }
    }

    showAuthButtons() {
        const mobileUserSection = document.getElementById('mobile-user-section');
        const mobileAuthSection = document.getElementById('mobile-auth-section');
        
        if (mobileUserSection) {
            mobileUserSection.style.display = 'none';
        }
        
        if (mobileAuthSection) {
            mobileAuthSection.style.display = 'block';
        }
    }

    handleLogout() {
        console.log('Mobile logout initiated');
        
        // Clear user data
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        
        // Close mobile menu
        this.close();
        
        // Update auth buttons
        this.updateAuthButtons();
        
        // Redirect to home page
        setTimeout(() => {
            window.location.href = '/';
        }, 300);
    }
}

// Initialize mobile menu when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mobileMenuInstance = new MobileMenu();
    
    // Make functions globally available for backward compatibility
    window.toggleMobileMenu = () => {
        if (window.mobileMenuInstance) {
            window.mobileMenuInstance.toggle();
        }
    };
    
    window.openMobileMenu = () => {
        if (window.mobileMenuInstance) {
            window.mobileMenuInstance.open();
        }
    };
    
    window.closeMobileMenu = () => {
        if (window.mobileMenuInstance) {
            window.mobileMenuInstance.close();
        }
    };
    
    window.mobileLogout = () => {
        if (window.mobileMenuInstance) {
            window.mobileMenuInstance.handleLogout();
        }
    };
});

// Update auth buttons when storage changes (for cross-tab sync)
window.addEventListener('storage', (e) => {
    if ((e.key === 'userToken' || e.key === 'userData') && window.mobileMenuInstance) {
        window.mobileMenuInstance.updateAuthButtons();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileMenu;
}
