// WhatsApp İletişim Sistemi JavaScript

// Global değişkenler
let whatsappModal = null;
const phoneNumber = '905347965682';

// Modal açma fonksiyonu
function openWhatsAppModal() {
    console.log('WhatsApp modal açılıyor...');

    if (!whatsappModal) {
        createWhatsAppModal();
    }

    whatsappModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus on first input
    setTimeout(() => {
        const nameInput = document.getElementById('whatsappName');
        if (nameInput) {
            nameInput.focus();
        }
    }, 300);

    // Auto-fill user info if logged in
    autoFillUserInfo();
}

// Modal oluşturma fonksiyonu
function createWhatsAppModal() {

    const modalHTML = `
            <div class="whatsapp-modal-overlay" id="whatsappModal">
                <div class="whatsapp-modal">
                    <div class="whatsapp-modal-header">
                        <h3 class="whatsapp-modal-title">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp ile İletişim
                        </h3>
                        <button class="whatsapp-modal-close" id="whatsappModalClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="whatsapp-modal-body">
                        <div class="whatsapp-info">
                            <i class="fas fa-info-circle"></i>
                            <div class="whatsapp-info-text">
                                Mesajınızı yazın ve WhatsApp üzerinden doğrudan bize ulaşın. 
                                Mesajınız otomatik olarak WhatsApp'ta açılacaktır.
                            </div>
                        </div>
                        <form id="whatsappForm">
                            <div class="whatsapp-form-group">
                                <label class="whatsapp-form-label" for="whatsappName">
                                    <i class="fas fa-user"></i> Adınız
                                </label>
                                <input 
                                    type="text" 
                                    id="whatsappName" 
                                    class="whatsapp-form-input" 
                                    placeholder="Adınızı girin"
                                    required
                                >
                            </div>
                            <div class="whatsapp-form-group">
                                <label class="whatsapp-form-label" for="whatsappSubject">
                                    <i class="fas fa-tag"></i> Konu
                                </label>
                                <input 
                                    type="text" 
                                    id="whatsappSubject" 
                                    class="whatsapp-form-input" 
                                    placeholder="Mesaj konusu"
                                    required
                                >
                            </div>
                            <div class="whatsapp-form-group">
                                <label class="whatsapp-form-label" for="whatsappMessage">
                                    <i class="fas fa-comment"></i> Mesajınız
                                </label>
                                <textarea 
                                    id="whatsappMessage" 
                                    class="whatsapp-form-textarea" 
                                    placeholder="Mesajınızı buraya yazın..."
                                    maxlength="500"
                                    required
                                ></textarea>
                                <div class="whatsapp-char-counter">
                                    <span id="charCount">0</span>/500 karakter
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="whatsapp-modal-footer">
                        <button type="button" class="whatsapp-btn whatsapp-btn-cancel" id="whatsappCancel">
                            İptal
                        </button>
                        <button type="submit" class="whatsapp-btn whatsapp-btn-send" id="whatsappSend">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp'ta Gönder
                        </button>
                    </div>
                </div>
            </div>
        `;

    // Modal'ı body'ye ekle
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    whatsappModal = document.getElementById('whatsappModal');

    // Event listeners ekle
    setupEventListeners();
}

// Event listeners kurulumu
function setupEventListeners() {

    // Modal close events
    document.getElementById('whatsappModalClose').addEventListener('click', closeWhatsAppModal);
    document.getElementById('whatsappCancel').addEventListener('click', closeWhatsAppModal);

    // Overlay click to close
    whatsappModal.addEventListener('click', (e) => {
        if (e.target === whatsappModal) {
            closeWhatsAppModal();
        }
    });

    // ESC key to close
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && whatsappModal && whatsappModal.classList.contains('active')) {
            closeWhatsAppModal();
        }
    });

    // Form submit
    document.getElementById('whatsappSend').addEventListener('click', (e) => {
        e.preventDefault();
        sendWhatsAppMessage();
    });

    // Character counter
    const messageTextarea = document.getElementById('whatsappMessage');
    const charCount = document.getElementById('charCount');

    messageTextarea.addEventListener('input', () => {
        const count = messageTextarea.value.length;
        charCount.textContent = count;

        if (count > 450) {
            charCount.style.color = '#ef4444';
        } else if (count > 400) {
            charCount.style.color = '#f59e0b';
        } else {
            charCount.style.color = '#64748b';
        }
    });
}

// Modal kapatma fonksiyonu
function closeWhatsAppModal() {
    if (whatsappModal) {
        whatsappModal.classList.remove('active');
        document.body.style.overflow = '';

        // Reset form
        setTimeout(() => {
            resetWhatsAppForm();
        }, 300);
    }
}

// Form sıfırlama fonksiyonu
function resetWhatsAppForm() {
    const form = document.getElementById('whatsappForm');
    const charCount = document.getElementById('charCount');

    if (form) form.reset();
    if (charCount) {
        charCount.textContent = '0';
        charCount.style.color = '#64748b';
    }
}

// Kullanıcı bilgilerini otomatik doldurma
function autoFillUserInfo() {
    const token = localStorage.getItem('token');
    if (token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            if (payload.firstName && payload.lastName) {
                const nameInput = document.getElementById('whatsappName');
                if (nameInput) {
                    nameInput.value = `${payload.firstName} ${payload.lastName}`;
                }
            }
        } catch (error) {
            console.log('Token parse error:', error);
        }
    }
}

// Form validasyonu
function validateWhatsAppForm() {
    const name = document.getElementById('whatsappName').value.trim();
    const subject = document.getElementById('whatsappSubject').value.trim();
    const message = document.getElementById('whatsappMessage').value.trim();

    if (!name || !subject || !message) {
        showNotification('Lütfen tüm alanları doldurun.', 'error');
        return false;
    }

    if (message.length < 10) {
        showNotification('Mesajınız en az 10 karakter olmalıdır.', 'error');
        return false;
    }

    return true;
}

// WhatsApp mesajı gönderme
function sendWhatsAppMessage() {
    if (!validateWhatsAppForm()) {
        return;
    }

    const name = document.getElementById('whatsappName').value.trim();
    const subject = document.getElementById('whatsappSubject').value.trim();
    const message = document.getElementById('whatsappMessage').value.trim();

    // WhatsApp mesaj formatı
    const whatsappMessage = `🔔 *${subject}*\n\n` +
                           `👤 *Ad:* ${name}\n` +
                           `📝 *Mesaj:* ${message}\n\n` +
                           `📅 *Tarih:* ${new Date().toLocaleString('tr-TR')}`;

    // WhatsApp URL oluştur
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(whatsappMessage)}`;

    // WhatsApp'ı aç
    window.open(whatsappUrl, '_blank');

    // Modal'ı kapat
    closeWhatsAppModal();

    // Başarı mesajı göster
    showNotification('WhatsApp açılıyor... Mesajınızı gönderebilirsiniz!', 'success');
}

// Bildirim gösterme fonksiyonu
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: 600;
        z-index: 10001;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        ${type === 'error' ? 'background: #ef4444;' : 'background: #10b981;'}
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Sayfa yüklendiğinde çalışacak fonksiyon
document.addEventListener('DOMContentLoaded', function() {
    console.log('WhatsApp Contact System loaded');
});

// Test fonksiyonu
function testWhatsApp() {
    console.log('WhatsApp test function called');
    openWhatsAppModal();
}
