// Test Database Connection

import { supabase } from '../../supabase-config.js';

const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
};

export async function handler(event, context) {
    // CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Environment variables kontrolü
        const envCheck = {
            SUPABASE_URL: !!process.env.SUPABASE_URL,
            SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY,
            SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
            SUPABASE_URL_VALUE: process.env.SUPABASE_URL?.substring(0, 30) + '...',
            SUPABASE_ANON_KEY_VALUE: process.env.SUPABASE_ANON_KEY?.substring(0, 30) + '...'
        };

        // Test 1: Admins tablosu
        const { data: admins, error: adminsError } = await supabase
            .from('admins')
            .select('id, email, is_active')
            .limit(5);

        // Test 2: Specific admin
        const { data: specificAdmin, error: specificError } = await supabase
            .from('admins')
            .select('*')
            .eq('email', '<EMAIL>');

        // Test 3: Users tablosu
        const { data: users, error: usersError } = await supabase
            .from('users')
            .select('id, email')
            .limit(3);

        // Test 4: Support categories
        const { data: categories, error: categoriesError } = await supabase
            .from('support_categories')
            .select('*')
            .limit(3);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                timestamp: new Date().toISOString(),
                environment: envCheck,
                tests: {
                    admins: {
                        success: !adminsError,
                        error: adminsError?.message,
                        count: admins?.length || 0,
                        data: admins
                    },
                    specificAdmin: {
                        success: !specificError,
                        error: specificError?.message,
                        count: specificAdmin?.length || 0,
                        data: specificAdmin
                    },
                    users: {
                        success: !usersError,
                        error: usersError?.message,
                        count: users?.length || 0,
                        data: users
                    },
                    supportCategories: {
                        success: !categoriesError,
                        error: categoriesError?.message,
                        count: categories?.length || 0,
                        data: categories
                    }
                }
            })
        };

    } catch (error) {
        console.error('Database connection test error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Test failed',
                details: error.message,
                stack: error.stack
            })
        };
    }
}
