/* Modern Mobile Menu Styles */

/* Mobile Menu Toggle - Enhanced */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.75rem;
    min-width: 48px;
    min-height: 48px;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1001;
    -webkit-tap-highlight-color: transparent;
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
}

.mobile-menu-toggle:active {
    background: var(--gray-200);
    transform: scale(0.95);
}

.mobile-menu-toggle i {
    font-size: 1.5rem;
    color: var(--gray-700);
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover i {
    color: var(--primary-color);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Menu Panel */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    max-width: 85vw;
    height: 100vh;
    background: var(--white);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    display: flex;
    flex-direction: column;
}

.mobile-menu.active {
    right: 0;
}

/* Mobile Menu Header */
.mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.mobile-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.mobile-brand i {
    font-size: 1.5rem;
}

.mobile-menu-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
}

.mobile-menu-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.mobile-menu-close i {
    font-size: 1.25rem;
}

/* Mobile Menu Content */
.mobile-menu-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

/* Mobile Navigation Links */
.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    padding: 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    font-size: 1rem;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
    background: var(--gray-50);
    color: var(--primary-color);
    transform: translateX(4px);
}

.mobile-nav-link i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
    color: var(--gray-500);
    transition: color 0.3s ease;
}

.mobile-nav-link:hover i,
.mobile-nav-link:focus i {
    color: var(--primary-color);
}

/* Mobile User Section */
.mobile-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    margin-bottom: 1rem;
}

.mobile-user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.mobile-user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.mobile-user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 1rem;
}

.mobile-user-status {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Mobile Auth Buttons */
.mobile-auth-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: auto;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.mobile-auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}

.mobile-auth-btn.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.mobile-auth-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.mobile-auth-btn.secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.mobile-auth-btn.secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Logout Button Special Styling */
.mobile-nav-link.logout-btn {
    color: #dc2626;
    margin-top: 1rem;
    border-top: 1px solid var(--gray-200);
    padding-top: 1.5rem;
}

.mobile-nav-link.logout-btn:hover {
    background: #fef2f2;
    color: #dc2626;
}

.mobile-nav-link.logout-btn i {
    color: #dc2626;
}

/* Mobile Navigation Sections */
.mobile-nav-section {
    margin-bottom: 1rem;
}

.mobile-nav-section:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu {
        width: 100vw;
        max-width: 100vw;
    }
}

@media (max-width: 480px) {
    .mobile-menu-content {
        padding: 1rem;
    }
    
    .mobile-menu-header {
        padding: 1rem;
    }
    
    .mobile-nav-link {
        padding: 0.875rem;
        font-size: 0.95rem;
    }
    
    .mobile-auth-btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }
}
