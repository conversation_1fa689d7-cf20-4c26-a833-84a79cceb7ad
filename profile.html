<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil - <PERSON><PERSON><PERSON> Ö<PERSON>tle<PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="whatsapp-contact.css">
    <style>
        /* Modern Corporate Profile Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #4f46e5;
            --primary-dark: #4338ca;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
            min-height: 100vh;
            color: var(--gray-900);
            line-height: 1.6;
        }

        /* Navigation */
        .navbar {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-brand i {
            font-size: 1.5rem;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .nav-link {
            color: var(--gray-600);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            color: var(--gray-700);
        }

        .nav-btn:hover {
            background: var(--gray-100);
            border-color: var(--gray-300);
        }

        /* User Menu Dropdown */
        .user-menu-dropdown {
            position: absolute;
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: 0.5rem;
            box-shadow: var(--shadow-lg);
            min-width: 200px;
            z-index: 1000;
        }

        .user-menu-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
            color: var(--gray-700);
        }

        .user-menu-item:hover {
            background: var(--gray-50);
        }

        .user-menu-divider {
            height: 1px;
            background: var(--gray-200);
            margin: 0.25rem 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-balance-scale"></i>
                <span>Hukuki Belge Özetleme</span>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">Ana Sayfa</a>
                <button class="nav-btn" id="user-menu-btn">
                    <i class="fas fa-user"></i>
                    <span id="user-name"></span>
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="max-width: 1200px; margin: 0 auto; padding: 2rem 1.5rem;">
        <!-- Profile Header -->
        <div style="background: var(--white); border-radius: 1rem; padding: 2rem; margin-bottom: 2rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
            <div style="display: flex; align-items: center; gap: 1.5rem; margin-bottom: 1.5rem;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <h1 style="font-size: 2rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;">Kullanıcı Profili</h1>
                    <p style="color: var(--gray-600); font-size: 1.1rem;">Hesap bilgilerinizi görüntüleyin ve yönetin</p>
                </div>
            </div>

            <!-- Status Messages -->
            <div id="account-status-messages" style="display: none;"></div>
        </div>

        <!-- Profile Grid -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
            <!-- Personal Information Card -->
            <div style="background: var(--white); border-radius: 1rem; padding: 1.5rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--gray-200);">
                    <div style="width: 40px; height: 40px; background: var(--primary-color); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900);">Kişisel Bilgiler</h2>
                </div>

                <div style="display: flex; flex-direction: column; gap: 1rem;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Ad</label>
                            <span id="profile-first-name" style="font-weight: 600; color: var(--gray-900);">-</span>
                        </div>
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Soyad</label>
                            <span id="profile-last-name" style="font-weight: 600; color: var(--gray-900);">-</span>
                        </div>
                    </div>

                    <div>
                        <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">E-posta</label>
                        <span id="profile-email" style="font-weight: 600; color: var(--gray-900);">-</span>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Telefon</label>
                            <span id="profile-phone" style="font-weight: 600; color: var(--gray-900);">-</span>
                        </div>
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Meslek/Ünvan</label>
                            <span id="profile-profession" style="font-weight: 600; color: var(--gray-900);">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Status Card -->
            <div style="background: var(--white); border-radius: 1rem; padding: 1.5rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--gray-200);">
                    <div style="width: 40px; height: 40px; background: var(--success-color); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-shield-check"></i>
                    </div>
                    <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900);">Hesap Durumu</h2>
                </div>

                <div style="display: flex; flex-direction: column; gap: 1rem;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Hesap Durumu</label>
                            <span id="profile-status" style="font-weight: 600;">-</span>
                        </div>
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">E-posta Durumu</label>
                            <span id="profile-email-status" style="font-weight: 600;">-</span>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Kayıt Tarihi</label>
                            <span id="profile-created-at" style="font-weight: 600; color: var(--gray-900);">-</span>
                        </div>
                        <div>
                            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-600); margin-bottom: 0.25rem;">Son Giriş</label>
                            <span id="profile-last-login" style="font-weight: 600; color: var(--gray-900);">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics Card -->
        <div style="background: var(--white); border-radius: 1rem; padding: 1.5rem; margin-bottom: 2rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--gray-200);">
                <div style="width: 40px; height: 40px; background: var(--warning-color); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900);">Günlük Özet Limiti</h2>
            </div>

            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; margin-bottom: 1.5rem;">
                <div style="text-align: center; padding: 1rem; background: var(--gray-50); border-radius: 0.75rem; border: 1px solid var(--gray-200);">
                    <div id="daily-limit" style="font-size: 2rem; font-weight: 700; color: var(--primary-color); margin-bottom: 0.25rem;">-</div>
                    <div style="font-size: 0.875rem; color: var(--gray-600); font-weight: 500;">Günlük Limit</div>
                </div>
                <div style="text-align: center; padding: 1rem; background: var(--gray-50); border-radius: 0.75rem; border: 1px solid var(--gray-200);">
                    <div id="used-today" style="font-size: 2rem; font-weight: 700; color: var(--warning-color); margin-bottom: 0.25rem;">-</div>
                    <div style="font-size: 0.875rem; color: var(--gray-600); font-weight: 500;">Bugün Kullanılan</div>
                </div>
                <div style="text-align: center; padding: 1rem; background: var(--gray-50); border-radius: 0.75rem; border: 1px solid var(--gray-200);">
                    <div id="remaining-today" style="font-size: 2rem; font-weight: 700; color: var(--success-color); margin-bottom: 0.25rem;">-</div>
                    <div style="font-size: 0.875rem; color: var(--gray-600); font-weight: 500;">Kalan Hak</div>
                </div>
            </div>

            <div style="margin-bottom: 0.75rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                    <span style="font-size: 0.875rem; font-weight: 500; color: var(--gray-600);">Kullanım Oranı</span>
                    <span id="usage-percentage" style="font-size: 0.875rem; font-weight: 600; color: var(--gray-900);">0%</span>
                </div>
                <div style="width: 100%; height: 8px; background: var(--gray-200); border-radius: 4px; overflow: hidden;">
                    <div id="usage-progress" style="height: 100%; background: var(--primary-color); border-radius: 4px; transition: width 0.3s ease; width: 0%;"></div>
                </div>
            </div>
        </div>

        <!-- Usage Purpose Card -->
        <div style="background: var(--white); border-radius: 1rem; padding: 1.5rem; margin-bottom: 2rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--gray-200);">
                <div style="width: 40px; height: 40px; background: var(--secondary-color); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-comment"></i>
                </div>
                <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900);">Kullanım Amacı</h2>
            </div>

            <div style="background: var(--gray-50); border-radius: 0.75rem; padding: 1rem; border: 1px solid var(--gray-200);">
                <p id="profile-reason" style="color: var(--gray-700); line-height: 1.6; margin: 0;">-</p>
            </div>
        </div>

        <!-- Support Center Card -->
        <div style="background: var(--white); border-radius: 1rem; padding: 1.5rem; margin-bottom: 2rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--gray-200);">
                <div style="width: 40px; height: 40px; background: #667eea; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-headset"></i>
                </div>
                <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900);">Destek Merkezi</h2>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 0.75rem; padding: 1.25rem; color: white; text-align: center; cursor: pointer; transition: transform 0.2s;" onclick="window.location.href='/support'">
                    <i class="fas fa-plus" style="font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                    <div style="font-weight: 600; margin-bottom: 0.25rem;">Yeni Destek Talebi</div>
                    <div style="font-size: 0.875rem; opacity: 0.9;">Sorun bildirin</div>
                </div>
                <div style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); border-radius: 0.75rem; padding: 1.25rem; color: white; text-align: center; cursor: pointer; transition: transform 0.2s;" onclick="window.location.href='/support-tickets'">
                    <i class="fas fa-ticket-alt" style="font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                    <div style="font-weight: 600; margin-bottom: 0.25rem;">Destek Kayıtlarım</div>
                    <div style="font-size: 0.875rem; opacity: 0.9;" id="support-tickets-count">Taleplerinizi görüntüleyin</div>
                </div>
            </div>

            <div style="background: var(--gray-50); border-radius: 0.75rem; padding: 1rem; border: 1px solid var(--gray-200);">
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <i class="fas fa-info-circle" style="color: #667eea;"></i>
                    <span style="font-weight: 600; color: var(--gray-900); font-size: 0.875rem;">Destek Hakkında</span>
                </div>
                <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5; margin: 0;">
                    Teknik sorunlar, hesap yönetimi, özellik talepleri ve genel sorularınız için destek ekibimizle iletişime geçebilirsiniz.
                    Ortalama yanıt süremiz 24 saattir.
                </p>
            </div>
        </div>

        <!-- Account Actions Card -->
        <div style="background: var(--white); border-radius: 1rem; padding: 1.5rem; margin-bottom: 2rem; box-shadow: var(--shadow-md); border: 1px solid var(--gray-200);">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--gray-200);">
                <div style="width: 40px; height: 40px; background: var(--gray-600); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-cog"></i>
                </div>
                <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900);">Hesap İşlemleri</h2>
            </div>

            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button class="whatsapp-contact-btn" onclick="openWhatsAppModal()">
                    <i class="fab fa-whatsapp"></i>
                    WhatsApp ile İletişim
                </button>
                <button onclick="goHome()" style="display: flex; align-items: center; gap: 0.5rem; background: var(--gray-100); color: var(--gray-700); border: 1px solid var(--gray-300); padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 600; cursor: pointer; transition: all 0.2s; text-decoration: none;">
                    <i class="fas fa-home"></i>
                    Ana Sayfaya Dön
                </button>
                <button onclick="logout()" style="display: flex; align-items: center; gap: 0.5rem; background: var(--danger-color); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 600; cursor: pointer; transition: all 0.2s;">
                    <i class="fas fa-sign-out-alt"></i>
                    Çıkış Yap
                </button>
            </div>
        </div>
    </main>

    <!-- User Menu Dropdown -->
    <div class="user-menu-dropdown" id="user-menu-dropdown" style="display: none;">
        <div class="user-menu-item" onclick="goToProfile()">
            <i class="fas fa-user"></i>
            <span>Profil</span>
        </div>
        <div class="user-menu-divider"></div>
        <div class="user-menu-item" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            <span>Çıkış Yap</span>
        </div>
    </div>

    <!-- Status Badge Styles -->
    <style>
        /* Status Badge Styles */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge.active {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.suspended {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-badge.verified {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.not-verified {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-badge.demo {
            background: #e0e7ff;
            color: #3730a3;
        }

        .status-badge.unknown {
            background: #f3f4f6;
            color: #374151;
        }

        /* Status Messages */
        .status-message {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }

        .status-message.success {
            background: #f0fdf4;
            border-left-color: #22c55e;
        }

        .status-message.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }

        .status-message.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }

        .status-message.info {
            background: #eff6ff;
            border-left-color: #3b82f6;
        }

        .message-icon {
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .message-title {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }

        .message-text {
            color: var(--gray-700);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            main {
                padding: 1rem !important;
            }

            .nav-container {
                padding: 0 1rem !important;
            }

            div[style*="grid-template-columns: 1fr 1fr"] {
                grid-template-columns: 1fr !important;
            }

            div[style*="grid-template-columns: repeat(3, 1fr)"] {
                grid-template-columns: 1fr !important;
            }

            div[style*="display: flex; gap: 1rem; justify-content: center"] {
                flex-direction: column;
            }

            .nav-brand span {
                display: none;
            }
        }

        /* Hover Effects */
        button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        button:active {
            transform: translateY(0);
        }
    </style>

    <script src="profile.js"></script>
    <script src="whatsapp-contact.js"></script>
</body>
</html>
