// Admin <PERSON>ini Supabase'de Güncelle
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

// Environment variables'ı kontrol et
console.log('🔍 Environment variables kontrol ediliyor...');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Mevcut' : '❌ Eksik');
console.log('SUPABASE_SERVICE_KEY:', process.env.SUPABASE_SERVICE_KEY ? '✅ Mevcut' : '❌ Eksik');

// Eğer env variables yoksa, manuel olarak girebilirsiniz
const supabaseUrl = process.env.SUPABASE_URL || 'YOUR_SUPABASE_URL_HERE';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || 'YOUR_SERVICE_KEY_HERE';

if (supabaseUrl === 'YOUR_SUPABASE_URL_HERE' || supabaseServiceKey === 'YOUR_SERVICE_KEY_HERE') {
    console.log('\n❌ Supabase bilgileri eksik!');
    console.log('Lütfen environment variables\'ı ayarlayın veya bu dosyada manuel olarak girin.');
    process.exit(1);
}

// Supabase client oluştur
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateAdminPassword() {
    try {
        console.log('\n🔐 Admin şifresi güncelleniyor...');
        
        // Yeni şifre hash'i
        const newPassword = '8454854';
        const newHash = '$2a$10$34yo5taxnj4nuzzOEDg8yuVRfFvOz675lEvNmKtgMxRZesTQ0DUpG';
        
        // Önce mevcut admin'i kontrol et
        console.log('👤 Mevcut admin bilgileri kontrol ediliyor...');
        const { data: currentAdmin, error: fetchError } = await supabase
            .from('admins')
            .select('id, email, full_name, role, is_active')
            .eq('email', '<EMAIL>')
            .single();
            
        if (fetchError) {
            console.error('❌ Admin bulunamadı:', fetchError.message);
            return;
        }
        
        console.log('✅ Admin bulundu:', {
            id: currentAdmin.id,
            email: currentAdmin.email,
            name: currentAdmin.full_name,
            role: currentAdmin.role,
            active: currentAdmin.is_active
        });
        
        // Şifreyi güncelle
        console.log('\n🔄 Şifre güncelleniyor...');
        const { data: updateResult, error: updateError } = await supabase
            .from('admins')
            .update({
                password_hash: newHash,
                updated_at: new Date().toISOString(),
                failed_login_attempts: 0,
                locked_until: null
            })
            .eq('email', '<EMAIL>')
            .select();
            
        if (updateError) {
            console.error('❌ Şifre güncellenemedi:', updateError.message);
            return;
        }
        
        console.log('✅ Şifre başarıyla güncellendi!');
        console.log('📧 E-posta: <EMAIL>');
        console.log('🔑 Yeni şifre: 8454854');
        
        // Test et
        console.log('\n🧪 Yeni şifre test ediliyor...');
        const testResult = await bcrypt.compare(newPassword, newHash);
        console.log('Hash testi:', testResult ? '✅ Başarılı' : '❌ Başarısız');
        
        console.log('\n🎉 İşlem tamamlandı!');
        console.log('Artık admin paneline şu bilgilerle giriş yapabilirsiniz:');
        console.log('E-posta: <EMAIL>');
        console.log('Şifre: 8454854');
        
    } catch (error) {
        console.error('❌ Genel hata:', error.message);
    }
}

updateAdminPassword();
