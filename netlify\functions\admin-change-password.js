// Admin Ş<PERSON>rme Function'ı
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);



exports.handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Token gerekli.' })
            };
        }

        const token = authHeader.substring(7);
        // JWT token kontrolü (basit)
        let adminData;
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            console.log('JWT decoded successfully:', decoded.email);

            // Admin bilgilerini Supabase'den getir
            const { data: admin, error: adminError } = await supabase
                .from('admins')
                .select('*')
                .eq('id', decoded.adminId)
                .eq('is_active', true)
                .single();

            if (adminError || !admin) {
                console.log('Admin not found:', adminError);
                return {
                    statusCode: 401,
                    headers,
                    body: JSON.stringify({ error: 'Admin bulunamadı.' })
                };
            }

            adminData = admin;
            console.log('Admin found:', admin.email);

        } catch (error) {
            console.error('JWT verification failed:', error.message);
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        const body = JSON.parse(event.body || '{}');
        const { currentPassword, newPassword } = body;

        if (!currentPassword || !newPassword) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Mevcut şifre ve yeni şifre gereklidir.' })
            };
        }

        if (newPassword.length < 6) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Yeni şifre en az 6 karakter olmalıdır.' })
            };
        }

        // Mevcut şifreyi kontrol et
        console.log('Checking current password for admin:', adminData.email);
        console.log('Current password hash info:', {
            length: adminData.password_hash ? adminData.password_hash.length : 0,
            start: adminData.password_hash ? adminData.password_hash.substring(0, 20) : 'NULL'
        });

        const passwordMatch = await bcrypt.compare(currentPassword, adminData.password_hash);
        console.log('Password match result:', passwordMatch);

        if (!passwordMatch) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({
                    error: 'Mevcut şifre yanlış.',
                    debug: {
                        hash_length: adminData.password_hash ? adminData.password_hash.length : 0,
                        input_password: currentPassword
                    }
                })
            };
        }

        // Yeni şifreyi hash'le
        console.log('Hashing new password...');
        const newPasswordHash = await bcrypt.hash(newPassword, 10);
        console.log('New password hash created, length:', newPasswordHash.length);

        // Şifreyi güncelle
        console.log('Updating password for admin ID:', adminData.id);
        const { error: updateError } = await supabase
            .from('admins')
            .update({
                password_hash: newPasswordHash,
                updated_at: new Date().toISOString()
            })
            .eq('id', adminData.id);

        console.log('Password update result:', { updateError });

        if (updateError) {
            console.error('Password update error:', updateError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Şifre güncellenirken hata oluştu.',
                    details: updateError.message
                })
            };
        }

        // Log ekle
        await supabase.from('admin_logs').insert([{
            admin_username: adminData.email,
            admin_email: adminData.email,
            action: 'password_changed',
            details: {
                admin_id: adminData.id,
                timestamp: new Date().toISOString()
            }
        }]).catch(err => console.error('Log insert error:', err));

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true,
                message: 'Şifre başarıyla değiştirildi.'
            })
        };

    } catch (error) {
        console.error('Change password error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Şifre değiştirme işlemi sırasında hata oluştu.',
                details: error.message 
            })
        };
    }
};
