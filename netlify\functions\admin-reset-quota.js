const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Environment variables kontrolü
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('Missing environment variables:', {
        SUPABASE_URL: !!supabaseUrl,
        SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
        SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY
    });
}

const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
    // CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    };

    // Environment variables kontrolü
    if (!supabaseUrl || !supabaseKey) {
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Server configuration error',
                details: 'Missing required environment variables'
            })
        };
    }

    // Handle preflight request
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Admin token kontrolü
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Admin token gerekli.' })
            };
        }

        const token = authHeader.substring(7);

        // JWT token doğrulama
        let adminData;
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            adminData = {
                id: decoded.adminId,
                email: decoded.email
            };
        } catch (error) {
            return {
                statusCode: 401,
                headers,
                body: JSON.stringify({ error: 'Geçersiz token.' })
            };
        }

        // Request body parse
        let requestBody;
        try {
            requestBody = JSON.parse(event.body || '{}');
        } catch (error) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Geçersiz JSON' })
            };
        }

        const { userId } = requestBody;

        if (!userId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Kullanıcı ID gerekli' })
            };
        }

        console.log('Resetting quota for user:', userId);

        // Önce kullanıcının var olup olmadığını kontrol et
        const { data: userCheck, error: userCheckError } = await supabase
            .from('users')
            .select('id, daily_summary_count, daily_summary_limit, last_summary_date')
            .eq('id', userId)
            .single();

        if (userCheckError) {
            console.error('User check error:', userCheckError);
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({
                    error: 'Kullanıcı bulunamadı',
                    details: userCheckError.message
                })
            };
        }

        console.log('User found:', userCheck);

        // Kullanıcının günlük özet sayısını sıfırla
        const { data: resetData, error: resetError } = await supabase
            .from('users')
            .update({
                daily_summary_count: 0,
                last_summary_date: new Date().toISOString().split('T')[0] // YYYY-MM-DD format
            })
            .eq('id', userId)
            .select();

        if (resetError) {
            console.error('Reset quota error:', resetError);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Hak sıfırlama işlemi başarısız oldu',
                    details: resetError.message
                })
            };
        }

        console.log('Reset successful:', resetData);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Kullanıcının günlük özet hakkı başarıyla sıfırlandı',
                data: resetData
            })
        };

    } catch (error) {
        console.error('Admin reset quota error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Sunucu hatası',
                details: error.message
            })
        };
    }
};
